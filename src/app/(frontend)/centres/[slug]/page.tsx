import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { processMetadata } from '@/lib/utils';
import { sanityFetch } from '@/sanity/lib/live';
import { PageBuilder } from '@/components/page-builder';
import { CenterHero } from '@/components/centers/center-hero';
import { CenterInfo } from '@/components/centers/center-info';
import { centerBySlugQuery, centerSlugsQuery } from '@/sanity/lib/queries/documents/center';

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateStaticParams() {
  const { data } = await sanityFetch({
    query: centerSlugsQuery,
    perspective: "published",
    stega: false,
  });
  return data;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { data: center } = await sanityFetch({
    query: centerBySlugQuery,
    params: await params,
    stega: false,
  });

  if (!center) { return {} };

  return processMetadata({ data: center });
}

export default async function CenterPage({ params }: PageProps) {
  const { data: center } = await sanityFetch({ 
    query: centerBySlugQuery, 
    params: await params
  });
  
  if (center === null) notFound();

  const hasPageBuilder = center.pageBuilder && center.pageBuilder.length > 0;

  return (
    <main>
      {/* Center Hero */}
      <CenterHero center={center} />

      {/* Page Builder Content */}
      {hasPageBuilder ? (
        <PageBuilder
          id={center._id}
          type="center"
          pageBuilder={center.pageBuilder}
        />
      ) : (
        /* Fallback content if no page builder content */
        <section id="info" className="py-12">
          <div className="container mx-auto px-4">
            <CenterInfo center={center} />
          </div>
        </section>
      )}
    </main>
  );
}
