import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import {PageQueryResult, processMetadata} from '@/lib/utils';
import { sanityFetch } from '@/sanity/lib/live';
import { PageBuilder } from '@/components/page-builder';
import {
  allPagesWithHierarchyQuery,
  pageByNestedSlugQuery
} from '@/sanity/lib/queries/documents/page';
import {
  buildAllPagePaths,
  findPageByPath,
  parseSlugSegments,
  type PageHierarchy
} from '@/lib/page-utils';

interface PageProps {
  params: Promise<{ slug: string[] }>;
}

export async function generateStaticParams() {
  const { data: pages } = await sanityFetch({
    query: allPagesWithHierarchyQuery,
    perspective: "published",
    stega: false,
  });

  // Build all possible nested paths
  return buildAllPagePaths(pages as PageHierarchy[]);
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const lastSlug = parseSlugSegments(slug);

  if (!lastSlug) return {};

  const { data: pages } = await sanityFetch({
    query: pageByNestedSlugQuery,
    params: { slug: lastSlug },
    stega: false,
  });

  if (!pages || pages.length === 0) return {};

  // Find the page that matches the full path
  const page = findPageByPath(pages, slug);

  if (!page) return {};

  return processMetadata({ data: page as unknown as PageQueryResult });
}

export default async function Page({ params }: PageProps) {
  const { slug } = await params;
  const lastSlug = parseSlugSegments(slug);

  if (!lastSlug) notFound();

  const { data: pages } = await sanityFetch({
    query: pageByNestedSlugQuery,
    params: { slug: lastSlug },
  });
  console.log(pages);
  if (!pages || pages.length === 0) notFound();

  // Find the page that matches the full path
  const page = findPageByPath(pages, slug);

  if (!page) notFound();

  return (
    <PageBuilder
      id={page._id}
      type="page"
      pageBuilder={page.pageBuilder ?? []}
      inGrid={false}
    />
  );
}