import React from 'react';
import { cn } from '@/lib/utils';
import { ButtonType } from '@/types';
import { Button } from '../ui/button';
import { stegaClean } from 'next-sanity';
import { motion } from 'motion/react';

export default function ButtonRenderer({ buttons, classNames }: {
  buttons: ButtonType[];
  classNames?: string;
}) {

  const MotionButton = motion(Button)

  if (!buttons?.length) return null;

  return (
    <div className={cn('flex items-center gap-3 list-none', classNames)}>
      {buttons.map((button) => (
          <div
              key={`button-${button?._key}`}
          >
            <MotionButton
                initial={{
                  backgroundColor: 'transparent',
                  borderColor: 'transparent',
                  color: 'currentColor'
                }}
                whileHover={{
                  backgroundColor: `var(--${button?.buttonVariant ? (button?.buttonVariant + '-hover') : 'primary-hover'})`,
                  borderColor: '--border',
                  color: `var(--${button?.buttonVariant ? (button.buttonVariant + '-foreground') : 'primary-foreground'})`,
                }}
                animate={{
                  // Let the Button's variant determine final styles
                  backgroundColor: `var(--${button?.buttonVariant ?? 'primary'})`,
                  borderColor: '--border',
                  color: `var(--${button?.buttonVariant ? (button.buttonVariant + '-foreground') : 'primary-foreground'})`,
                }}
                transition={{ duration: 0.2, ease: 'easeInOut' }}
                variant={stegaClean(button?.buttonVariant) ?? 'primary'}
                buttonType={stegaClean(button?.buttonType) ?? 'external'}
                width={stegaClean(button?.buttonWidth) ?? 'auto'}
                pageReference={stegaClean(button?.buttonPageReference) ?? null}
                externalUrl={stegaClean(button?.buttonExternalUrl) ?? ''}
                emailAddress={stegaClean(button?.buttonEmailAddress) ?? ''}
                fileUrl={stegaClean(button?.buttonFileUrl)?.asset?.url}
                anchorLocation={stegaClean(button?.buttonAnchorLocation) ?? 'currentPage'}
                anchorId={stegaClean(button?.buttonAnchorId) ?? ''}
                className={cn('w-auto transition-all duration-100 ease-in-out', {
                  'w-full': stegaClean(button?.buttonWidth) === 'fullWidth'
                })}
            >
              {button?.buttonText}
            </MotionButton>
            </div>
      ))}
    </div>
  )
}