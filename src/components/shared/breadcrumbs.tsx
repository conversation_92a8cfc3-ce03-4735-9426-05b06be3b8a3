import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { buildBreadcrumbs, type PageHierarchy } from '@/lib/page-utils';

interface BreadcrumbsProps {
  page: PageHierarchy;
  className?: string;
  showHome?: boolean;
}

export function Breadcrumbs({ page, className = '', showHome = true }: BreadcrumbsProps) {
  const breadcrumbs = buildBreadcrumbs(page);

  if (breadcrumbs.length <= 1 && !showHome) {
    return null;
  }

  return (
    <nav className={`flex items-center space-x-2 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {showHome && (
          <>
            <li>
              <Link
                href="/"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Home"
              >
                <Home className="w-4 h-4" />
              </Link>
            </li>
            {breadcrumbs.length > 0 && (
              <li>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </li>
            )}
          </>
        )}

        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.href} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mr-2" />
            )}

            {breadcrumb.isActive ? (
              <span className="text-gray-900 font-medium" aria-current="page">
                {breadcrumb.title}
              </span>
            ) : (
              <Link
                href={breadcrumb.href}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {breadcrumb.title}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
