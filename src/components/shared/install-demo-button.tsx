"use client"
import { Copy } from "lucide-react";
import { Toaster } from "react-hot-toast";
import { copyToClipboard } from "@/lib/utils";
import Heading from "./heading";
import {EdgeBlur} from "@/components/shared/edge-blur";

export default function InstallDemoButton() {
  
  function handleClick() {
    copyToClipboard('npx sanity dataset import demo-content.tar.gz production')
  }

  return (
    <div className="flex flex-col items-center gap-6">
      <Heading tag="h2" size="xxl" className='max-w-[420px] relative pr-2.5 py-3 text-balance leading-normal border-y border-dashed border-t-gray-200 border-b-border-200 bg-card pattern-bg'>
        <span className='relative z-20 px-4'>
          SiteEngine
        </span>
        <EdgeBlur />
      </Heading>
      <button 
        onClick={() => handleClick()} 
        className="flex flex-col items-center gap-6 text-base cursor-pointer hover:opacity-80 transition-opacity"
      >
        Run the command to install demo content <span className="flex items-center gap-2 text-lg bg-foreground text-foreground px-3 py-1 rounded-lg">npx sanity dataset import demo-content.tar.gz production <Copy size={16} /></span>
      </button>
      <Toaster 
        position="bottom-right" 
        toastOptions={{
          className: 'text-sm font-semibold antialiased',
          style: {
            borderRadius: '300px',
            padding: '4px 8px',
            color: '#FFFFFF',
            fontWeight: '500',
            backgroundColor: '#000000'
          }
        }}
      />
    </div>
  )
}
