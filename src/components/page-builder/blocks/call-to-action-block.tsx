import { PageBuilderType } from '@/types';
import Heading from '@/components/shared/heading';
import Container from '@/components/global/container';
import ButtonRenderer from '@/components/shared/button-renderer';
import PortableTextEditor from '@/components/portable-text/portable-text-editor';

export type CallToActionBlockProps = PageBuilderType<"callToActionBlock">;

export default function CallToActionBlock(props: CallToActionBlockProps) {

  const { heading, content, buttons, anchorId } = props;

  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})}
      className='xl:px-10 pattern-bg border-t border-t-border min-w-120'
    >
      <Container className='py-16 md:py-28 border-x border-dashed w-full'>
        <div className='flex flex-col lg:flex-row lg:items-center justify-between gap-x-16'>
          <div>
            <Heading tag="h2" size="xl" className='max-w-160 text-balance leading-tight'>
              {heading}
            </Heading>
            <PortableTextEditor 
              data={content ?? []}
              classNames='mt-6 md:mt-8 text-balance text-muted-foreground'
            />
          </div>
          {buttons && buttons.length > 0 && (
            <div className='mt-10'>
              <ButtonRenderer buttons={buttons} />
            </div>
          )}
        </div>
      </Container>
    </section>
  )
}