import { cn } from '@/lib/utils';
import Heading from '@/components/shared/heading';
import Container from '@/components/global/container';
import PortableTextEditor from '@/components/portable-text/portable-text-editor';
import { motion } from 'motion/react';
import { CheckCircle, Clock, Play, XCircle } from 'lucide-react';

export type ProcessTimelinesBlockProps = {
  _key: string;
  _type: string;
  title?: string;
  subtitle?: string;
  description?: string;
  steps?: Array<{
    _key: string;
    title?: string;
    description?: string;
    status?: 'completed' | 'in-progress' | 'pending' | 'blocked';
    isMilestone?: boolean;
    duration?: string;
    content?: never[];
    customColor?: string;
  }>;
  timelineConfig?: {
    layout?: 'horizontal' | 'vertical' | 'centered' | 'simple';
    style?: 'default' | 'minimal' | 'cards' | 'activity';
    showConnectors?: boolean;
  };
  showOverallProgress?: boolean;
  accentColor?: {
    value: string;
  };
  backgroundColor?: {
    value: string;
  };
  anchorId?: string;
};

interface TimelineItemProps {
  step: {
    _key: string;
    title?: string;
    description?: string;
    status?: 'completed' | 'in-progress' | 'pending' | 'blocked';
    isMilestone?: boolean;
    duration?: string;
    content?: never[];
    customColor?: string;
  };
  index: number;
  total: number;
  accentColor?: { value: string };
  layout?: string;
  style?: string;
}

function TimelineItem({ step, index, accentColor }: TimelineItemProps) {
  const isCompleted = step.status === 'completed';
  const isInProgress = step.status === 'in-progress';
  const isBlocked = step.status === 'blocked';

  const getStatusIcon = () => {
    if (isCompleted) return <CheckCircle className="w-4 h-4" />;
    if (isInProgress) return <Play className="w-4 h-4" />;
    if (isBlocked) return <XCircle className="w-4 h-4" />;
    return <Clock className="w-4 h-4" />;
  };

  const getStatusColor = () => {
    if (accentColor?.value && isCompleted) return accentColor.value;
    if (isCompleted) return '#059669';   // emerald-600
    if (isInProgress) return '#d97706';  // amber-600
    if (isBlocked) return '#dc2626';     // rose-600
    return '#64748b';  // slate-500
  };

  const getStatusBgColor = () => {
    if (isCompleted) return 'bg-emerald-50 text-emerald-700';
    if (isInProgress) return 'bg-amber-50 text-amber-700';
    if (isBlocked) return 'bg-rose-50 text-rose-700';
    return 'bg-slate-50 text-slate-700';
  };

  return (
    <motion.div
      className="border border-dashed rounded-3xl overflow-hidden"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-10%" }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: [0.25, 0.1, 0.25, 1]
      }}
      whileHover={{ 
        y: -4,
        transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }
      }}
    >
      <div className="p-6 md:p-8 h-full">
        <div className="flex items-start gap-4 h-full">
          {/* Icon container matching design system */}
          <div className="shrink-0">
            <div 
              className="w-12 h-12 rounded-2xl border border-dashed bg-card flex items-center justify-center"
              style={{ 
                borderColor: getStatusColor(),
                color: getStatusColor()
              }}
            >
              {step.isMilestone ? (
                <div className="flex items-center justify-center w-5 h-5">
                  {getStatusIcon()}
                </div>
              ) : (
                <span className="text-lg font-bold">{index + 1}</span>
              )}
            </div>
          </div>

          {/* Content area */}
          <div className="flex-1 space-y-4">
            {/* Header */}
            <div className="space-y-2">
              <div className="flex items-start justify-between gap-4">
                <h3 className="text-lg font-bold text-card-foreground leading-tight">
                  {step.title}
                </h3>
                
                {step.duration && (
                  <span className="text-sm text-accent-foreground whitespace-nowrap font-medium">
                    {step.duration}
                  </span>
                )}
              </div>

              {/* Status badge */}
              <div className="flex items-center gap-2">
                <span 
                  className={cn(
                    'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-semibold',
                    getStatusBgColor()
                  )}
                >
                  <div className="flex items-center justify-center w-3 h-3">
                    {getStatusIcon()}
                  </div>
                  {step.status ? step.status.charAt(0).toUpperCase() + step.status.slice(1).replace('-', ' ') : 'Pending'}
                </span>
              </div>
            </div>

            {/* Description */}
            {step.description && (
              <p className="text-muted-foreground leading-relaxed text-sm">
                {step.description}
              </p>
            )}

            {/* Rich content */}
            {step.content && step.content.length > 0 && (
              <div className="border-t border-dashed pt-4">
                <PortableTextEditor 
                  data={step.content}
                  classNames="text-sm text-muted-foreground prose-sm prose-headings:text-card-foreground prose-headings:font-semibold prose-headings:text-sm prose-headings:mb-2 prose-p:mb-2 prose-ul:mb-2 prose-li:mb-1"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default function ProcessTimelinesBlock(props: ProcessTimelinesBlockProps) {
  const { 
    title, 
    subtitle, 
    description, 
    steps, 
    timelineConfig, 
    showOverallProgress, 
    accentColor, 
    backgroundColor,
    anchorId 
  } = props;

  const completedSteps = steps?.filter(step => step.status === 'completed').length || 0;
  const totalSteps = steps?.length || 0;
  const progressPercentage = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  
  const layout = timelineConfig?.layout || 'vertical';

  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})} 
      className={cn('px-4 md:px-10 py-16 md:py-24', {
        'pattern-bg': !backgroundColor,
      })}
    >
      <Container className="space-y-12">
        {/* Header */}
        {(title || subtitle || description) && (
          <div
            className="text-center max-w-3xl mx-auto"
          >
            {title && (
              <Heading size="xl" tag="h2" className="mb-4">
                {title}
              </Heading>
            )}
            {subtitle && (
              <PortableTextEditor 
                data={[{
                  _type: 'block',
                  children: [{ _type: 'span', text: subtitle, marks: [] }],
                  style: 'normal'
                }]}
                classNames="text-lg text-muted-foreground leading-relaxed"
              />
            )}
            {description && (
              <PortableTextEditor 
                data={[{
                  _type: 'block',
                  children: [{ _type: 'span', text: description, marks: [] }],
                  style: 'normal'
                }]}
                classNames="text-muted-foreground leading-relaxed"
              />
            )}
          </div>
        )}

        {/* Overall Progress */}
        {showOverallProgress && totalSteps > 0 && (
          <motion.div 
            className="max-w-2xl mx-auto border border-dashed rounded-3xl"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="p-6 md:p-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span className="font-semibold">Progress</span>
                  <span className="font-bold">{completedSteps} of {totalSteps} completed</span>
                </div>
                
                <div className="relative w-full bg-card rounded-full h-3 overflow-hidden border border-dashed">
                  <motion.div 
                    className="h-full rounded-full"
                    style={{
                      backgroundColor: accentColor?.value || '#3b82f6'
                    }}
                    initial={{ width: 0 }}
                    whileInView={{ width: `${progressPercentage}%` }}
                    viewport={{ once: true }}
                    transition={{ duration: 1.5, delay: 0.5 }}
                  />
                </div>
                
                <div className="text-center">
                  <motion.span 
                    className="text-2xl font-bold text-card-foreground"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 1 }}
                  >
                    {Math.round(progressPercentage)}% Complete
                  </motion.span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Timeline - using the same grid pattern as feature cards */}
        {steps && steps.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
            {steps.map((step, index) => (
              <TimelineItem
                key={step._key}
                step={step}
                index={index}
                total={steps.length}
                accentColor={accentColor}
                layout={layout}
              />
            ))}
          </div>
        )}

        {/* Legend - matching the design system */}
        <motion.div 
          className="flex flex-wrap gap-6 justify-center text-sm text-muted-foreground border border-dashed rounded-3xl p-6 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-sm border border-dashed bg-emerald-50 flex items-center justify-center">
              <CheckCircle className="w-3 h-3 text-emerald-600" />
            </div>
            <span className="font-medium">Completed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-sm border border-dashed bg-amber-50 flex items-center justify-center">
              <Play className="w-3 h-3 text-amber-600" />
            </div>
            <span className="font-medium">In Progress</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-sm border border-dashed bg-slate-50 flex items-center justify-center">
              <Clock className="w-3 h-3 text-slate-600" />
            </div>
            <span className="font-medium">Pending</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-sm border border-dashed bg-rose-50 flex items-center justify-center">
              <XCircle className="w-3 h-3 text-rose-600" />
            </div>
            <span className="font-medium">Blocked</span>
          </div>
        </motion.div>
      </Container>
    </section>
  );
}
