import { cn } from '@/lib/utils';
import { PageBuilderType } from '@/types';
import Heading from '@/components/shared/heading';
import Container from '@/components/global/container';
import PortableTextEditor from '@/components/portable-text/portable-text-editor';
import { motion } from 'motion/react';

export type HeaderBlockProps = PageBuilderType<"headerBlock">;

export default function HeaderBlock(props: HeaderBlockProps) {

  const { heading, content, bottomCornerRadius, anchorId } = props;

  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})} 
      className={cn('px-4 md:px-10 pattern-bg border-b', {
        'rounded-4xl': bottomCornerRadius === 'rounded-sm'
      })}
    >
      <Container className='border-x border-dashed'>
        <div className='pt-36 md:pt-52 pb-20 md:pb-36'>
          <Heading tag="h1" size="xxl" className='text-balance leading-normal'>
            {heading}
          </Heading>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
          >
            <PortableTextEditor
              data={content ?? []}
              classNames='mt-6 md:mt-8 md:text-xl text-balance text-muted-foreground'
            />
          </motion.div>
        </div>
      </Container>
    </section>
  )
}
