import {GridItemType, GridLayoutBlockType} from "@/types";
import { PageBuilder } from "@/components/page-builder";
import { cn } from "@/lib/utils";

type GridLayoutBlockProps = GridLayoutBlockType

const spacingMap: Record<string, string> = {
  none: '',
  small: 'py-4',
  medium: 'py-8',
  large: 'py-16',
  xlarge: 'py-24'
};

const backgroundColorMap: Record<string, string> = {
  transparent: '',
  white: 'bg-background',
  gray: 'bg-card',
  dark: 'bg-gray-900',
  primary: 'bg-primary',
  secondary: 'bg-gray-600'
};

const containerMap: Record<string, string> = {
  full: 'w-full',
  contained: 'container mx-auto px-4'
};

function getGridClasses(gridConfig: GridLayoutBlockType["gridConfig"]) {
  if (!gridConfig) return 'grid gap-4 items-stretch';
  
  const { columns, gap } = gridConfig;
  
  // Grid columns classes
  const colClasses = {
    mobile: columns?.mobile ? `grid-cols-${columns.mobile}` : 'grid-cols-1',
    tablet: columns?.tablet ? `md:grid-cols-${columns.tablet}` : '',
    desktop: columns?.desktop ? `lg:grid-cols-${columns.desktop}` : ''
  };
  
  // Gap classes - gap is a simple string value, not responsive
  const gapClass = gap ? `gap-${gap}` : 'gap-4';

  return cn(
    'grid',
    'items-stretch', // This makes grid items stretch to fill their container height
    colClasses.mobile,
    colClasses.tablet,
    colClasses.desktop,
    gapClass,
  );
}

function getGridItemClasses(gridItemConfig: GridItemType["gridItemConfig"]) {
  if (!gridItemConfig) return '';
  
  const { columnSpan, rowSpan } = gridItemConfig;
  
  const classes = [];
  
  // Column span classes
  if (columnSpan?.mobile) classes.push(`col-span-${columnSpan.mobile}`);
  if (columnSpan?.tablet) classes.push(`md:col-span-${columnSpan.tablet}`);
  if (columnSpan?.desktop) classes.push(`lg:col-span-${columnSpan.desktop}`);
  
  // Row span classes
  if (rowSpan) classes.push(`row-span-${rowSpan}`);
  
  return classes.join(' ');
}

export default function GridLayoutBlock({
  displayTitle,
  publicTitle,
  description,
  gridConfig,
  gridStyling,
  gridItems,
  _id,
  _type
}: GridLayoutBlockProps) {
  const spacing = gridStyling?.spacing || 'medium';
  const backgroundColor = gridStyling?.backgroundColor || 'transparent';
  const containerStyle = gridStyling?.containerStyle || 'contained';
  
  const gridClasses = getGridClasses(gridConfig);
  
  return (
    <section
      className={cn(
        spacingMap[spacing],
        backgroundColorMap[backgroundColor]
      )}
    >
      <div className={containerMap[containerStyle]}>
        {displayTitle && publicTitle && (
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-card-foreground mb-4">{publicTitle}</h2>
            {description && (
              <p className="text-lg text-muted-foreground">{description}</p>
            )}
          </div>
        )}
        
        <div className={gridClasses}>
          {(gridItems as GridItemType[])?.map((gridItem, index) => {
            const gridItemClasses = getGridItemClasses(gridItem.gridItemConfig);
            
            // Each grid item should contain exactly one block
            const block = gridItem.block?.[0];
            if (!block) return (
                <div
                  key={index}
                  className={cn(gridItemClasses, 'grid-item-stretch')}
                >
                  <div className='h-full flex items-center justify-center bg-muted text-accent-foreground'>
                    Empty Grid Item
                  </div>
                </div>
            );

            return (
              <div
                key={`${block._key}-${index}`}
                className={cn(gridItemClasses, 'grid-item-stretch')}
              >
                <PageBuilder
                  pageBuilder={[block]}
                  id={_id || 'grid'}
                  type={_type || 'gridLayoutBlock'}
                  inGrid={true}
                />
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
