'use client'

import React, { useCallback, useEffect, useState, useRef } from 'react';
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';
import Heading from '@/components/shared/heading';
import Container from '@/components/global/container';
import PortableTextEditor from '@/components/portable-text/portable-text-editor';
import { Image as ImageIcon, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import {EmblaOptionsType, EmblaCarouselType} from 'embla-carousel'
import Autoplay from 'embla-carousel-autoplay';
import {CarouselBlockType, CarouselSlideType} from "@/types";
import {Button} from "@/components/ui/button";

type CarouselBlockProps = CarouselBlockType

type CarouselSlide = CarouselSlideType;

type CarouselConfig = CarouselBlockType["carouselConfig"];

// Default configuration
const defaultConfig: {
  autoplay: { enabled: boolean; delay: number; stopOnInteraction: boolean };
  emblaOptions: { align: string; loop: boolean; dragFree: boolean; slidesToScroll: number };
  slidesDisplay: { slidesPerView: { mobile: number; tablet: number; desktop: number }; slideSpacing: string };
  showNavigation: boolean;
  showDots: boolean
} = {
  autoplay: {
    enabled: false,
    delay: 4000,
    stopOnInteraction: true,
  },
  emblaOptions: {
    align: 'start',
    loop: true,
    dragFree: false,
    slidesToScroll: 1,
  },
  slidesDisplay: {
    slidesPerView: {
      mobile: 1,
      tablet: 2,
      desktop: 3,
    },
    slideSpacing: '1.5rem',
  },
  showNavigation: true,
  showDots: true,
};

// Video slide component
const VideoSlide: React.FC<{ slide: CarouselSlide, index: number }> = ({ slide }) => {
  const [isPlaying, setIsPlaying] = useState(false);

  return (
      <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-10%" }}
          transition={{
            duration: 0.6,
            delay: 0.1,
            ease: [0.25, 0.1, 0.25, 1]
          }}
          whileHover={{
            y: -4,
            transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }
          }}
          className="border border-dashed rounded-3xl overflow-hidden h-full"
      >
      <div className="relative aspect-video bg-foreground">
        {slide.video?.thumbnail?.asset?.url && !isPlaying && (
          <>
            <Image
              src={slide.video.thumbnail.asset.url}
              alt={slide.video.caption || 'Video thumbnail'}
              fill
              className="object-cover"
            />
            <button
              onClick={() => setIsPlaying(true)}
              className="absolute inset-0 flex items-center justify-center bg-background/20 backdrop-blur-lg  duration-300 transition-colors group"
            >
              <Play className="w-8 h-8 text-card-foreground ml-1" />
            </button>
          </>
        )}
        {slide.video?.videoFile?.asset?.url && isPlaying && (
          <video
            src={slide.video.videoFile.asset.url}
            controls
            autoPlay
            className="w-full h-full object-cover"
          />
        )}
      </div>
      {slide.video?.caption && (
        <div className="p-4">
          <p className="text-sm text-muted-foreground">
            {slide.video.caption}
          </p>
        </div>
      )}
    </motion.div>
  );
};

// Image slide component
const ImageSlide: React.FC<{ slide: CarouselSlide, index: number }> = ({ slide }) => (
    <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-10%" }}
        transition={{
          duration: 0.6,
          delay: 0.1,
          ease: [0.25, 0.1, 0.25, 1]
        }}
        whileHover={{
          y: -4,
          transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }
        }}
        className="border border-dashed rounded-3xl overflow-hidden h-full"
    >
    <div className="relative aspect-video">
      {slide.image?.asset?.url ? (
        <>
          <Image
            src={slide.image.asset.url}
            alt={slide.image.alt || slide.title || ''}
            fill
            className="object-cover hover:scale-105 transition-transform duration-500"
          />
        </>
      ) : (
        <div className="flex items-center justify-center h-full bg-card">
          <div className="text-center">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground font-medium">
              {slide.title || 'Add Image'}
            </p>
          </div>
        </div>
      )}
    </div>
    {slide.image?.caption && (
      <div className="p-4">
        <p className="text-sm text-muted-foreground">
          {slide.image.caption}
        </p>
      </div>
    )}
  </motion.div>
);

// Content card slide component
const CardSlide: React.FC<{ slide: CarouselSlide, index: number }> = ({ slide, index }) => (
  <motion.div
      initial={{ opacity: 0, x: -30 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, margin: "-10%" }}
      transition={{
        duration: 0.6,
        delay: index < 3 ? 0.2 * index : 0.2,
        ease: [0.25, 0.1, 0.25, 1]
      }}
      whileHover={{
        y: -4,
        transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }
      }}
      className="border border-dashed rounded-3xl overflow-hidden h-full flex flex-col group"
  >
    {slide.image?.asset?.url ? (
      <div className="aspect-video relative overflow-hidden">
        <Image
          src={slide.image.asset.url}
          alt={slide.image.alt || slide.title || ''}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-500"
        />
      </div>
    ) : (
      <div className="aspect-video relative overflow-hidden bg-card flex items-center justify-center">
        <div className="text-center">
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-muted-foreground font-medium">
            {slide.title || 'Add Image'}
          </p>
        </div>
      </div>
    )}
    
    <div className="p-6 space-y-4 flex flex-col flex-1">
      {slide.title && (
        <h3 className="text-lg font-bold text-card-foreground leading-tight">
          {slide.title}
        </h3>
      )}
      
      {slide.description && (
        <p className="text-muted-foreground flex-1 leading-relaxed text-sm">
          {slide.description}
        </p>
      )}

      {slide.content && (
        <div className="flex-1">
          <PortableTextEditor 
            data={slide.content}
            classNames="text-sm text-muted-foreground"
          />
        </div>
      )}

      {slide.button && (
        <div className="pt-2 mt-auto border-t border-dashed">
          <Button
            // @ts-expect-error: incompatible types
            variant={slide.button.style ?? 'primary'}
            buttonType={"external"}
            externalUrl={slide.button.url ?? "#"}
            className='h-12 w-full'
          >
            {slide.button.text}
          </Button>
        </div>
      )}
    </div>
  </motion.div>
);

// Render slide function
const renderSlide = (slide: CarouselSlide, index: number, contentType?: string) => {
  // Handle different slide types based on content type or slide type
  if (contentType === 'videos' || slide.slideType === 'video') {
    return <VideoSlide slide={slide} index={index}/>;
  }
  
  if (contentType === 'images' || slide.slideType === 'image' || slide._type === 'imageSlide') {
    return <ImageSlide slide={slide} index={index}/>;
  }
  
  if (contentType === 'cards' || slide.slideType === 'card' || slide._type === 'cardSlide') {
    return <CardSlide slide={slide} index={index}/>;
  }

  // Mixed content type - determine based on slide content
  if (slide.video) {
    return <VideoSlide slide={slide} index={index}/>;
  }
  
  if (slide.content || slide.title || slide.description || slide.button) {
    return <CardSlide slide={slide} index={index}/>;
  }
  
  // Default to image slide
  return <ImageSlide slide={slide} index={index}/>;
};

export default function CarouselBlock(props: CarouselBlockProps) {
  const { 
    title, 
    description, 
    carouselConfig: configProp, 
    contentType, 
    slides,
    anchorId 
  } = props;

  // Merge with default configuration
  const carouselConfig: NonNullable<CarouselConfig & { emblaOptions: EmblaOptionsType }> = {
    ...defaultConfig,
    ...configProp,
    autoplay: { ...defaultConfig.autoplay, ...configProp?.autoplay },
    // @ts-expect-error: Merging complex types
    emblaOptions: { ...defaultConfig.emblaOptions, ...configProp?.emblaOptions },
    // @ts-expect-error: Merging complex types
    slidesDisplay: { 
      ...defaultConfig.slidesDisplay,
      // @ts-expect-error: Merging complex types
      ...configProp?.slidesDisplay,
      slidesPerView: {
        ...defaultConfig.slidesDisplay?.slidesPerView,
        // @ts-expect-error: Merging complex types
        ...configProp?.slidesDisplay?.slidesPerView,
      }
    },
  };

  // Setup autoplay plugin with ref for better control
  const autoplayRef = useRef(
    carouselConfig.autoplay?.enabled
      ? Autoplay({
          delay: carouselConfig.autoplay.delay || 4000,
          stopOnInteraction: carouselConfig.autoplay.stopOnInteraction ?? true,
          stopOnMouseEnter: true,
          playOnInit: true,
        })
      : null
  );

  // Setup Embla carousel options
  const emblaOptions: EmblaOptionsType = {
    align: carouselConfig.emblaOptions?.align || 'start',
    loop: carouselConfig.emblaOptions?.loop ?? true,
    dragFree: carouselConfig.emblaOptions?.dragFree ?? false,
    slidesToScroll: typeof carouselConfig.emblaOptions?.slidesToScroll === 'string' 
      ? carouselConfig.emblaOptions.slidesToScroll === 'auto' 
        ? 'auto' 
        : parseInt(carouselConfig.emblaOptions.slidesToScroll)
      : carouselConfig.emblaOptions?.slidesToScroll || 1,
    containScroll: 'trimSnaps',
  };

  // Initialize Embla with conditional plugins
  const plugins = autoplayRef.current ? [autoplayRef.current] : [];
  const [emblaRef, emblaApi] = useEmblaCarousel(emblaOptions, plugins);

  // State management
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [isAutoplayActive, setIsAutoplayActive] = useState(false);

  // Navigation callbacks
  const scrollPrev = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollPrev();
    }
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollNext();
    }
  }, [emblaApi]);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) {
      emblaApi.scrollTo(index);
    }
  }, [emblaApi]);

  // Autoplay controls
  const toggleAutoplay = useCallback(() => {
    const autoplay = autoplayRef.current;
    if (!autoplay) return;

    if (autoplay.isPlaying()) {
      autoplay.stop();
      setIsAutoplayActive(false);
    } else {
      autoplay.play();
      setIsAutoplayActive(true);
    }
  }, []);

  // Initialize carousel state
  const onInit = useCallback((emblaApi: EmblaCarouselType) => {
    if (!emblaApi) return;
    
    setScrollSnaps(emblaApi.scrollSnapList());
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    
    // Set initial autoplay state
    if (autoplayRef.current) {
      setIsAutoplayActive(autoplayRef.current.isPlaying());
    }
  }, []);

  const onSelect = useCallback((emblaApi: EmblaCarouselType) => {
    if (!emblaApi) return;
    
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, []);

  // Autoplay event handlers
  const onAutoplayPlay = useCallback(() => {
    setIsAutoplayActive(true);
  }, []);

  const onAutoplayStop = useCallback(() => {
    setIsAutoplayActive(false);
  }, []);

  // Setup event listeners
  useEffect(() => {
    if (!emblaApi) return;

    // Initialize
    onInit(emblaApi);
    
    // Core events
    emblaApi.on('reInit', onInit);
    emblaApi.on('select', onSelect);
    
    // Autoplay events
    if (autoplayRef.current) {
      emblaApi.on('autoplay:play', onAutoplayPlay);
      emblaApi.on('autoplay:stop', onAutoplayStop);
    }

    // Cleanup
    return () => {
      emblaApi.off('reInit', onInit);
      emblaApi.off('select', onSelect);
      
      // eslint-disable-next-line react-hooks/exhaustive-deps
      if (autoplayRef.current) {
        emblaApi.off('autoplay:play', onAutoplayPlay);
        emblaApi.off('autoplay:stop', onAutoplayStop);
      }
    };
  }, [emblaApi, onInit, onSelect, onAutoplayPlay, onAutoplayStop]);

  // Get slides per view configuration
  const slidesPerView = {
    // @ts-expect-error: Merging complex types
    mobile: carouselConfig.slidesDisplay?.slidesPerView?.mobile || 1,
    // @ts-expect-error: Merging complex types
    tablet: carouselConfig.slidesDisplay?.slidesPerView?.tablet || 2,
    // @ts-expect-error: Merging complex types
    desktop: carouselConfig.slidesDisplay?.slidesPerView?.desktop || 3,
  };

  // Handle missing slides
  if (!slides || slides.length === 0) {
    return (
      <section 
        {...(anchorId ? { id: anchorId } : {})} 
        className="px-4 md:px-10 py-16 md:py-24 pattern-bg"
      >
        <Container>
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-gray-400 mb-4">
              <ImageIcon className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-card-foreground mb-2">No slides configured</h3>
            <p className="text-muted-foreground">Add some slides to display your carousel content.</p>
          </motion.div>
        </Container>
      </section>
    );
  }

  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})} 
      className="px-4 md:px-10 py-16 md:py-24 pattern-bg"
    >
      <Container className="space-y-12">
        {/* Header */}
        {(title || description) && (
          <div
            className="text-center max-w-3xl mx-auto"
          >
            {title && (
              <Heading size="xl" tag="h2" className="mb-4">
                {title}
              </Heading>
            )}
            {description && (
              <PortableTextEditor 
                data={typeof description === 'string' ? [{
                  _type: 'block',
                  children: [{ _type: 'span', text: description, marks: [] }],
                  style: 'normal'
                }] : description}
                classNames="text-lg text-muted-foreground"
              />
            )}
          </div>
        )}

        {/* Carousel Container */}
        <motion.div className="relative max-w-6xl mx-auto">
          {/* Embla Carousel Viewport */}
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex gap-6">
              {slides.map((slide: CarouselSlide, index: number) => (
                <div
                  key={slide._key} 
                  className={cn(
                    'shrink-0 min-w-0',
                    // Mobile
                    slidesPerView.mobile === 1 && 'basis-full',
                    slidesPerView.mobile === 2 && 'basis-1/2',
                    slidesPerView.mobile === 3 && 'basis-1/3',
                    // Tablet
                    slidesPerView.tablet === 1 && 'md:basis-full',
                    slidesPerView.tablet === 2 && 'md:basis-1/2',
                    slidesPerView.tablet === 3 && 'md:basis-1/3',
                    slidesPerView.tablet === 4 && 'md:basis-1/4',
                    // Desktop
                    slidesPerView.desktop === 1 && 'lg:basis-full',
                    slidesPerView.desktop === 2 && 'lg:basis-1/2',
                    slidesPerView.desktop === 3 && 'lg:basis-1/3',
                    slidesPerView.desktop === 4 && 'lg:basis-1/4',
                    slidesPerView.desktop === 5 && 'lg:basis-1/5'
                  )}
                >
                  <div className="h-full">
                    {/* @ts-expect-error: Merging complex types*/}
                    {renderSlide(slide, index, contentType)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Controls */}
          {/* @ts-expect-error: Merging complex types*/}
          {carouselConfig.showNavigation && slides.length > slidesPerView.desktop && (
            <>
              <button
                className="hidden lg:block absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-background border border-dashed rounded-full p-3 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-card shadow-xs"
                onClick={scrollPrev}
                disabled={!canScrollPrev && !emblaOptions.loop}
                aria-label="Previous slide"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              
              <button
                className="hidden lg:block absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-background border border-dashed rounded-full p-3 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-card shadow-xs"
                onClick={scrollNext}
                disabled={!canScrollNext && !emblaOptions.loop}
                aria-label="Next slide"
              >
                <ChevronRight className="w-6 h-6" />
              </button>
            </>
          )}

          {/* Autoplay Toggle */}
          {carouselConfig.autoplay?.enabled && (
            <button
              className="absolute top-4 right-4 z-10 bg-background border border-dashed rounded-full p-3 transition-all duration-200 hover:bg-card shadow-xs"
              onClick={toggleAutoplay}
              aria-label={isAutoplayActive ? 'Pause autoplay' : 'Start autoplay'}
              title={isAutoplayActive ? 'Pause autoplay' : 'Start autoplay'}
            >
              {isAutoplayActive ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </button>
          )}
        </motion.div>

        {/* Dot Indicators */}
        {/* @ts-expect-error: Merging complex types*/}
        {carouselConfig.showDots && scrollSnaps.length > 1 && (
          <div className="flex justify-center gap-2">
            {scrollSnaps.map((_, index) => (
              <button
                key={index}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-200 border border-dashed hover:scale-110',
                  {
                    'bg-primary border-primary scale-110': index === selectedIndex,
                    'bg-gray-200 border-gray-300 hover:bg-gray-300': index !== selectedIndex,
                  }
                )}
                onClick={() => scrollTo(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Thumbnails (if enabled) */}
        {/* @ts-expect-error: Merging complex types*/}
        {carouselConfig.showThumbnails && scrollSnaps.length > 1 && (
          <div className="flex justify-center gap-3 mt-6">
            {slides.slice(0, Math.min(scrollSnaps.length, 10)).map((slide: CarouselSlide, index: number) => (
              <button
                key={slide._key}
                className={cn(
                  'w-16 h-16 rounded-2xl overflow-hidden border-2 border-dashed transition-all duration-200',
                  {
                    'border-primary ring-2 ring-primary/20': index === selectedIndex,
                    'border-gray-200 hover:border-gray-400': index !== selectedIndex,
                  }
                )}
                onClick={() => scrollTo(index)}
                aria-label={`Go to slide ${index + 1}`}
              >
                {slide.image?.asset?.url ? (
                  <Image
                    src={slide.image.asset.url}
                    alt={slide.image.alt || `Thumbnail ${index + 1}`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                ) : slide.video?.thumbnail?.asset?.url ? (
                  <Image
                    src={slide.video.thumbnail.asset.url}
                    alt={slide.video.caption || `Video thumbnail ${index + 1}`}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-muted flex items-center justify-center">
                    <ImageIcon className="w-4 h-4 text-gray-400" />
                  </div>
                )}
              </button>
            ))}
          </div>
        )}
      </Container>
    </section>
  );
}
