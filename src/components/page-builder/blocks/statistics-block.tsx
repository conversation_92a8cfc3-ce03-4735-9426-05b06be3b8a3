"use client"
import { motion, useInView } from 'motion/react';
import { cn } from '@/lib/utils';
import Heading from '@/components/shared/heading';
import Container from '@/components/global/container';
import PortableTextEditor from '@/components/portable-text/portable-text-editor';
import { useRef, useEffect, useState } from 'react';
import Image from 'next/image';

export type StatisticsBlockProps = {
  _key: string;
  _type: string;
  title?: string;
  description?: string;
  statistics?: Array<{
    _key: string;
    label: string;
    description?: string;
    dataSource?: {
      staticConfig?: { value: string | number };
      staticValue?: string | number;
    };
    staticValue?: string | number;
    numberFormatting?: {
      prefix?: string;
      suffix?: string;
      decimalPlaces?: number;
      useThousandsSeparator?: boolean;
      abbreviateLargeNumbers?: boolean;
    };
    animation?: {
      duration?: number;
      delay?: number;
      trigger?: string;
    };
    icon?: {
      iconType?: string;
      emoji?: string;
      customImage?: {
        asset?: { url: string };
      };
    };
    styling?: {
      numberSize?: string;
      fontWeight?: string;
      numberColor?: { value: string };
      labelColor?: { value: string };
    };
  }>;
  layout?: {
    columns?: {
      mobile?: number;
      tablet?: number;
      desktop?: number;
    };
    spacing?: string;
    alignment?: string;
  };
  globalAnimation?: {
    enableStagger?: boolean;
    staggerDelay?: number;
  };
  backgroundColor?: {
    value: string;
  };
  anchorId?: string;
};

interface AnimatedNumberProps {
  value: number;
  duration: number;
  delay: number;
  formatting: {
    prefix?: string;
    suffix?: string;
    decimalPlaces?: number;
    useThousandsSeparator?: boolean;
    abbreviateLargeNumbers?: boolean;
  };
  trigger?: string;
  className?: string;
  style?: React.CSSProperties;
}

function AnimatedNumber({ 
  value, 
  duration, 
  delay, 
  formatting, 
  trigger = 'inView',
  className,
  style
}: AnimatedNumberProps) {
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true, margin: "0px 0px -10% 0px" });
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    if (trigger === 'inView' && !isInView) return;
    
    const timer = setTimeout(() => {
      const start = 0;
      const end = value;
      const startTime = Date.now();
      const animationDuration = duration * 1000;

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);
        
        // Easing function for smooth animation
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = start + (end - start) * easeOut;
        
        setDisplayValue(current);

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [value, duration, delay, trigger, isInView]);

  const formatNumber = (num: number) => {
    let formattedNum = num;
    let suffix = formatting.suffix || '';
    
    if (formatting.abbreviateLargeNumbers) {
      if (num >= 1000000000) {
        formattedNum = num / 1000000000;
        suffix = 'B' + suffix;
      } else if (num >= 1000000) {
        formattedNum = num / 1000000;
        suffix = 'M' + suffix;
      } else if (num >= 1000) {
        formattedNum = num / 1000;
        suffix = 'K' + suffix;
      }
    }

    if (formatting.decimalPlaces !== undefined) {
      formattedNum = parseFloat(formattedNum.toFixed(formatting.decimalPlaces));
    }

    let result = formattedNum.toString();
    
    if (formatting.useThousandsSeparator && !formatting.abbreviateLargeNumbers) {
      result = formattedNum.toLocaleString();
    }

    return `${formatting.prefix || ''}${result}${suffix}`;
  };

  return (
    <motion.span 
      ref={ref} 
      className={className}
      style={style}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ 
        duration: 0.6, 
        delay: delay,
        ease: [0.25, 0.1, 0.25, 1]
      }}
    >
      {formatNumber(displayValue)}
    </motion.span>
  );
}

export default function StatisticsBlock(props: StatisticsBlockProps) {
  const { 
    title, 
    description, 
    statistics, 
    layout, 
    globalAnimation, 
    backgroundColor,
    anchorId 
  } = props;

  const { columns, spacing } = layout || {};
  const { enableStagger, staggerDelay } = globalAnimation || {};

  // Add some demo data if statistics are missing values
  const enhancedStatistics = statistics?.map((stat) => {
    // Try to get the value from different possible locations in the data structure
    const staticValue = 
      stat.dataSource?.staticConfig?.value ||  // New schema structure
      stat.dataSource?.staticValue ||          // Alternative structure
      stat.staticValue                // Legacy structure
    
    return {
      ...stat,
      computedValue: staticValue ? parseFloat(staticValue.toString()) : 0
    };
  });

  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})} 
      className={cn('px-4 md:px-10 py-16 md:py-24', {
        'pattern-bg': !backgroundColor,
      })}
    >
      <Container className="space-y-12">
        {(title || description) && (
          <div
            className="text-center max-w-3xl mx-auto"
          >
            {title && (
              <Heading size="xl" tag="h2" className="mb-4">
                {title}
              </Heading>
            )}
            {description && (
              <PortableTextEditor 
                data={[{
                  _type: 'block',
                  children: [{ _type: 'span', text: description, marks: [] }],
                  style: 'normal'
                }]}
                classNames="text-lg text-muted-foreground"
              />
            )}
          </div>
        )}

        <div 
          className={cn(
            'grid gap-6 max-w-5xl mx-auto',
            {
              'grid-cols-1': columns?.mobile === 1,
              'grid-cols-2': columns?.mobile === 2,
              'grid-cols-3': columns?.mobile === 3,
              'md:grid-cols-2': columns?.tablet === 2,
              'md:grid-cols-3': columns?.tablet === 3,
              'md:grid-cols-4': columns?.tablet === 4,
              'lg:grid-cols-2': columns?.desktop === 2,
              'lg:grid-cols-3': columns?.desktop === 3,
              'lg:grid-cols-4': columns?.desktop === 4,
              'lg:grid-cols-5': columns?.desktop === 5,
              'lg:grid-cols-6': columns?.desktop === 6,
              'gap-4': spacing === '4',
              'gap-6': spacing === '6',
              'gap-8': spacing === '8',
              'gap-12': spacing === '12',
            }
          )}
        >
          {enhancedStatistics?.map((stat, index) => {
            const { 
              label, 
              description: statDescription, 
              numberFormatting, 
              animation, 
              icon, 
              computedValue
            } = stat;

            const animationDelay = enableStagger 
              ? (staggerDelay || 0.2) * index 
              : animation?.delay || 0;

            return (
              <motion.div
                key={stat._key}
                className="border border-dashed rounded-3xl"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-10%" }}
                transition={{ 
                  duration: 0.6, 
                  delay: animationDelay,
                  ease: [0.25, 0.1, 0.25, 1]
                }}
                whileHover={{ 
                  y: -4,
                  transition: { duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }
                }}
              >
                <div className="p-6 md:p-8 h-full">
                  <div className="text-center space-y-4 h-full flex flex-col">
                    {/* Icon */}
                    {icon?.iconType === 'emoji' && icon.emoji && (
                      <motion.div 
                        className="text-2xl flex justify-center"
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        viewport={{ once: true }}
                        transition={{ 
                          duration: 0.5, 
                          delay: animationDelay + 0.2,
                          type: "spring",
                          bounce: 0.4
                        }}
                      >
                        <div className="w-10 h-10 rounded-2xl bg-card border border-dashed flex items-center justify-center">
                          {icon.emoji}
                        </div>
                      </motion.div>
                    )}
                    
                    {icon?.iconType === 'customImage' && icon.customImage?.asset?.url && (
                      <motion.div 
                        className="flex justify-center"
                        initial={{ scale: 0, rotate: -10 }}
                        whileInView={{ scale: 1, rotate: 0 }}
                        viewport={{ once: true }}
                        transition={{ 
                          duration: 0.5, 
                          delay: animationDelay + 0.2,
                          type: "spring",
                          bounce: 0.3
                        }}
                      >
                        <div className="w-10 h-10 rounded-2xl bg-card border border-dashed flex items-center justify-center p-2">
                          <Image
                            src={icon.customImage.asset.url}
                            alt="" 
                            className="w-full h-full object-contain"
                          />
                        </div>
                      </motion.div>
                    )}

                    {/* Number and Label - fixed height container */}
                    <div className="space-y-3 flex-1 flex flex-col justify-center">
                      <div className="space-y-2">
                        <AnimatedNumber
                          value={computedValue}
                          duration={animation?.duration || 2}
                          delay={animationDelay + 0.3}
                          formatting={numberFormatting || {}}
                          trigger={animation?.trigger || 'inView'}
                          className="block font-bold leading-none text-3xl text-card-foreground"
                        />

                        <motion.h3 
                          className="text-sm font-semibold text-muted-foreground text-balance leading-tight"
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: true }}
                          transition={{ 
                            duration: 0.5, 
                            delay: animationDelay + 0.5
                          }}
                        >
                          {label}
                        </motion.h3>
                      </div>

                      {statDescription && (
                        <motion.p 
                          className="text-xs text-accent-foreground leading-relaxed text-balance border-t border-dashed pt-3 mt-auto"
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: true }}
                          transition={{ 
                            duration: 0.5, 
                            delay: animationDelay + 0.6
                          }}
                        >
                          {statDescription}
                        </motion.p>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </Container>
    </section>
  );
}
