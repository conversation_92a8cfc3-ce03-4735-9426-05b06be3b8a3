/* Embla Carousel Base Styles */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
}

.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 auto;
  min-width: 0;
  transition: transform 0.3s ease-out;
}

/* Fade Effect Styles */
.embla--fade .embla__slide {
  opacity: 0;
  transition: opacity 0.2s;
}

.embla--fade .embla__slide.is-snapped {
  opacity: 1;
}
