import Image from 'next/image';
import { cn } from '@/lib/utils';
import { stegaClean } from 'next-sanity';
import { PageBuilderType } from '@/types';
import Container from '@/components/global/container';
import {EdgeBlur} from "@/components/shared/edge-blur";

export type LogoBlockProps = PageBuilderType<"logoBlock">;

export default function LogoBlock(props: LogoBlockProps) {

  const { heading, logos, anchorId } = props;
  
  const items = logos ? [...logos, ...logos] : [];
  
  return (
    <section 
      {...(anchorId ? { id: anchorId } : {})}
      className='border-b border-b-border bg-card'
    >
      <Container className='px-0 border-x border-x-border border-dashed'>
        <div className='py-6 md:py-10'>
          <div className='relative w-fit mx-auto py-2 px-10 mt-4 md:mt-7 bg-accent border-y border-y-border'>
            <h2 className='text-center font-geist-mono text-xs md:text-sm uppercase font-medium text-accent-foreground px-4'>
              {heading}
            </h2>
            <EdgeBlur />
          </div>
          <div className="relative mt-10 md:mt-16 mb-6 md:mb-8 overflow-clip bg-accent">
            <div className="relative z-30 flex items-center py-4 md:py-10 pl-[4.8rem] gap-16 md:gap-40 w-max animate-logo-marquee border-y border-dashed">
              {items.map((item, index) => (
                <div key={item._key + index}>
                  {item.link ? (
                    <a 
                      href={item.link}
                      target="_blank" rel="noopener noreferrer" 
                    >
                      <Image
                        width={200}
                        height={100}
                        src={item.image?.asset?.url ?? ''}
                        alt={`${item.title} Logo`}
                        className={cn('w-20 md:w-28 object-contain', {
                          'w-36 md:w-40': stegaClean(item?.size) === 'large'
                        })}
                      />
                    </a>
                  ): (
                    <Image
                      width={200}
                      height={100}
                      src={item.image?.asset?.url ?? ''}
                      alt={`${item.title} Logo`}
                      className={cn('w-20 md:w-28 object-contain', {
                        'w-36 md:w-40': stegaClean(item?.size) === 'large'
                      })}
                    />  
                  )}
                </div>
              ))}
            </div>
            <EdgeBlur />
          </div>
        </div>
      </Container>
    </section>
  )
}