"use client"
import dynamic from "next/dynamic";
import {ComponentType} from "react";
import {PageBuilderType} from "@/types";
import {createDataAttribute} from "next-sanity";
import {PageBySlugQueryResult} from "../../../sanity.types";
import {dataset, projectId, studioUrl} from "@/sanity/lib/api";
import {CircleSlash} from "lucide-react";

const HeroBlock = dynamic(() => import("./blocks/hero-block"));
const HeaderBlock = dynamic(() => import("./blocks/header-block"));
const FeatureCardsBlock = dynamic(() => import("./blocks/feature-cards-block"));
const TestimonialBlock = dynamic(() => import("./blocks/testimonial-block"));
const LogoBlock = dynamic(() => import("./blocks/logo-block"));
const FreeformBlock = dynamic(() => import("./blocks/freeform-block"));
const PortableTextBlock = dynamic(() => import("./blocks/portable-text-block"));
const CallToActionBlock = dynamic(() => import("./blocks/call-to-action-block"));
const FeaturesMinimalBlock = dynamic(() => import("./blocks/features-minimal-block"));
const ServicesBlock = dynamic(() => import("./blocks/services-block"));
const FormBlock = dynamic(() => import("./blocks/form-block"));
const MediaBlock = dynamic(() => import("./blocks/media-block"));
const GridLayoutBlock = dynamic(() => import("./blocks/grid-layout-block"));
const ContentGridsBlock = dynamic(() => import("./blocks/content-grids-block"));
const ProcessTimelinesBlock = dynamic(() => import("./blocks/process-timelines-block"));
const StatisticsBlock = dynamic(() => import("./blocks/statistics-block"));
const CarouselBlock = dynamic(() => import("./blocks/carousel-block"));

type PageBlock = NonNullable<
    NonNullable<PageBySlugQueryResult>["pageBuilder"]
>[number];

export type PageBuilderProps = {
    pageBuilder: PageBlock[];
    id: string;
    type: string;
    inGrid?: boolean;
};

const PB_BLOCKS = {
    heroBlock: HeroBlock,
    headerBlock: HeaderBlock,
    featureCardsBlock: FeatureCardsBlock,
    testimonialBlock: TestimonialBlock,
    logoBlock: LogoBlock,
    freeformBlock: FreeformBlock,
    portableTextBlock: PortableTextBlock,
    callToActionBlock: CallToActionBlock,
    featuresMinimalBlock: FeaturesMinimalBlock,
    servicesBlock: ServicesBlock,
    formBlock: FormBlock,
    mediaBlock: MediaBlock,
    gridLayoutBlock: GridLayoutBlock,
    contentGridsBlock: ContentGridsBlock,
    processTimelinesBlock: ProcessTimelinesBlock,
    statisticsBlock: StatisticsBlock,
    carouselBlock: CarouselBlock,
} as const;

type BlockType = keyof typeof PB_BLOCKS;

export function PageBuilder({pageBuilder, id, type, inGrid = false}: PageBuilderProps) {
    const dataAttribute = id ? createDataAttribute({
        id: id,
        type: type,
        dataset: dataset,
        baseUrl: studioUrl,
        path: "pageBuilder",
        projectId: projectId,
    }).toString() : undefined;

    if (!id || !type) return (
        <div
            className="py-20 flex items-center justify-center gap-2 border border-dashed rounded-3xl text-center text-muted-foreground bg-background">
            <CircleSlash size={20} className='text-red-500'/> <span className='font-medium antialiased'>No page data found.</span>
        </div>
    );

    return (
        <div
            className={inGrid ? 'h-full flex flex-col' : undefined}
            {...(dataAttribute && {'data-sanity': dataAttribute})}
        >
            {pageBuilder.map((block) => {
                const Component = PB_BLOCKS[block._type] as ComponentType<PageBuilderType<BlockType>>;
                const blockDataAttribute = id ? createDataAttribute({
                    id: id,
                    type: type,
                    dataset: dataset,
                    baseUrl: studioUrl,
                    projectId: projectId,
                    path: `pageBuilder[_key=="${block._key}"]`,
                }).toString() : undefined;

                if (!Component) {
                    console.warn(`No component found for block type: ${block._type}`);
                    return (
                        <div
                            key={`${block._type}-${block._key}`}
                            className={inGrid ? 'h-full flex-1' : undefined}
                            {...(blockDataAttribute && {'data-sanity': blockDataAttribute})}
                        >
                            <div
                                className="py-20 flex items-center justify-center gap-2 border border-dashed rounded-3xl text-center text-muted-foreground bg-background">
                                <div className='flex flex-col items-center gap-2 0 min-w-[300px]'>
                                    <CircleSlash size={20} className='text-red-500'/>
                                    <span
                                        className='font-medium antialiased'>No component found for block type: {block._type}</span>
                                </div>
                                <code
                                    className='justify-start text-sm text-muted-foreground'>{JSON.stringify(block, null, 2)}</code>
                            </div>
                        </div>
                    );
                }

                return (
                    <div
                        key={`${block._type}-${block._key}`}
                        className={inGrid ? 'h-full flex-1' : undefined}
                        {...(blockDataAttribute && {'data-sanity': blockDataAttribute})}
                    >
                        <Component {...block}/>
                    </div>
                );
            })}
        </div>
    );
}