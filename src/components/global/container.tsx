import { cn } from '@/lib/utils';
import { cva, VariantProps } from 'class-variance-authority';

const containerVariants = cva(
  'h-full mx-auto',
  {
    variants: {
      variant: {
        contained:
          'max-w-8xl px-6 md:px-10',
        fullWidth:
          'max-w-full',
      },
    },
    defaultVariants: {
      variant: 'contained',
    },
  }
)

interface ContainerProps 
  extends React.HTMLAttributes<HTMLDivElement>, 
  VariantProps<typeof containerVariants>{
    paddingTop?: 'none' | 'small' | 'medium' | 'default' | 'large',
    paddingBottom?: 'none' | 'small' | 'medium' | 'default' | 'large',
  }

export default function Container(props: ContainerProps) {

  const { 
    variant, 
    className, 
    paddingTop, 
    paddingBottom, 
    children 
  } = props

  const paddingTopClass = paddingTop ? {
    'none': '',
    'small': 'pt-7',
    'medium': 'pt-14 md:pt-16',
    'default': 'pt-16 md:pt-28',
    'large': 'pt-16 lg:pt-36',
  }[paddingTop] : ''

  const paddingBottomClass = paddingBottom ? {
    'none': '',
    'small': 'pb-7',
    'medium': 'pb-14 md:pb-16',
    'default': 'pb-16 md:pb-28',
    'large': 'pb-16 lg:pb-36'
  }[paddingBottom] : ''
 
  return (
    <div className={cn(
      containerVariants({ variant }),
      paddingTopClass,
      paddingBottomClass,
      className
    )}>
      {children}
    </div>
  )
}