import React from 'react';
import Link from 'next/link';
import Container from './container';
import { Button } from '../ui/button';
import useScroll from '@/hooks/use-scroll';
import SiteLogo from '../shared/site-logo';
import SlideOutMenu from './slide-out-menu';
import { usePathname } from 'next/navigation';
import {cn, resolveHref} from '@/lib/utils';
import { ChevronRight, Menu } from 'lucide-react';
import AnimatedText from '../shared/animated-text';
import { GeneralSettingsQueryResult, NavigationSettingsQueryResult } from '../../../sanity.types';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuList, NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import { ModeToggle } from "@/components/shared/mode-toggle";
import {
  processNavigationItems,
  type NavigationItem
} from '@/lib/navigation-utils';
import { buildPagePath } from '@/lib/page-utils';
import {ButtonType} from "@/types";

interface NavbarProps {
  settings: GeneralSettingsQueryResult;
  navigationSettings: NavigationSettingsQueryResult;
}

export default function Navbar({ settings, navigationSettings }: NavbarProps) {
  const pathname = usePathname();
  const hasScrolled = useScroll();

  const { navbarMenuItems } = navigationSettings?.navbar ?? {};
  const { showSlideOutMenu } = navigationSettings?.slideOutMenu ?? {};
  
  // Process navigation items to ensure proper nested URLs
  const processedNavItems = processNavigationItems(navbarMenuItems as NavigationItem[] || []);
  console.dir(processedNavItems, {depth: 5});
  return (
    <header 
      className={cn('z-40 fixed top-0 left-0 w-full py-6 rounded-b-xl border-b border-b-border-100 bg-background/80 backdrop-blur-lg transition-all duration-300 ease-in-out', {
        'py-4 ': hasScrolled
      })}
    >
      <Container className='flex items-center justify-between'>
        <SiteLogo settings={settings} />
        <div className='flex items-center gap-3'>
          <NavigationMenu className='hidden md:block'>
            <NavigationMenuList className='space-x-8 group/nav'>
              {processedNavItems.map((item) => (
                <React.Fragment key={item._key}>
                  {!item.isButton ? (
                    <>
                      {item.menuItemType === 'group' ? (
                        <NavigationMenuItem>
                          <NavigationMenuTrigger className='group-hover/nav:opacity-40 hover:opacity-100!'>
                            {item.title}
                          </NavigationMenuTrigger>
                          <NavigationMenuContent className='min-w-[180px] text-nowrap py-3 px-3 flex flex-col gap-2 bg-background'>
                            {item.pageReferences?.map((page) => {
                              const pageUrl = resolveHref(page._type, page.slug ?? '');
                              console.log(page)
                              return (
                                <Link
                                  key={page._id}
                                  href={pageUrl ?? '/'}
                                  className='group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card'
                                >
                                  {page.title || page.name}
                                  <ChevronRight
                                    size={14}
                                    className='text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300'
                                  />
                                </Link>
                              );
                            })}
                          </NavigationMenuContent>
                        </NavigationMenuItem>
                      ) : (
                        <NavigationMenuItem>
                          {/* Handle single menu item with potential children */}
                          {item.showChildren && item.pageReference?.children && item.pageReference.children.length > 0 ? (
                            <>
                              <NavigationMenuTrigger className='group-hover/nav:opacity-40 hover:opacity-100!'>
                                {item.pageReference.title}
                              </NavigationMenuTrigger>
                              <NavigationMenuContent className='min-w-[180px] text-nowrap py-3 px-3 flex flex-col gap-2 bg-background'>
                                {/* Child pages as dropdown items */}
                                {item.pageReference.children.map((child) => {
                                  // Build proper nested path for child pages
                                  const childWithParent = {
                                    ...child,
                                    parent: item.pageReference
                                  };
                                  const childUrl = buildPagePath(childWithParent);
                                  return (
                                    <Link
                                      key={child._id}
                                      href={childUrl}
                                      className='group py-1 pl-3 pr-2 flex items-center justify-between gap-6 rounded-md border border-dashed hover:bg-card'
                                    >
                                      {child.title}
                                      <ChevronRight
                                        size={14}
                                        className='text-muted-foreground group-hover:-translate-x-0.5 group-hover:text-accent-foreground transition-all duration-300'
                                      />
                                    </Link>
                                  );
                                })}
                              </NavigationMenuContent>
                            </>
                          ) : item.showChildren ? (
                            <>
                              <NavigationMenuTrigger className='group-hover/nav:opacity-40 hover:opacity-100!'>
                                {item.pageReference?.title || item.title}
                              </NavigationMenuTrigger>
                              <NavigationMenuContent className='min-w-[180px] text-nowrap py-3 px-3 flex flex-col gap-2 bg-background'>
                                <div className='py-1 pl-3 pr-2 text-sm text-muted-foreground italic'>
                                  No child pages yet
                                </div>
                              </NavigationMenuContent>
                            </>
                          ) : (
                            /* Regular single menu item */
                            <Link
                              href={resolveHref(item?.pageReference?._type ?? '', item?.pageReference?.slug ?? '') ?? '/'}
                              className={cn('relative overflow-hidden inline-flex transition-opacity duration-200 group-hover/nav:opacity-40 hover:opacity-100!', {
                                'hover:underline underline-offset-38': !item.isButton,
                                'py-2 px-4 rounded-full text-foreground bg-primary': item.isButton,
                                'text-primary-hover': pathname.includes(`/${item.pageReference?.slug ?? ''}`)
                              })}
                            >
                              <AnimatedText>
                                {item.pageReference?.title || item.title}
                              </AnimatedText>
                            </Link>
                          )}
                        </NavigationMenuItem>
                      )}
                    </>
                  ) : (
                    <NavigationMenuItem>
                      <Button 
                        variant="primary" 
                        disableIcon={true}
                        buttonType="internal"
                        pageReference={item.pageReference as ButtonType['buttonPageReference']}
                      >
                        {item.title}
                      </Button>
                    </NavigationMenuItem>
                  )}
                </React.Fragment>
              ))}
              <ModeToggle/>
            </NavigationMenuList>
          </NavigationMenu>
          <div className='block md:hidden'>
            <ModeToggle/>
          </div>
          {showSlideOutMenu && (
            <SlideOutMenu settings={settings} navigationSettings={navigationSettings}>
              <button className="md:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors">
                <Menu className="h-6 w-6" />
              </button>
            </SlideOutMenu>
          )}
        </div>
      </Container>
    </header>
  );
}