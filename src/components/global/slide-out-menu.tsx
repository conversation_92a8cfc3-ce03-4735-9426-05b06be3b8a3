import React, { useState } from "react";
import { ChevronDown } from "lucide-react";
import SiteLogo from "../shared/site-logo";
import { useRouter } from "next/navigation";
import { cn, resolveHref } from "@/lib/utils";
import ButtonRenderer from "../shared/button-renderer";
import AnimatedUnderline from "../shared/animated-underline";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../ui/collapsible";
import { GeneralSettingsQueryResult, NavigationSettingsQueryResult } from "../../../sanity.types";
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '../ui/sheet';
import {buildPagePath, PageHierarchy} from '@/lib/page-utils';

export default function SlideOutMenu({ children, settings, navigationSettings }: {
  children: React.ReactNode;
  settings: GeneralSettingsQueryResult;
  navigationSettings: NavigationSettingsQueryResult;
}) {

  const router = useRouter();
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({});

  const { 
    slideOutMenuItems: menuItems,
    slideOutMenuButtons,
    slideOutMenuSettings,
    showCompanyDetailsSlideOutMenu
  } = navigationSettings?.slideOutMenu ?? {};

  return(
    <Sheet>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className='overflow-y-scroll pb-44'>
        <SheetHeader className='z-20 fixed top-0 pt-[26px] right-7 w-[338px] md:w-[330px] h-20 border-b border-dashed border-b-border-200 bg-background/95'>
          <SiteLogo settings={settings} theme='dark' />
        </SheetHeader>
        <SheetTitle className='mt-16 px-0 py-6 antialiased font-normal text-gray-400'>
          Explore
        </SheetTitle>
        <ul className='px-0 flex flex-col gap-4 text-foreground'>
          {menuItems?.map((item) => {
            return (
              <React.Fragment key={item?._key}>
                {item.menuItemType === 'group' ? (
                  <Collapsible 
                    open={openItems[item._key]}
                    onOpenChange={(open) => (
                      setOpenItems(prev => ({ ...prev, [item._key]: open }))
                    )
                    }
                    className="mx-auto"
                  >
                    <CollapsibleTrigger className="text-foreground relative flex items-center gap-2 text-3xl tracking-tight group">
                      <span className="relative">
                        {item.title} 
                        <AnimatedUnderline className='h-[2px]' />
                      </span>
                      <ChevronDown 
                        size={23} 
                        className={cn('translate-y-0.5 rotate-0 transition-transform duration-200', {
                          'rotate-180': openItems[item._key]
                        })}
                      />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="py-4 flex flex-col gap-y-1 transition-all duration-200">
                      {item?.pageReferences?.map((page) => (
                        <SheetClose key={page._id}>
                          <div
                            onClick={() => {
                              router.push(resolveHref(page._type ?? '', page.slug ?? '') ?? '/');
                              setOpenItems(prev => ({ ...prev, [item._key]: false }));
                            }}
                            className='relative block text-xl tracking-tight text-accent-foreground group cursor-pointer'
                          >
                            {page.title}
                            <AnimatedUnderline className='h-[1.5px] bg-card0 group-hover:bg-foreground' />
                          </div>
                        </SheetClose>
                      ))}                        
                    </CollapsibleContent>
                  </Collapsible>
                ) : (
                  // Handle single menu item with potential children
                  <>
                    {item.showChildren && item.pageReference?.children && item.pageReference.children.length > 0 ? (
                      <Collapsible
                        open={openItems[item._key]}
                        onOpenChange={(open) => (
                          setOpenItems(prev => ({ ...prev, [item._key]: open }))
                        )}
                        className="mx-auto"
                      >
                        <CollapsibleTrigger className="text-foreground relative flex items-center gap-2 text-3xl tracking-tight group">
                          <span className="relative">
                            {item.pageReference.title}
                            <AnimatedUnderline className='h-[2px]' />
                          </span>
                          <ChevronDown
                            size={23}
                            className={cn('translate-y-0.5 rotate-0 transition-transform duration-200', {
                              'rotate-180': openItems[item._key]
                            })}
                          />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="py-4 flex flex-col gap-y-1 transition-all duration-200">
                          {/* Child pages using buildPagePath for hierarchical URLs */}
                          {item.pageReference.children.map((child) => {
                            // Build proper nested path for child pages
                            const childWithParent = {
                              ...child,
                              title: child.title ?? '',
                              slug: child.slug ?? '',
                              parent: {
                                ...item.pageReference,
                                title: item.pageReference?.title ?? '',
                                slug: item.pageReference?.slug ?? ''
                              }
                            } as PageHierarchy;

                            const childUrl = buildPagePath(childWithParent);

                            return (
                              <SheetClose key={child._id}>
                                <div
                                  onClick={() => {
                                    router.push(childUrl);
                                    setOpenItems(prev => ({ ...prev, [item._key]: false }));
                                  }}
                                  className='relative block text-xl tracking-tight text-accent-foreground group cursor-pointer'
                                >
                                  {child.title}
                                  <AnimatedUnderline className='h-[1.5px] bg-card0 group-hover:bg-foreground' />
                                </div>
                              </SheetClose>
                            );
                          })}
                        </CollapsibleContent>
                      </Collapsible>
                    ) : item.showChildren ? (
                      <Collapsible
                        open={openItems[item._key]}
                        onOpenChange={(open) => (
                          setOpenItems(prev => ({ ...prev, [item._key]: open }))
                        )}
                        className="mx-auto"
                      >
                        <CollapsibleTrigger className="text-foreground relative flex items-center gap-2 text-3xl tracking-tight group">
                          <span className="relative">
                            {item.pageReference?.title || item.title}
                            <AnimatedUnderline className='h-[2px]' />
                          </span>
                          <ChevronDown
                            size={23}
                            className={cn('translate-y-0.5 rotate-0 transition-transform duration-200', {
                              'rotate-180': openItems[item._key]
                            })}
                          />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="py-4 flex flex-col gap-y-1 transition-all duration-200">
                          <div className='text-sm text-muted-foreground italic ml-4 mt-2'>
                            No child pages yet
                          </div>
                        </CollapsibleContent>
                      </Collapsible>
                    ) : (
                      /* Regular single menu item - keep using resolveHref */
                      <SheetClose>
                        <div
                          onClick={() => (
                            router.push(resolveHref(item.pageReference?._type ?? '', item.pageReference?.slug ?? '') ?? '/')
                          )}
                          className='text-foreground relative block text-3xl tracking-tight group cursor-pointer'
                        >
                          {item.pageReference?.title || item.title}
                          <AnimatedUnderline className='h-[2px]' />
                        </div>
                      </SheetClose>
                    )}
                  </>
                )}
              </React.Fragment>
            )
          })}
        </ul>
        {showCompanyDetailsSlideOutMenu && (
          <>
            <SheetTitle className='border-t border-dashed mt-8 px-0 pt-8 antialiased font-normal text-gray-400'>
              Say Hello
            </SheetTitle>
            <div className="mt-2 space-y-4">
              <a 
                href={`mailto:${slideOutMenuSettings?.companyEmailAddress ?? ''}`} 
                className="relative w-fit block text-2xl tracking-tight group"
              >
                {slideOutMenuSettings?.companyEmailAddress ?? ''}
                <AnimatedUnderline className='h-[2px]' />
              </a>
            </div>
          </>
        )}
        {slideOutMenuButtons && slideOutMenuButtons.length > 0 && (
          <div className='pt-10 absolute bottom-1 right-0 w-full md:w-[380px] px-4 pb-4 bg-linear-to-t from-background via-accent to-transparent'>
            <ButtonRenderer buttons={slideOutMenuButtons} classNames="flex-col md:flex-row" />  
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}