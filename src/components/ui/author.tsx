import Image from "next/image";
import { PostBySlugQueryResult } from "../../../sanity.types";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "./hover-card";

type Author = NonNullable<
  NonNullable<PostBySlugQueryResult>
>;

interface AuthorProps {
  author: Author['author'];
  classNames?: string;  
}

export default function Author({ author, classNames }: AuthorProps) {

  if (!author) return null;

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div className="cursor-pointer">
          <Image
            src={author?.avatar?.asset?.url ?? ''}
            width={26}
            height={26}
            alt={author.name ?? ''}
            className='rounded-full'
          />
        </div>
      </HoverCardTrigger>
      <HoverCardContent className={classNames}>
        <div className='text-sm font-semibold antialiased'>
          {author.name}
        </div>
        <div className='text-sm text-muted-foreground'>
          @{author.username}
        </div>
        <div className='mt-3 pt-3 border-t border-dashed text-sm text-muted-foreground'>
          {author.bio}
        </div>
      </HoverCardContent>
    </HoverCard>
  )
}