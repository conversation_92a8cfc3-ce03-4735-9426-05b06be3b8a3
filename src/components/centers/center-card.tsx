import Link from 'next/link';
import Image from 'next/image';
import { MapPin, Phone, Mail, Car, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import AnimatedUnderline from '@/components/shared/animated-underline';
import {Center} from "../../../sanity.types";

interface CenterCardProps {
  center: Omit<Center, 'brandConfig' | 'image'> & {
    image?: {
      asset?: {
        url: string;
      };
      altText?: string;
    };
    brandConfig?: {
      logo?: {
        asset?: {
          url: string;
        };
        altText?: string;
      };
    };
  };
}

export function CenterCard({ center }: CenterCardProps) {
  const centerTypeColors = {
    audi: 'bg-red-100 text-red-800',
    skoda: 'bg-green-100 text-green-800',
    occasions: 'bg-blue-100 text-blue-800',
    multibrands: 'bg-purple-100 text-purple-800',
  };

  const centerTypeLabels = {
    audi: 'Audi',
    skoda: 'Skoda',
    occasions: 'Occasions Premium',
    multibrands: 'Multi-marques',
  };

  return (
    <article aria-label={center.name || ''} className="relative group pb-8 border-b border-dashed">
      <div className="relative block">
        {/* Center Type Badge */}
        <div className="z-10 absolute top-10 left-10 px-1.5 rounded-md text-sm font-medium text-nowrap bg-background">
          <Badge 
            className={centerTypeColors[center.centerType as keyof typeof centerTypeColors] || 'bg-gray-100 text-gray-800'}
          >
            {centerTypeLabels[center.centerType as keyof typeof centerTypeLabels] || center.centerType}
          </Badge>
        </div>

        {/* Image */}
        <div className="p-4 rounded-3xl border border-dashed backdrop-blur-md backdrop-opacity-50 pattern-bg">
          {center.image?.asset?.url ? (
            <Image
              src={center.image.asset.url}
              alt={center.image.altText || center.name || ''}
              width={800}
              height={500}
              className="aspect-3/2 rounded-2xl object-cover"
            />
          ) : (
            <div className="aspect-3/2 rounded-2xl bg-muted flex items-center justify-center">
              <Car className="h-12 w-12 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Header with brand logo and name */}
        <div className="mt-5 md:mt-6 flex items-center gap-3">
          {center.brandConfig?.logo?.asset?.url && (
            <Image
              src={center.brandConfig.logo.asset.url}
              alt={center.brandConfig.logo.altText || `${center.name} logo`}
              width={32}
              height={32}
              className="object-contain"
            />
          )}
          <h3 className="text-xl font-semibold text-balance">{center.name}</h3>
        </div>

        {/* Description */}
        {center.shortDescription && (
          <p className="mt-4 text-balance text-neutral-500 line-clamp-2">
            {center.shortDescription}
          </p>
        )}

        {/* Info Section */}
        <div className="mt-5 md:mt-6 space-y-3">
          {/* Location */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>{center.address?.city}{center.address?.region && `, ${center.address?.region}`}</span>
          </div>

          {/* Contact */}
          <div className="flex items-center gap-2 text-sm">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <a 
              href={`tel:${center.contact?.phone}`}
              className="hover:underline"
            >
              {center.contact?.phone}
            </a>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <a 
              href={`mailto:${center.contact?.email}`}
              className="hover:underline"
            >
              {center.contact?.email}
            </a>
          </div>
        </div>

        {/* Specialties */}
        {center.specialties && center.specialties.length > 0 && (
          <div className="mt-4">
            <div className="flex flex-wrap gap-1">
              {center.specialties.slice(0, 3).map((specialty, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {specialty}
                </Badge>
              ))}
              {center.specialties.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{center.specialties.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Footer with action indicator */}
        <Link href={`/centres/${center.slug}`}>
          <div className="mt-5 md:mt-6 flex items-center justify-between">
            <span className="text-sm font-medium">Découvrir ce centre</span>
            <ChevronRight
              size={18}
              className="-translate-x-6 opacity-0 group-hover:-translate-x-0 group-hover:opacity-100 transition-all duration-300 text-muted-foreground"
            />
          </div>
        </Link>
      </div>
      <AnimatedUnderline className="-translate-y-0.5" />
    </article>
  );
}
