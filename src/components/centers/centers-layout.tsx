import { ReactNode } from 'react';
import Container from '@/components/global/container';

interface CentersLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

export function CentersLayout({ 
  children, 
  title = "Nos Centres AutoCorner",
  description = "Découvrez nos centres spécialisés en Suisse. Expertise Audi et Skoda, véhicules d'occasion premium, service personnalisé."
}: CentersLayoutProps) {
  return (
    <main className="pt-24">
      <Container className="py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">{title}</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {description}
          </p>
        </div>
        {children}
      </Container>
    </main>
  );
}
