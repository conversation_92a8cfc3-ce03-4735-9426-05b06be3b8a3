import Image from 'next/image';
import { MapPin, Phone, Mail } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Container from '@/components/global/container';
import Heading from '@/components/shared/heading';
import AnimatedUnderline from '@/components/shared/animated-underline';
import {Center} from "../../../sanity.types";

interface CenterHeroProps {
  center: Omit<Center, 'brandConfig' | 'image'> & {
    image?: {
      asset?: {
        url: string;
      };
      altText?: string;
    };
    brandConfig?: {
      logo?: {
        asset?: {
          url: string;
        };
        altText?: string;
      };
    };
  };
}

export function CenterHero({ center }: CenterHeroProps) {
  const centerTypeColors = {
    audi: 'bg-red-100 text-red-800',
    skoda: 'bg-green-100 text-green-800',
    occasions: 'bg-blue-100 text-blue-800',
    multibrands: 'bg-purple-100 text-purple-800',
  };

  const centerTypeLabels = {
    audi: 'Audi',
    skoda: 'Skoda',
    occasions: 'Occasions Premium',
    multibrands: 'Multi-marques',
  };

  return (
    <section className="relative pt-24 pb-12 pattern-bg">
      {/* Background Image */}
      {center.image?.asset?.url && (
        <div className="absolute inset-0 overflow-hidden">
          <Image
            src={center.image.asset.url}
            alt={center.image.altText || center.name || ''}
            fill
            className="object-cover opacity-10"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-background/80 to-background/95 backdrop-blur-sm" />
        </div>
      )}

      <Container className="relative">
        <div className="max-w-4xl mx-auto">
          <div className="p-6 md:p-8 rounded-3xl border border-dashed backdrop-blur-md backdrop-opacity-50 text-center relative">
            {/* Badge and Logo */}
            <div className="flex items-center justify-center gap-4 mb-6">
              {center.brandConfig?.logo?.asset?.url && (
                <Image
                  src={center.brandConfig.logo.asset.url}
                  alt={center.brandConfig.logo.altText || `${center.name} logo`}
                  width={80}
                  height={80}
                  className="object-contain"
                />
              )}
              <Badge 
                className={`text-lg px-4 py-2 ${centerTypeColors[center.centerType as keyof typeof centerTypeColors] || 'bg-gray-100 text-gray-800'}`}
              >
                {centerTypeLabels[center.centerType as keyof typeof centerTypeLabels] || center.centerType}
              </Badge>
            </div>

            {/* Title */}
            <Heading tag="h1" size="2xl" className="mb-6 text-balance">
              {center.name}
            </Heading>

            {/* Description */}
            {center.shortDescription && (
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto text-balance">
                {center.shortDescription}
              </p>
            )}

            {/* Quick Info */}
            <div className="inline-flex flex-wrap items-center justify-center gap-6 mb-8 text-sm p-4 rounded-2xl border border-dashed">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{center.address?.city}{center.address?.region && `, ${center.address?.region}`}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <a 
                  href={`tel:${center.contact?.phone}`}
                  className="hover:underline"
                >
                  {center.contact?.phone}
                </a>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <a 
                  href={`mailto:${center.contact?.email}`}
                  className="hover:underline"
                >
                  {center.contact?.email}
                </a>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-wrap gap-4 justify-center">
              <Button asChild className="relative group overflow-hidden">
                <a href="#contact" className="px-6 py-2">
                  <span className="relative z-10">Nous contacter</span>
                  <span className="absolute inset-0 w-full h-full bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                </a>
              </Button>
              <Button variant="outline" asChild className="border-dashed hover:border-solid transition-all">
                <a href="#info">Informations pratiques</a>
              </Button>
              <Button variant="outline" asChild className="border-dashed hover:border-solid transition-all">
                <a href="#vehicles">Nos véhicules</a>
              </Button>
            </div>
          </div>
          <AnimatedUnderline className="mt-2" />
        </div>
      </Container>
    </section>
  );
}
