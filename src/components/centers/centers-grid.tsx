import { sanityFetch } from '@/sanity/lib/live';
import { allCentersQuery, centersByTypeQuery } from '@/sanity/lib/queries/documents/center';
import { CenterCard } from './center-card';
import {Center} from "../../../sanity.types";

interface CentersGridProps {
  centerType?: string;
  limit?: number;
  className?: string;
}

export async function CentersGrid({ centerType, limit, className }: CentersGridProps) {
  const query = centerType ? centersByTypeQuery : allCentersQuery;
  const params = centerType ? { centerType } : {};
  
  const { data: centers }: { data: Center[] } = await sanityFetch({
    query,
    params,
  });

  if (!centers || centers.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          Aucun centre trouvé{centerType && ` pour le type ${centerType}`}.
        </p>
      </div>
    );
  }

  const displayCenters = limit ? centers.slice(0, limit) : centers;

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className || ''}`}>
      {displayCenters.map((center) => (
          /* @ts-expect-error weak reference for images */
          <CenterCard key={center._id} center={center} />
      ))}
    </div>
  );
}
