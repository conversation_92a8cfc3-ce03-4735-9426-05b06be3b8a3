import Image from 'next/image';
import { MapPin, Phone, Mail, Clock, Globe, MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {Center} from "../../../sanity.types";

interface CenterInfoProps {
  center: Omit<Center, 'brandConfig' | 'image'> & {
    image?: {
      asset?: {
        url: string;
      };
      altText?: string;
    };
    brandConfig?: {
      logo?: {
        asset?: {
          url: string;
        };
        altText?: string;
      };
    };
  };
}

export function CenterInfo({ center }: CenterInfoProps) {
  const centerTypeColors = {
    audi: 'bg-red-100 text-red-800',
    skoda: 'bg-green-100 text-green-800',
    occasions: 'bg-blue-100 text-blue-800',
    multibrands: 'bg-purple-100 text-purple-800',
  };

  const centerTypeLabels = {
    audi: 'Audi',
    skoda: 'Skoda',
    occasions: 'Occasions Premium',
    multibrands: 'Multi-marques',
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start gap-4">
        {center.brandConfig?.logo?.asset?.url && (
          <Image
            src={center.brandConfig.logo.asset.url}
            alt={center.brandConfig.logo.altText || `${center.name} logo`}
            width={64}
            height={64}
            className="object-contain"
          />
        )}
        <div>
          <Badge 
            className={`mb-2 ${centerTypeColors[center.centerType as keyof typeof centerTypeColors] || 'bg-gray-100 text-gray-800'}`}
          >
            {centerTypeLabels[center.centerType as keyof typeof centerTypeLabels] || center.centerType}
          </Badge>
          <h1 className="text-3xl font-bold mb-2">{center.name}</h1>
          {center.shortDescription && (
            <p className="text-muted-foreground">{center.shortDescription}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Address */}
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div>
                <p className="font-medium">Adresse</p>
                <p className="text-sm text-muted-foreground">
                  {center.address?.street}<br />
                  {center.address?.postalCode} {center.address?.city}
                  {center.address?.region && `, ${center.address?.region}`}
                </p>
                {center.address?.coordinates && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    asChild
                  >
                    <a 
                      href={`https://maps.google.com/?q=${center.address.coordinates.lat},${center.address.coordinates.lng}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Voir sur la carte
                    </a>
                  </Button>
                )}
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Téléphone</p>
                <a 
                  href={`tel:${center.contact?.phone}`}
                  className="text-sm text-primary hover:underline"
                >
                  {center.contact?.phone}
                </a>
              </div>
            </div>

            {/* Email */}
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="font-medium">Email</p>
                <a 
                  href={`mailto:${center.contact?.email}`}
                  className="text-sm text-primary hover:underline"
                >
                  {center.contact?.email}
                </a>
              </div>
            </div>

            {/* Website */}
            {center.contact?.website && (
              <div className="flex items-center gap-3">
                <Globe className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Site web</p>
                  <a 
                    href={center.contact.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline"
                  >
                    Visiter le site
                  </a>
                </div>
              </div>
            )}

            {/* WhatsApp */}
            {center.contact?.whatsapp && (
              <div className="flex items-center gap-3">
                <MessageCircle className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">WhatsApp</p>
                  <a 
                    href={`https://wa.me/${center.contact.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline"
                  >
                    Envoyer un message
                  </a>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Opening Hours */}
        {center.openingHours && center.openingHours.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {`Heures d'ouverture`}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {center.openingHours.map((schedule, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="font-medium">{schedule.day}</span>
                    <span className="text-muted-foreground">{schedule.hours}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Specialties */}
      {center.specialties && center.specialties.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Spécialités</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {center.specialties.map((specialty, index) => (
                <Badge key={index} variant="outline">
                  {specialty}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Team */}
      {center.team && center.team.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Notre équipe</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {center.team.map((member, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                  {/* @ts-expect-error: weak reference */}
                  {member.photo?.asset?.url ? (
                    <Image
                      //@ts-expect-error: weak reference
                      src={member.photo.asset.url}
                      //@ts-expect-error: weak reference
                      alt={member.photo.altText || member.name}
                      width={48}
                      height={48}
                      className="rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">
                        {member.name?.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                  )}
                  <div className="flex-1">
                    <p className="font-medium text-sm">{member.name}</p>
                    <p className="text-xs text-muted-foreground">{member.role}</p>
                    {member.directPhone && (
                      <a 
                        href={`tel:${member.directPhone}`}
                        className="text-xs text-primary hover:underline block"
                      >
                        {member.directPhone}
                      </a>
                    )}
                    {member.directEmail && (
                      <a 
                        href={`mailto:${member.directEmail}`}
                        className="text-xs text-primary hover:underline block"
                      >
                        {member.directEmail}
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
