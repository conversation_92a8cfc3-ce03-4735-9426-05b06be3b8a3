import { buildPagePath, type PageHierarchy } from './page-utils';

export interface NavigationPage extends PageHierarchy {
  name: string;
  _type: string;
}

export interface NavigationItem {
  _key: string;
  title: string;
  menuItemType: 'single' | 'group';
  isButton?: boolean;
  showChildren?: boolean;
  pageReference?: NavigationPage & {
    children?: NavigationPage[];
  };
  pageReferences?: NavigationPage[];
}

/**
 * Build URL for navigation items using hierarchy-aware path building
 */
export function buildNavigationUrl(page: NavigationPage): string {
  if (!page) return '/';

  // Use the page hierarchy to build the correct nested path
  return buildPagePath(page);
}

/**
 * Process navigation items to ensure all URLs are hierarchy-aware
 */
export function processNavigationItems(items: NavigationItem[]): NavigationItem[] {
  return items.map(item => {
    const processedItem = { ...item };

    // Process single item with potential children
    if (item.menuItemType === 'single' && item.pageReference) {
      // @ts-expect-error - url is not in the type definition but we add it here
      processedItem.pageReference = {
        ...item.pageReference,
        url: buildNavigationUrl(item.pageReference)
      } as NavigationPage & { url: string };

      // Process children from pageReference.children (not item.children)
      if (item.pageReference.children && item.pageReference.children.length > 0) {
        // @ts-expect-error - url is not in the type definition but we add it here
        processedItem.pageReference.children = item.pageReference.children.map(child => ({
          ...child,
          _type: child._type || 'page', // Ensure _type is present
          url: buildNavigationUrl({ ...child, _type: child._type || 'page' } as NavigationPage)
        })) as (NavigationPage & { url: string })[];
      }
    }

    // Process group items
    if (item.menuItemType === 'group' && item.pageReferences) {
      processedItem.pageReferences = item.pageReferences.map(page => ({
        ...page,
        url: buildNavigationUrl(page)
      })) as (NavigationPage & { url: string })[];
    }

    return processedItem;
  });
}
