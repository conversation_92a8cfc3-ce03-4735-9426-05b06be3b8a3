/**
 * Utility functions for handling nested page URLs and breadcrumbs
 */

export interface PageHierarchy {
  _type: string;
  _id: string;
  title: string;
  slug: string;
  parent?: PageHierarchy;
  children?: PageHierarchy[];
}

export interface BreadcrumbItem {
  title: string;
  href: string;
  isActive?: boolean;
}

/**
 * Build a nested URL path from a page hierarchy
 * @param page - Page object with parent references
 * @returns Full URL path (e.g., "/parent/child/grandchild")
 */
export function buildPagePath(page: PageHierarchy): string {
  const segments: string[] = [];
  let currentPage: PageHierarchy | undefined = page;

  // Traverse up the hierarchy to collect all slugs
  while (currentPage) {
    if (currentPage.slug) {
      segments.unshift(currentPage.slug);
    }
    currentPage = currentPage.parent;
  }

  return segments.length > 0 ? `/${segments.join('/')}` : '/';
}

/**
 * Build breadcrumb navigation from page hierarchy
 * @param page - Page object with parent references
 * @returns Array of breadcrumb items
 */
export function buildBreadcrumbs(page: PageHierarchy): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];
  const pages: PageHierarchy[] = [];
  let currentPage: PageHierarchy | undefined = page;

  // Collect all pages in the hierarchy
  while (currentPage) {
    pages.unshift(currentPage);
    currentPage = currentPage.parent;
  }

  // Build breadcrumb items
  pages.forEach((p, index) => {
    const isLast = index === pages.length - 1;
    const pathSegments = pages.slice(0, index + 1).map(pg => pg.slug);

    breadcrumbs.push({
      title: p.title,
      href: `/${pathSegments.join('/')}`,
      isActive: isLast
    });
  });

  return breadcrumbs;
}

/**
 * Parse URL segments to match against page hierarchy
 * @param segments - Array of URL segments
 * @returns Formatted slug path for Sanity queries
 */
export function parseSlugSegments(segments: string[]): string {
  if (!segments || segments.length === 0) return '';
  return segments[segments.length - 1]; // Return the last segment for now
}

/**
 * Build all possible paths for a page hierarchy (for static generation)
 * @param pages - Array of all pages with hierarchy
 * @returns Array of path objects for Next.js static generation
 */
export function buildAllPagePaths(pages: PageHierarchy[]): { slug: string[] }[] {
  return pages.map(page => {
    const path = buildPagePath(page);
    const segments = path.split('/').filter(Boolean);
    return { slug: segments };
  });
}

/**
 * Find a page by its full path
 * @param pages - Array of all pages
 * @param pathSegments - URL path segments
 * @returns Matching page or null
 */
export function findPageByPath(pages: PageHierarchy[], pathSegments: string[]): PageHierarchy & { pageBuilder?: never[] } | null {
  if (!pathSegments || pathSegments.length === 0) return null;

  const targetSlug = pathSegments[pathSegments.length - 1];
  const targetPage = pages.find(page => page.slug === targetSlug);

  if (!targetPage) return null;

  // Verify the full path matches
  const fullPath = buildPagePath(targetPage);
  const expectedPath = `/${pathSegments.join('/')}`;

  return fullPath === expectedPath ? targetPage : null;
}
