import { type DataSourceConfig, type GridItem } from './data-source-fetcher';
import { fetchDataSource } from './data-source-fetcher';

/**
 * Server-side data fetcher for ContentGridsBlock
 * This runs on the server during SSR/SSG for better performance
 */
export async function fetchContentGridsData(
  dataSource?: DataSourceConfig,
  manualItems?: GridItem[]
): Promise<{ items: GridItem[]; error?: string }> {
  try {
    if (!dataSource) {
      return { items: manualItems || [] };
    }

    // For now, only handle document queries on the server
    // API calls and other sources will be handled client-side
    if (dataSource.sourceType === 'documents' && dataSource.documentConfig) {
      const fetchedItems = await fetchDataSource(dataSource);
      return {
        items: [...fetchedItems, ...(manualItems || [])]
      };
    }

    // For other source types, return manual items and let client handle the rest
    return { items: manualItems || [] };
  } catch (error) {
    console.error('Server-side data fetch failed:', error);
    return { 
      items: manualItems || [],
      error: dataSource?.errorHandling?.fallbackValue || 'Failed to load content'
    };
  }
}

/**
 * Build a static GROQ query for the content grids block
 * This can be used in getStaticProps/getServerSideProps
 */
export function buildContentGridsQuery(documentType: string = 'post', limit: number = 12) {
  return `*[_type == "${documentType}"] | order(publishedAt desc) [0...${limit}] {
    _id,
    _type,
    _createdAt,
    title,
    description,
    excerpt,
    shortDescription,
    publishedAt,
    image {
      asset->{
        _id,
        url
      },
      alt,
      altText,
      caption
    },
    "slug": slug.current,
    category->{
      _id,
      title,
      "slug": slug.current
    },
    categories[]->{
      _id,
      title,
      "slug": slug.current
    },
    author->{
      _id,
      name,
      username,
      avatar {
        asset->{
          url
        }
      }
    },
    tags,
    featured
  }`;
}
