import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.settingsItemTranslations,
  fr: fr.settingsItemTranslations,
});

export const settingsItemDict = dict;
export const getSettingsItemTranslations = getTranslations;
export type SettingsItemTranslations = typeof en.settingsItemTranslations;
