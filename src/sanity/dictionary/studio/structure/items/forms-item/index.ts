import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.formsItemTranslations,
  fr: fr.formsItemTranslations,
});

export const formsItemDict = dict;
export const getFormsItemTranslations = getTranslations;
export type FormsItemTranslations = typeof en.formsItemTranslations;
