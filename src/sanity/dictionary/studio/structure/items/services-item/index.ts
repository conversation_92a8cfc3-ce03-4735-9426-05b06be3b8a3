import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.servicesItemTranslations,
  fr: fr.servicesItemTranslations,
});

export const servicesItemDict = dict;
export const getServicesItemTranslations = getTranslations;
export type ServicesItemTranslations = typeof en.servicesItemTranslations;
