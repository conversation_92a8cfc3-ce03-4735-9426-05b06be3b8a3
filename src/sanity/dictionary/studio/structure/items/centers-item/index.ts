import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.centersItemTranslations,
  fr: fr.centersItemTranslations,
});

export const centersItemDict = dict;
export const getCentersItemTranslations = getTranslations;
export type CentersItemTranslations = typeof en.centersItemTranslations;
