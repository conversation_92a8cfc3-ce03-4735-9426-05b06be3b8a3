import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.blogItemTranslations,
  fr: fr.blogItemTranslations,
});

export const blogItemDict = dict;
export const getBlogItemTranslations = getTranslations;
export type BlogItemTranslations = typeof en.blogItemTranslations;
