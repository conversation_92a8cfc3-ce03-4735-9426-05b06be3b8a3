import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.testimonialsItemTranslations,
  fr: fr.testimonialsItemTranslations,
});

export const testimonialsItemDict = dict;
export const getTestimonialsItemTranslations = getTranslations;
export type TestimonialsItemTranslations = typeof en.testimonialsItemTranslations;
