import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.pagesItemTranslations,
  fr: fr.pagesItemTranslations,
});

export const pagesItemDict = dict;
export const getPagesItemTranslations = getTranslations;
export type PagesItemTranslations = typeof en.pagesItemTranslations;
