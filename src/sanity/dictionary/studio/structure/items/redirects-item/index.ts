import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.redirectsItemTranslations,
  fr: fr.redirectsItemTranslations,
});

export const redirectsItemDict = dict;
export const getRedirectsItemTranslations = getTranslations;
export type RedirectsItemTranslations = typeof en.redirectsItemTranslations;
