import * as en from './en';
import * as fr from './fr';
import { setupStructureTranslations } from '../../../schemas/utils';

const { dict, getTranslations } = setupStructureTranslations({
  en: en.projectsItemTranslations,
  fr: fr.projectsItemTranslations,
});

export const projectsItemDict = dict;
export const getProjectsItemTranslations = getTranslations;
export type ProjectsItemTranslations = typeof en.projectsItemTranslations;
