// French translations for author schema
export const authorTranslations = {
  // Document info
  document: {
    name: 'author',
    title: 'Auteur',
  },
  
  // Field translations
  fields: {
    name: 'Nom',
    username: 'Nom d\'utilisateur',
    bio: 'Biographie',
    avatar: 'Avatar',
  },
  
  // Validation messages
  validation: {
    nameRequired: 'Le nom est requis',
    usernameRequired: 'Le nom d\'utilisateur est requis',
    bioRequired: 'La biographie est requise',
  },
  
  // Field descriptions/help text
  descriptions: {
    name: 'Nom complet de l\'auteur',
    username: 'Nom d\'utilisateur unique pour l\'auteur',
    bio: 'Une courte biographie (2-3 phrases)',
    avatar: 'Photo de profil de l\'auteur',
  },
} as const;
