// English translations for author schema
export const authorTranslations = {
  // Document info
  document: {
    name: 'author',
    title: 'Author',
  },
  
  // Field translations
  fields: {
    name: 'Name',
    username: '<PERSON>rna<PERSON>',
    bio: 'Biography',
    avatar: 'Avatar',
  },
  
  // Validation messages
  validation: {
    nameRequired: 'Name is required',
    usernameRequired: 'Username is required',
    bioRequired: 'Bio is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    name: 'Full name of the author',
    username: 'Unique username for the author',
    bio: 'A short biography (2-3 sentences)',
    avatar: 'Profile picture for the author',
  },
} as const;
