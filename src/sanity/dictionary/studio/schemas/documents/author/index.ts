// Author schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.authorTranslations,
  fr: fr.authorTranslations,
});

// Export for use in schemas
export const authorDict = dict;
export const createAuthorField = createField;
export const getAuthorTranslations = getTranslations;

// Export types
export type AuthorTranslations = typeof en.authorTranslations;
