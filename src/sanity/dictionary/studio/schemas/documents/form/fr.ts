// French translations for form schema
export const formTranslations = {
  document: {
    name: 'form',
    title: 'Formulaire',
  },
  fields: {
    formTitle: 'Titre du formulaire',
    submitButtonText: 'Texte du bouton d\'envoi',
    formFields: 'Champs du formulaire',
    name: 'Nom',
    placeholder: 'Placeholder',
    inputType: 'Type d\'entrée',
    isRequired: 'Obligatoire',
    text: 'Texte',
    textarea: 'Zone de texte',
    email: 'E-mail',
    telephone: 'Téléphone',
  },
  validation: {
    titleRequired: 'Le titre est requis',
    submitButtonTextRequired: 'Le texte du bouton d\'envoi est requis',
  },
  descriptions: {
    formTitle: 'Le titre principal de ce formulaire',
    submitButtonText: 'Texte du bouton d\'envoi',
    formFields: 'Champs inclus dans ce formulaire',
    name: 'Nom du champ',
    placeholder: 'Texte d\'espace réservé pour le champ',
    inputType: 'Type d\'entrée (texte, zone de texte, téléphone, etc.)',
    isRequired: 'Ce champ est-il obligatoire ?',
    text: 'Champ de texte sur une ligne',
    textarea: 'Zone de texte multi-lignes',
    telephone: 'Champ de saisie de téléphone',
  },
} as const;
