// English translations for form schema
export const formTranslations = {
  document: {
    name: 'form',
    title: 'Form',
  },
  fields: {
    formTitle: 'Form Title',
    submitButtonText: 'Submit Button Text',
    formFields: 'Form Fields',
    name: 'Name',
    placeholder: 'Placeholder',
    inputType: 'Input Type',
    isRequired: 'Required',
    text: 'Text',
    textarea: 'Text Area',
    email: 'Email',
    telephone: 'Telephone',
  },
  validation: {
    titleRequired: 'Title is required',
    submitButtonTextRequired: 'Submit button text is required',
  },
  descriptions: {
    formTitle: 'The main title for this form',
    submitButtonText: 'Text for the submit button',
    formFields: 'Fields included in this form',
    name: 'Field name',
    placeholder: 'Placeholder text for the field',
    inputType: 'Type of input (text, textarea, telephone, etc.)',
    isRequired: 'Is this field required?',
    text: 'Single line text input',
    textarea: 'Multi-line text area',
    telephone: 'Telephone input',
  },
} as const;
