// Form schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.formTranslations,
  fr: fr.formTranslations,
});

export const formDict = dict;
export const createFormField = createField;
export const getFormTranslations = getTranslations;

export type FormTranslations = typeof en.formTranslations;
