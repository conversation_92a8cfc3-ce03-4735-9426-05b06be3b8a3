import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
    en: en.redirectTranslations,
    fr: fr.redirectTranslations,
});
// Export for use in schemas
export const redirectDict = dict;
export const createRedirectField = createField;
export const getRedirectTranslations = getTranslations;
// Export types
export type RedirectTranslations = typeof en.redirectTranslations;