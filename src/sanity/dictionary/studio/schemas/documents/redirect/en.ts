// English translations for redirect schema
export const redirectTranslations = {
  document: {
    name: 'redirect',
    title: 'Redirect',
  },
  fields: {
    source: 'Source Path',
    destination: 'Destination URL',
    permanent: 'Permanent',
    isEnabled: 'Enabled',
    // Add your fields here
  },
  validation: {
    sourceRequired: 'Source path is required',
    destinationRequired: 'Destination URL is required',
    invalidSource: 'Source path is invalid',
    invalidDestination: 'Destination URL is invalid',
    sameSourceDestination: 'Source and destination cannot be the same',
    // Add your validation messages here
  },
  descriptions: {
    source: 'The internal path to redirect from (must start with /)',
    destination: 'The full URL or internal path to redirect to',
    permanent: 'If true, this is a permanent (301) redirect. Otherwise, it is temporary (302).',
    isEnabled: 'If false, this redirect will be ignored.',
    // Add your field descriptions here
  },
} as const;
