// French translations for redirect schema
export const redirectTranslations = {
  document: {
    name: 'redirect',
    title: 'Redirection',
  },
  fields: {
    source: 'Chemin source',
    destination: 'URL de destination',
    permanent: 'Permanent',
    isEnabled: 'Activé',
    // Ajoutez vos champs ici
  },
  validation: {
    sourceRequired: 'Le chemin source est requis',
    destinationRequired: 'L’URL de destination est requise',
    invalidSource: 'Le chemin source est invalide',
    invalidDestination: 'L’URL de destination est invalide',
    sameSourceDestination: 'La source et la destination ne peuvent pas être identiques',
    // Ajoutez vos messages de validation ici
  },
  descriptions: {
    source: 'Le chemin interne à rediriger (doit commencer par /)',
    destination: 'L’URL complète ou le chemin interne vers lequel rediriger',
    permanent: 'Si vrai, il s’agit d’une redirection permanente (301). Sinon, elle est temporaire (302).',
    isEnabled: 'Si désactivé, cette redirection sera ignorée.',
    // Ajoutez vos descriptions de champ ici
  },
} as const;
