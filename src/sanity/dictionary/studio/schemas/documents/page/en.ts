// English translations for page schema
export const pageTranslations = {
  // Document info
  document: {
    name: 'page',
    title: 'Page',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    pageBuilder: 'Page Builder',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The main title for this page',
    slug: 'URL-friendly version of the title, used in the page URL',
    pageBuilder: 'Build your page using flexible content blocks',
    seo: 'Search engine optimization settings for this page',
  },
} as const;

export type PageTranslations = typeof pageTranslations;
