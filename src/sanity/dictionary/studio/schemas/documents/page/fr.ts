// French translations for page schema
export const pageTranslations = {
  // Document info
  document: {
    name: 'page',
    title: '<PERSON>',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    pageBuilder: 'Constructeur de page',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le titre principal de cette page',
    slug: 'Version URL-friendly du titre, utilisée dans l\'URL de la page',
    pageBuilder: 'Construisez votre page en utilisant des blocs de contenu flexibles',
    seo: 'Paramètres d\'optimisation pour les moteurs de recherche de cette page',
  },
} as const;

export type PageTranslations = typeof pageTranslations;
