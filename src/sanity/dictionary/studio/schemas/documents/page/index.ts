// Page schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.pageTranslations,
  fr: fr.pageTranslations,
});

// Export for use in schemas
export const pageDict = dict;
export const createPageField = createField;
export const getPageTranslations = getTranslations;

// Export types for backwards compatibility
export type PageTranslations = typeof en.pageTranslations;
