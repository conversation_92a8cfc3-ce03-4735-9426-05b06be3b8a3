// Center schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.centerTranslations,
  fr: fr.centerTranslations,
});

// Export for use in schemas
export const centerDict = dict;
export const createCenterField = createField;
export const getCenterTranslations = getTranslations;

// Export types
export type CenterTranslations = typeof en.centerTranslations;
