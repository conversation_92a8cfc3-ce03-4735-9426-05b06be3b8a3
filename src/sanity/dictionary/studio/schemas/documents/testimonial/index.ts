// Testimonial schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.testimonialTranslations,
  fr: fr.testimonialTranslations,
});

// Export for use in schemas
export const testimonialDict = dict;
export const createTestimonialField = createField;
export const getTestimonialTranslations = getTranslations;

// Export types
export type TestimonialTranslations = typeof en.testimonialTranslations;
