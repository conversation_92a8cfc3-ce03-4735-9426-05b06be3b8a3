// French translations for testimonial schema
export const testimonialTranslations = {
  // Document info
  document: {
    name: 'testimonial',
    title: 'Témoignage',
  },
  
  // Field translations
  fields: {
    name: 'Nom',
    jobTitle: 'Titre du poste',
    company: 'Nom de l\'entreprise',
    quote: 'Citation',
    avatar: 'Avatar',
    logo: 'Logo',
  },
  
  // Validation messages
  validation: {
    nameRequired: 'Le nom est requis',
    jobTitleRequired: 'Le titre du poste est requis',
    quoteRequired: 'La citation est requise',
    avatarRequired: 'L\'avatar est requis',
    logoRequired: 'Le logo est requis',
  },
  
  // Field descriptions/help text
  descriptions: {
    name: 'Nom complet de la personne donnant le témoignage',
    jobTitle: 'Titre du poste ou rôle de la personne',
    company: 'Nom de l\'entreprise ou organisation',
    quote: 'Le texte du témoignage ou de l\'avis',
    avatar: 'Photo de profil de la personne',
    logo: 'Logo de l\'entreprise ou organisation',
  },
} as const;
