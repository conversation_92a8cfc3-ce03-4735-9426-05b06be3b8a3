// English translations for testimonial schema
export const testimonialTranslations = {
  // Document info
  document: {
    name: 'testimonial',
    title: 'Testimonial',
  },
  
  // Field translations
  fields: {
    name: 'Name',
    jobTitle: 'Job Title',
    company: 'Company Name',
    quote: 'Quote',
    avatar: 'Avatar',
    logo: 'Logo',
  },
  
  // Validation messages
  validation: {
    nameRequired: 'Name is required',
    jobTitleRequired: 'Job title is required',
    quoteRequired: 'Quote is required',
    avatarRequired: 'Avatar is required',
    logoRequired: 'Logo is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    name: 'Full name of the person giving the testimonial',
    jobTitle: 'Job title or role of the person',
    company: 'Company or organization name',
    quote: 'The testimonial quote or review text',
    avatar: 'Profile picture of the person',
    logo: 'Company or organization logo',
  },
} as const;
