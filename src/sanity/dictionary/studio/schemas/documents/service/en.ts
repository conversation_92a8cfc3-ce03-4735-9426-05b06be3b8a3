// English translations for service schema
export const serviceTranslations = {
  // Document info
  document: {
    name: 'service',
    title: 'Service',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    shortDescription: 'Short Description',
    image: 'Image',
    altText: 'Alt Text',
    caption: 'Caption',
    pageBuilder: 'Page Builder',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The main title for this service',
    slug: 'URL-friendly version of the title, used in the service URL',
    shortDescription: 'A brief description of the service',
    image: 'Featured image for this service',
    altText: 'Alternative text for the image (for accessibility)',
    caption: 'Caption to display with the image',
    pageBuilder: 'Build your service page using flexible content blocks',
    seo: 'Search engine optimization settings for this service',
  },
} as const;
