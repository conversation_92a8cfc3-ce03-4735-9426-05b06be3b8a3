// French translations for service schema
export const serviceTranslations = {
  // Document info
  document: {
    name: 'service',
    title: 'Service',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    shortDescription: 'Description courte',
    image: 'Image',
    altText: 'Texte alternatif',
    caption: 'Légende',
    pageBuilder: 'Constructeur de page',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le titre principal de ce service',
    slug: 'Version URL-friendly du titre, utilisée dans l\'URL du service',
    shortDescription: 'Une brève description du service',
    image: 'Image en vedette pour ce service',
    altText: 'Texte alternatif pour l\'image (pour l\'accessibilité)',
    caption: 'Légende à afficher avec l\'image',
    pageBuilder: 'Construisez votre page de service en utilisant des blocs de contenu flexibles',
    seo: 'Paramètres d\'optimisation pour les moteurs de recherche de ce service',
  },
} as const;
