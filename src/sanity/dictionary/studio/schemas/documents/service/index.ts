// Service schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.serviceTranslations,
  fr: fr.serviceTranslations,
});

// Export for use in schemas
export const serviceDict = dict;
export const createServiceField = createField;
export const getServiceTranslations = getTranslations;

// Export types
export type ServiceTranslations = typeof en.serviceTranslations;
