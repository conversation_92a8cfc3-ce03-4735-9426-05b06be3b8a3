// English translations for post-category schema
export const postCategoryTranslations = {
  // Document info
  document: {
    name: 'postCategory',
    title: 'Post Category',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    categoryColor: 'Category Color',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The name of this post category',
    slug: 'URL-friendly version of the title, used in category URLs',
    categoryColor: 'Color associated with this category for visual distinction',
  },
} as const;
