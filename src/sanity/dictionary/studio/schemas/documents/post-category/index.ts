// Post Category schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.postCategoryTranslations,
  fr: fr.postCategoryTranslations,
});

// Export for use in schemas
export const postCategoryDict = dict;
export const createPostCategoryField = createField;
export const getPostCategoryTranslations = getTranslations;

// Export types
export type PostCategoryTranslations = typeof en.postCategoryTranslations;
