// French translations for post-category schema
export const postCategoryTranslations = {
  // Document info
  document: {
    name: 'postCategory',
    title: 'Catégorie d\'article',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    categoryColor: 'Couleur de catégorie',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le nom de cette catégorie d\'article',
    slug: 'Version URL-friendly du titre, utilisée dans les URLs de catégorie',
    categoryColor: 'Couleur associée à cette catégorie pour la distinction visuelle',
  },
} as const;
