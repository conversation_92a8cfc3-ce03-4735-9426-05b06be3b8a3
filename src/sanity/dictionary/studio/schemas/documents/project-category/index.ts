import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
    en: en.projectCategoryTranslations,
    fr: fr.projectCategoryTranslations,
});
// Export for use in schemas
export const projectCategoryDict = dict;
export const createProjectCategoryField = createField;
export const getProjectCategoryTranslations = getTranslations;
// Export types
export type ProjectCategoryTranslations = typeof en.projectCategoryTranslations;