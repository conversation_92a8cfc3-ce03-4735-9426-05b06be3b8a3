// English translations for post schema
export const postTranslations = {
  // Document info
  document: {
    name: 'post',
    title: 'Blog Post',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    category: 'Category',
    author: 'Author',
    excerpt: 'Excerpt',
    content: 'Content',
    image: 'Image',
    altText: 'Alt Text',
    caption: 'Caption',
    relatedPosts: 'Related Posts',
    relatedPostsType: 'Related Posts Type',
    chooseRelatedPosts: 'Choose Related Posts',
    customRelatedPosts: 'Custom Related Posts',
    autofill: 'Autofill',
    custom: 'Custom',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
    categoryRequired: 'Category is required',
    authorRequired: 'Author is required',
    maxRelatedPosts: 'Maximum 3 related posts allowed',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The main title for this blog post',
    slug: 'URL-friendly version of the title, used in the post URL',
    category: 'The category this post belongs to',
    author: 'The author of this blog post',
    excerpt: 'A short summary of the post content',
    content: 'The main content of the blog post',
    image: 'Featured image for this blog post',
    altText: 'Alternative text for the image (for accessibility)',
    caption: 'Caption to display with the image',
    relatedPosts: 'How to determine related posts for this article',
    relatedPostsType: 'Choose between automatic related posts or manually selected ones',
    chooseRelatedPosts: 'Manually select up to 3 related posts',
    customRelatedPosts: 'Select specific posts to show as related',
    seo: 'Search engine optimization settings for this post',
  },
} as const;
