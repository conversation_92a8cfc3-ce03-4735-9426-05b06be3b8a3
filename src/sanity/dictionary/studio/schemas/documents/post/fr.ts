// French translations for post schema
export const postTranslations = {
  // Document info
  document: {
    name: 'post',
    title: 'Article de blog',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    category: 'Caté<PERSON>ie',
    author: 'Auteur',
    excerpt: 'Extrait',
    content: 'Contenu',
    image: 'Image',
    altText: 'Texte alternatif',
    caption: 'Légende',
    relatedPosts: 'Articles liés',
    relatedPostsType: 'Type d\'articles liés',
    chooseRelatedPosts: 'Choisir les articles liés',
    customRelatedPosts: 'Articles liés personnalisés',
    autofill: 'Remplissage automatique',
    custom: 'Personnalisé',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
    categoryRequired: 'La catégorie est requise',
    authorRequired: 'L\'auteur est requis',
    maxRelatedPosts: 'Maximum 3 articles liés autorisés',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le titre principal de cet article de blog',
    slug: 'Version URL-friendly du titre, utilisée dans l\'URL de l\'article',
    category: 'La catégorie à laquelle appartient cet article',
    author: 'L\'auteur de cet article de blog',
    excerpt: 'Un court résumé du contenu de l\'article',
    content: 'Le contenu principal de l\'article de blog',
    image: 'Image en vedette pour cet article de blog',
    altText: 'Texte alternatif pour l\'image (pour l\'accessibilité)',
    caption: 'Légende à afficher avec l\'image',
    relatedPosts: 'Comment déterminer les articles liés pour cet article',
    relatedPostsType: 'Choisir entre les articles liés automatiques ou sélectionnés manuellement',
    chooseRelatedPosts: 'Sélectionner manuellement jusqu\'à 3 articles liés',
    customRelatedPosts: 'Sélectionner des articles spécifiques à afficher comme liés',
    seo: 'Paramètres d\'optimisation pour les moteurs de recherche de cet article',
  },
} as const;
