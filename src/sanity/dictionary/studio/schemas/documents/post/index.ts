// Post schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.postTranslations,
  fr: fr.postTranslations,
});

// Export for use in schemas
export const postDict = dict;
export const createPostField = createField;
export const getPostTranslations = getTranslations;

// Export types
export type PostTranslations = typeof en.postTranslations;
