// Project schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.projectTranslations,
  fr: fr.projectTranslations,
});

// Export for use in schemas
export const projectDict = dict;
export const createProjectField = createField;
export const getProjectTranslations = getTranslations;

// Export types
export type ProjectTranslations = typeof en.projectTranslations;
