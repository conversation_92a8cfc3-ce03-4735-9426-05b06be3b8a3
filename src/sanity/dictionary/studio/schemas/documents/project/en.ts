// English translations for project schema
export const projectTranslations = {
  // Document info
  document: {
    name: 'project',
    title: 'Project',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    category: 'Project Category',
    excerpt: 'Excerpt',
    image: 'Image',
    altText: 'Alt Text',
    caption: 'Caption',
    pageBuilder: 'Page Builder',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
    categoryRequired: 'Category is required',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The main title for this project',
    slug: 'URL-friendly version of the title, used in the project URL',
    category: 'The category this project belongs to',
    excerpt: 'A short summary of the project',
    image: 'Featured image for this project',
    altText: 'Alternative text for the image (for accessibility)',
    caption: 'Caption to display with the image',
    pageBuilder: 'Build your project page using flexible content blocks',
    seo: 'Search engine optimization settings for this project',
  },
} as const;
