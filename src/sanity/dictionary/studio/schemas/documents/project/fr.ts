// French translations for project schema
export const projectTranslations = {
  // Document info
  document: {
    name: 'project',
    title: 'Projet',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    category: 'Catégorie de projet',
    excerpt: 'Extrait',
    image: 'Image',
    altText: 'Texte alternatif',
    caption: 'Légende',
    pageBuilder: 'Constructeur de page',
    seo: 'SEO',
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
    categoryRequired: 'La catégorie est requise',
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le titre principal de ce projet',
    slug: 'Version URL-friendly du titre, utilisée dans l\'URL du projet',
    category: 'La catégorie à laquelle appartient ce projet',
    excerpt: 'Un court résumé du projet',
    image: 'Image en vedette pour ce projet',
    altText: 'Texte alternatif pour l\'image (pour l\'accessibilité)',
    caption: 'Légende à afficher avec l\'image',
    pageBuilder: 'Construisez votre page de projet en utilisant des blocs de contenu flexibles',
    seo: 'Paramètres d\'optimisation pour les moteurs de recherche de ce projet',
  },
} as const;
