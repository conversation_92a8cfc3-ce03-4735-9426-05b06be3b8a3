// Utility to easily create new schema translations
import { createSchemaTranslations, type SchemaTranslationDictionaries, type SchemaTranslations } from './factory';

/**
 * Helper function to quickly set up translations for any schema
 * 
 * Usage:
 * ```typescript
 * import { setupSchemaTranslations } from '../../utils';
 * 
 * const { dict, createField, getTranslations } = setupSchemaTranslations({
 *   en: enTranslations,
 *   fr: frTranslations,
 * });
 * ```
 */
export function setupSchemaTranslations<T extends SchemaTranslations>(
  dictionaries: SchemaTranslationDictionaries<T>
) {
  const translations = createSchemaTranslations(dictionaries);
  
  return {
    dict: translations.getDict(),
    createField: (fieldName: string, options: Record<string, string> = {}) =>
      translations.createField(fieldName, options),
    getTranslations: (language?: string) => 
      translations.getTranslations(language),
    availableLanguages: translations.getAvailableLanguages(),
  };
}

export interface StructureTranslations {
  items: Record<string, string>;
}

/**
 * Helper function to quickly set up translations for structure items (desk structure, etc)
 * Accepts a dictionary with 'items' (Record<string, string>).
 */
export function setupStructureTranslations<T extends StructureTranslations>(
  dictionaries: { en: T; fr: T; [key: string]: T }
) {
  const lang = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
  const dict = (lang && dictionaries[lang]) ? dictionaries[lang] : dictionaries.en;
  const getTranslations = (language?: string) => {
    if (language && dictionaries[language]) return dictionaries[language];
    return dict;
  };
  return {
    dict,
    getTranslations,
    availableLanguages: Object.keys(dictionaries)
  };
}
