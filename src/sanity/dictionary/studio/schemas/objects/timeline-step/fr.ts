// French translations for timeline-step object schema
export const timelineStepObjectTranslations = {
  fields: {
    title: 'Étape de la frise',
    stepTitle: 'Titre de l’étape',
    stepDescription: 'Description de l’étape',
    stepDescriptionDesc: 'Courte description de cette étape',
    content: 'Contenu',
    contentDesc: 'Contenu riche pour cette étape',
    status: 'Statut',
    status_completed: 'Terminée',
    status_inProgress: 'En cours',
    status_pending: 'En attente',
    status_blocked: 'Bloquée',
    isMilestone: 'Jalon',
    isMilestoneDesc: 'Marquer cette étape comme un jalon',
    icon: 'Icône',
    iconType: 'Type d’icône',
    iconType_number: 'Numéro',
    iconType_lucide: 'Icône Lucide',
    iconType_image: 'Image personnalisée',
    iconType_none: 'Aucune',
    number: 'Numéro d’étape',
    numberDesc: 'Numéro à afficher dans l’icône',
    lucideIcon: 'Nom de l’icône Lucide',
    lucideIconDesc: 'Nom de l’icône Lucide (ex : CheckCircle)',
    customImage: 'Image personnalisée',
    timeline: 'Frise',
    startDate: 'Date de début',
    startDateDesc: 'Quand cette étape commence',
    endDate: 'Date de fin',
    endDateDesc: 'Quand cette étape se termine',
    duration: 'Durée',
    durationDesc: 'Durée de cette étape',
    assignee: 'Assigné',
    name: 'Nom',
    role: 'Rôle',
    avatar: 'Avatar',
    customColor: 'Couleur personnalisée',
    customColor_default: 'Défaut',
    customColor_blue: 'Bleu',
    customColor_green: 'Vert',
    customColor_orange: 'Orange',
    customColor_red: 'Rouge',
    customColor_purple: 'Violet',
    progress: 'Progression',
    progressDesc: 'Pourcentage d’achèvement de cette étape',
    untitled: 'Étape sans titre',
    milestone: 'Jalon',
  },
  validation: {},
  descriptions: {},
} as const;
