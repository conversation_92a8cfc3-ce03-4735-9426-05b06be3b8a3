import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.timelineStepObjectTranslations,
  fr: fr.timelineStepObjectTranslations,
});

export const timelineStepObjectDict = dict;
export const createTimelineStepObjectField = createField;
export const getTimelineStepObjectTranslations = () => getTranslations();
export type TimelineStepObjectTranslations = typeof en.timelineStepObjectTranslations;
