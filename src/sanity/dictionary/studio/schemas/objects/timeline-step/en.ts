// English translations for timeline-step object schema
export const timelineStepObjectTranslations = {
  fields: {
    title: 'Timeline Step',
    stepTitle: 'Step Title',
    stepDescription: 'Step Description',
    stepDescriptionDesc: 'Short description of this step',
    content: 'Content',
    contentDesc: 'Rich content for this step',
    status: 'Status',
    status_completed: 'Completed',
    status_inProgress: 'In Progress',
    status_pending: 'Pending',
    status_blocked: 'Blocked',
    isMilestone: 'Milestone',
    isMilestoneDesc: 'Mark this step as a milestone',
    icon: 'Icon',
    iconType: 'Icon Type',
    iconType_number: 'Number',
    iconType_lucide: 'Lucide Icon',
    iconType_image: 'Custom Image',
    iconType_none: 'None',
    number: 'Step Number',
    numberDesc: 'Number to display in the icon',
    lucideIcon: 'Lucide Icon Name',
    lucideIconDesc: 'Name of the Lucide icon (e.g., CheckCircle)',
    customImage: 'Custom Image',
    timeline: 'Timeline',
    startDate: 'Start Date',
    startDateDesc: 'When this step starts',
    endDate: 'End Date',
    endDateDesc: 'When this step ends',
    duration: 'Duration',
    durationDesc: 'Duration of this step',
    assignee: 'Assignee',
    name: 'Name',
    role: 'Role',
    avatar: 'Avatar',
    customColor: 'Custom Color',
    customColor_default: 'Default',
    customColor_blue: 'Blue',
    customColor_green: 'Green',
    customColor_orange: 'Orange',
    customColor_red: 'Red',
    customColor_purple: 'Purple',
    progress: 'Progress',
    progressDesc: 'Completion percentage for this step',
    untitled: 'Untitled Step',
    milestone: 'Milestone',
  },
  validation: {},
  descriptions: {},
} as const;
