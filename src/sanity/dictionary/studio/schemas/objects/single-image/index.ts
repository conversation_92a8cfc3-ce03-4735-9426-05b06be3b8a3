import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.singleImageObjectTranslations,
  fr: fr.singleImageObjectTranslations,
});

export const singleImageObjectDict = dict;
export const createSingleImageObjectField = createField;
export const getSingleImageObjectTranslations = () => getTranslations();
export type SingleImageObjectTranslations = typeof en.singleImageObjectTranslations;
