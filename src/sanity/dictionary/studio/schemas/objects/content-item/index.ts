import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.contentItemObjectTranslations,
  fr: fr.contentItemObjectTranslations,
});

export const contentItemObjectDict = dict;
export const createContentItemObjectField = createField;
export const getContentItemObjectTranslations = getTranslations;
export type ContentItemObjectTranslations = typeof en.contentItemObjectTranslations;
