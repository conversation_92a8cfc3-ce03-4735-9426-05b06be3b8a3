// English translations for content-item object schema
export const contentItemObjectTranslations = {
  fields: {
    title: 'Content Item',
    type: 'Content Type',
    type_manual: 'Manual Entry',
    type_reference: 'Reference Existing Document',
    type_description: 'Choose between manual content or linking to existing documents',
    title_field: 'Title',
    title_required: 'Title is required for manual entries',
    excerpt: 'Excerpt',
    excerpt_description: 'Brief description or excerpt',
    image: 'Image',
    image_alt: 'Alt Text',
    reference: 'Reference Document',
    reference_required: 'Reference is required when using reference type',
    categories: 'Categories',
    categories_description: 'Categories for filtering (manual entry or override for references)',
    tags: 'Tags',
    tags_description: 'Tags for filtering and search',
    featured: 'Featured',
    featured_description: 'Mark as featured content',
    link: 'Action Link',
    link_label: 'Link Label',
    link_label_default: 'Read More',
    link_url: 'URL',
    link_url_description: 'External URL or internal path',
    link_openInNewTab: 'Open in New Tab',
    metadata: 'Metadata',
    metadata_author: 'Author',
    metadata_publishDate: 'Publish Date',
    metadata_priority: 'Priority',
    metadata_priority_description: 'Higher numbers appear first when sorting by priority',
    metadata_status: 'Status',
    metadata_status_published: 'Published',
    metadata_status_draft: 'Draft',
    metadata_status_archived: 'Archived',
  },
  validation: {},
  descriptions: {},
} as const;
