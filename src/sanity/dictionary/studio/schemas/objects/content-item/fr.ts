// French translations for content-item object schema
export const contentItemObjectTranslations = {
  fields: {
    title: 'Élément de contenu',
    type: 'Type de contenu',
    type_manual: 'Saisie manuelle',
    type_reference: 'Référence à un document existant',
    type_description: 'Choi<PERSON><PERSON>z entre un contenu manuel ou un lien vers un document existant',
    title_field: 'Titre',
    title_required: 'Le titre est requis pour les saisies manuelles',
    excerpt: 'Extrait',
    excerpt_description: 'Brève description ou extrait',
    image: 'Image',
    image_alt: 'Texte alternatif',
    reference: 'Document de référence',
    reference_required: 'La référence est requise pour le type référence',
    categories: 'Catégories',
    categories_description: 'Catégories pour le filtrage (saisie manuelle ou remplacement pour les références)',
    tags: 'Étiquettes',
    tags_description: 'Étiquettes pour le filtrage et la recherche',
    featured: 'À la une',
    featured_description: 'Marquer comme contenu à la une',
    link: 'Lien d’action',
    link_label: 'Libellé du lien',
    link_label_default: 'Lire la suite',
    link_url: 'URL',
    link_url_description: 'URL externe ou chemin interne',
    link_openInNewTab: 'Ouvrir dans un nouvel onglet',
    metadata: 'Métadonnées',
    metadata_author: 'Auteur',
    metadata_publishDate: 'Date de publication',
    metadata_priority: 'Priorité',
    metadata_priority_description: 'Les nombres plus élevés apparaissent en premier lors du tri par priorité',
    metadata_status: 'Statut',
    metadata_status_published: 'Publié',
    metadata_status_draft: 'Brouillon',
    metadata_status_archived: 'Archivé',
  },
  validation: {},
  descriptions: {},
} as const;
