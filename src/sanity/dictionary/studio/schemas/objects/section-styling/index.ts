import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.sectionStylingObjectTranslations,
  fr: fr.sectionStylingObjectTranslations,
});

export const sectionStylingObjectDict = dict;
export const createSectionStylingObjectField = createField;
export const getSectionStylingObjectTranslations = () => getTranslations();
export type SectionStylingObjectTranslations = typeof en.sectionStylingObjectTranslations;
