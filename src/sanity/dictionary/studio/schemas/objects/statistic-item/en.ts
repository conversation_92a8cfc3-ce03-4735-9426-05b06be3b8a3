// English translations for statistic item object
export const statisticItemObjectTranslations = {
  fields: {
    title: 'Statistic Item',
    label: 'Label',
    labelDesc: 'The label that describes this statistic',
    description: 'Description',
    descriptionDesc: 'Optional additional description or context',
    dataSource: 'Data Source',
    dataSourceDesc: 'Configure where this statistic gets its data',
    numberFormatting: 'Number Formatting',
    prefix: 'Prefix',
    prefixDesc: 'Text to show before the number (e.g., "$", "+", "#")',
    suffix: 'Suffix',
    suffixDesc: 'Text to show after the number (e.g., "%", "+", "K")',
    decimalPlaces: 'Decimal Places',
    decimalPlacesDesc: 'Number of decimal places to show',
    useThousandsSeparator: 'Use Thousands Separator',
    useThousandsSeparatorDesc: 'Show commas for thousands (1,000 vs 1000)',
    abbreviateLargeNumbers: 'Abbreviate Large Numbers',
    abbreviateLargeNumbersDesc: 'Show 1K, 1M, 1B instead of full numbers',
    animation: 'Animation Configuration',
    animationDesc: 'Configure how this statistic animates',
    icon: 'Icon',
    iconType: 'Icon Type',
    iconType_none: 'None',
    iconType_lucide: 'Lucide Icon',
    iconType_image: 'Custom Image',
    iconType_emoji: 'Emoji',
    lucideIcon: 'Lucide Icon Name',
    lucideIconDesc: 'Icon name from Lucide React (e.g., Users, Star, TrendingUp)',
    customImage: 'Custom Icon Image',
    emoji: 'Emoji',
    emojiDesc: 'Single emoji character',
    position: 'Icon Position',
    position_above: 'Above',
    position_left: 'Left',
    position_right: 'Right',
    styling: 'Custom Styling',
    numberColor: 'Number Color',
    numberColorDesc: 'Custom color for the number',
    labelColor: 'Label Color',
    labelColorDesc: 'Custom color for the label',
    numberSize: 'Number Size',
    numberSize_small: 'Small',
    numberSize_medium: 'Medium',
    numberSize_large: 'Large',
    numberSize_xl: 'Extra Large',
    numberSize_huge: 'Huge',
    fontWeight: 'Font Weight',
    fontWeight_normal: 'Normal',
    fontWeight_medium: 'Medium',
    fontWeight_semibold: 'Semibold',
    fontWeight_bold: 'Bold',
    fontWeight_extrabold: 'Extra Bold',
    untitled: 'Untitled Statistic',
  },
  validation: {},
  descriptions: {},
} as const;
