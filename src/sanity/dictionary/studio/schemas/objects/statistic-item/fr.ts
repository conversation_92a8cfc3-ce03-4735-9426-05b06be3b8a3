// French translations for statistic item object
export const statisticItemObjectTranslations = {
  fields: {
    title: 'Élément statistique',
    label: 'Libellé',
    labelDesc: 'Le libellé qui décrit cette statistique',
    description: 'Description',
    descriptionDesc: 'Description ou contexte supplémentaire (optionnel)',
    dataSource: 'Source de données',
    dataSourceDesc: 'Configurer la source de cette statistique',
    numberFormatting: 'Formatage des nombres',
    prefix: 'Préfixe',
    prefixDesc: 'Texte à afficher avant le nombre (ex : "$", "+", "#")',
    suffix: 'Suffixe',
    suffixDesc: 'Texte à afficher après le nombre (ex : "%", "+", "K")',
    decimalPlaces: 'Décimales',
    decimalPlacesDesc: 'Nombre de décimales à afficher',
    useThousandsSeparator: 'Séparateur de milliers',
    useThousandsSeparatorDesc: 'Afficher les virgules pour les milliers (1 000 vs 1000)',
    abbreviateLargeNumbers: 'Abréger les grands nombres',
    abbreviateLargeNumbersDesc: 'Afficher 1K, 1M, 1B au lieu du nombre complet',
    animation: 'Configuration de l’animation',
    animationDesc: 'Configurer l’animation de cette statistique',
    icon: 'Icône',
    iconType: 'Type d’icône',
    iconType_none: 'Aucune',
    iconType_lucide: 'Icône Lucide',
    iconType_image: 'Image personnalisée',
    iconType_emoji: 'Émoji',
    lucideIcon: 'Nom de l’icône Lucide',
    lucideIconDesc: 'Nom de l’icône Lucide React (ex : Users, Star, TrendingUp)',
    customImage: 'Image d’icône personnalisée',
    emoji: 'Émoji',
    emojiDesc: 'Caractère émoji unique',
    position: 'Position de l’icône',
    position_above: 'Au-dessus',
    position_left: 'Gauche',
    position_right: 'Droite',
    styling: 'Style personnalisé',
    numberColor: 'Couleur du nombre',
    numberColorDesc: 'Couleur personnalisée pour le nombre',
    labelColor: 'Couleur du libellé',
    labelColorDesc: 'Couleur personnalisée pour le libellé',
    numberSize: 'Taille du nombre',
    numberSize_small: 'Petite',
    numberSize_medium: 'Moyenne',
    numberSize_large: 'Grande',
    numberSize_xl: 'Très grande',
    numberSize_huge: 'Énorme',
    fontWeight: 'Graisse de police',
    fontWeight_normal: 'Normal',
    fontWeight_medium: 'Moyenne',
    fontWeight_semibold: 'Demi-gras',
    fontWeight_bold: 'Gras',
    fontWeight_extrabold: 'Extra gras',
    untitled: 'Statistique sans titre',
  },
  validation: {},
  descriptions: {},
} as const;
