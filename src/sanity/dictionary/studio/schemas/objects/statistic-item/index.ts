import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.statisticItemObjectTranslations,
  fr: fr.statisticItemObjectTranslations,
});

export const statisticItemObjectDict = dict;
export const createStatisticItemObjectField = createField;
export const getStatisticItemObjectTranslations = () => getTranslations();
export type StatisticItemObjectTranslations = typeof en.statisticItemObjectTranslations;
