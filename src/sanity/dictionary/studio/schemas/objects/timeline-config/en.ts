// English translations for timeline-config object schema
export const timelineConfigObjectTranslations = {
  fields: {
    title: 'Timeline Configuration',
    layout: 'Timeline Layout',
    layout_vertical: 'Vertical',
    layout_horizontal: 'Horizontal',
    layout_alternating: 'Alternating',
    layout_stepped: 'Stepped',
    layout_description: 'Choose the timeline layout style',
    visualStyle: 'Visual Style',
    visualStyle_dotStyle: 'Dot Style',
    visualStyle_dotStyle_circle: 'Circle',
    visualStyle_dotStyle_square: 'Square',
    visualStyle_dotStyle_diamond: 'Diamond',
    visualStyle_lineStyle: 'Connection Line Style',
    visualStyle_lineStyle_solid: 'Solid',
    visualStyle_lineStyle_dashed: 'Dashed',
    visualStyle_lineStyle_dotted: 'Dotted',
    visualStyle_lineStyle_gradient: 'Gradient',
    visualStyle_cardStyle: 'Card Style',
    visualStyle_cardStyle_clean: 'Clean',
    visualStyle_cardStyle_bordered: 'Bordered',
    visualStyle_cardStyle_shadowed: 'Shadowed',
    visualStyle_cardStyle_glass: 'Glass',
    visualStyle_colorScheme: 'Color Scheme',
    visualStyle_colorScheme_default: 'Default',
    visualStyle_colorScheme_monochrome: 'Monochrome',
    visualStyle_colorScheme_colorful: 'Colorful',
    visualStyle_colorScheme_brand: 'Brand Colors',
    animations: 'Animation Settings',
    animations_enableAnimations: 'Enable Animations',
    animations_animationType: 'Animation Type',
    animations_animationType_fadeIn: 'Fade In',
    animations_animationType_slideIn: 'Slide In',
    animations_animationType_scaleIn: 'Scale In',
    animations_animationType_progressive: 'Progressive Reveal',
    animations_staggerDelay: 'Stagger Delay (ms)',
    animations_staggerDelay_description: 'Delay between animating each step',
    animations_enableScrollProgress: 'Enable Scroll Progress',
    animations_enableScrollProgress_description: 'Show progress line that follows scroll position',
    responsiveSettings: 'Responsive Settings',
    responsiveSettings_mobileLayout: 'Mobile Layout Override',
    responsiveSettings_mobileLayout_same: 'Same as Desktop',
    responsiveSettings_mobileLayout_vertical: 'Force Vertical',
    responsiveSettings_mobileLayout_compact: 'Compact Cards',
    responsiveSettings_hideElementsOnMobile: 'Hide on Mobile',
    responsiveSettings_hideElementsOnMobile_icons: 'Timeline Icons',
    responsiveSettings_hideElementsOnMobile_dates: 'Dates',
    responsiveSettings_hideElementsOnMobile_assignees: 'Assignees',
    responsiveSettings_hideElementsOnMobile_progress: 'Progress Indicators',
    interactivity: 'Interactive Features',
    interactivity_expandableContent: 'Expandable Content',
    interactivity_expandableContent_description: 'Allow users to expand/collapse detailed content',
    interactivity_clickableSteps: 'Clickable Steps',
    interactivity_clickableSteps_description: 'Make timeline steps clickable for navigation',
    interactivity_showProgress: 'Show Overall Progress',
    interactivity_showProgress_description: 'Display overall completion percentage',
  },
  validation: {},
  descriptions: {},
} as const;
