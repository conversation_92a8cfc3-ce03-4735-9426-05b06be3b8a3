// French translations for timeline-config object schema
export const timelineConfigObjectTranslations = {
  fields: {
    title: 'Configuration de la frise chronologique',
    layout: 'Disposition de la frise',
    layout_vertical: 'Verticale',
    layout_horizontal: 'Horizontale',
    layout_alternating: 'Alternée',
    layout_stepped: 'En escalier',
    layout_description: 'Choisissez le style de disposition de la frise',
    visualStyle: 'Style visuel',
    visualStyle_dotStyle: 'Style du point',
    visualStyle_dotStyle_circle: 'Cercle',
    visualStyle_dotStyle_square: 'Carré',
    visualStyle_dotStyle_diamond: 'Losange',
    visualStyle_lineStyle: 'Style de la ligne de connexion',
    visualStyle_lineStyle_solid: 'Plein',
    visualStyle_lineStyle_dashed: 'Tirets',
    visualStyle_lineStyle_dotted: 'Pointillés',
    visualStyle_lineStyle_gradient: 'Dégradé',
    visualStyle_cardStyle: 'Style de carte',
    visualStyle_cardStyle_clean: 'Épuré',
    visualStyle_cardStyle_bordered: 'Bordé',
    visualStyle_cardStyle_shadowed: 'Ombre',
    visualStyle_cardStyle_glass: 'Verre',
    visualStyle_colorScheme: 'Palette de couleurs',
    visualStyle_colorScheme_default: 'Défaut',
    visualStyle_colorScheme_monochrome: 'Monochrome',
    visualStyle_colorScheme_colorful: 'Coloré',
    visualStyle_colorScheme_brand: 'Couleurs de la marque',
    animations: 'Paramètres d’animation',
    animations_enableAnimations: 'Activer les animations',
    animations_animationType: 'Type d’animation',
    animations_animationType_fadeIn: 'Fondu',
    animations_animationType_slideIn: 'Glissement',
    animations_animationType_scaleIn: 'Agrandissement',
    animations_animationType_progressive: 'Révélation progressive',
    animations_staggerDelay: 'Délai d’enchaînement (ms)',
    animations_staggerDelay_description: 'Délai entre chaque étape animée',
    animations_enableScrollProgress: 'Activer la progression au scroll',
    animations_enableScrollProgress_description: 'Afficher une ligne de progression suivant le scroll',
    responsiveSettings: 'Paramètres responsives',
    responsiveSettings_mobileLayout: 'Disposition mobile',
    responsiveSettings_mobileLayout_same: 'Comme sur ordinateur',
    responsiveSettings_mobileLayout_vertical: 'Forcer vertical',
    responsiveSettings_mobileLayout_compact: 'Cartes compactes',
    responsiveSettings_hideElementsOnMobile: 'Masquer sur mobile',
    responsiveSettings_hideElementsOnMobile_icons: 'Icônes de la frise',
    responsiveSettings_hideElementsOnMobile_dates: 'Dates',
    responsiveSettings_hideElementsOnMobile_assignees: 'Assignés',
    responsiveSettings_hideElementsOnMobile_progress: 'Indicateurs de progression',
    interactivity: 'Fonctionnalités interactives',
    interactivity_expandableContent: 'Contenu extensible',
    interactivity_expandableContent_description: 'Permettre d’étendre/réduire le contenu détaillé',
    interactivity_clickableSteps: 'Étapes cliquables',
    interactivity_clickableSteps_description: 'Permettre la navigation par étapes cliquables',
    interactivity_showProgress: 'Afficher la progression globale',
    interactivity_showProgress_description: 'Afficher le pourcentage d’avancement global',
  },
  validation: {},
  descriptions: {},
} as const;
