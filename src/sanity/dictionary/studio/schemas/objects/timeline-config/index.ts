import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.timelineConfigObjectTranslations,
  fr: fr.timelineConfigObjectTranslations,
});

export const timelineConfigObjectDict = dict;
export const createTimelineConfigObjectField = createField;
export const getTimelineConfigObjectTranslations = getTranslations;
export type TimelineConfigObjectTranslations = typeof en.timelineConfigObjectTranslations;
