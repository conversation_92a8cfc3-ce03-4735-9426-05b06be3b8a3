import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.videoObjectTranslations,
  fr: fr.videoObjectTranslations,
});

export const videoObjectDict = dict;
export const createVideoObjectField = createField;
export const getVideoObjectTranslations = () => getTranslations();
export type VideoObjectTranslations = typeof en.videoObjectTranslations;
