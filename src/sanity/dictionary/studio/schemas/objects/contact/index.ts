import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.contactObjectTranslations,
  fr: fr.contactObjectTranslations,
});

export const contactObjectDict = dict;
export const createContactObjectField = createField;
export const getContactObjectTranslations = getTranslations;
export type ContactObjectTranslations = typeof en.contactObjectTranslations;
