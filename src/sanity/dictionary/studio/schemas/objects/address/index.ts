import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.addressObjectTranslations,
  fr: fr.addressObjectTranslations,
});

export const addressObjectDict = dict;
export const createAddressObjectField = createField;
export const getAddressObjectTranslations = getTranslations;
export type AddressObjectTranslations = typeof en.addressObjectTranslations;
