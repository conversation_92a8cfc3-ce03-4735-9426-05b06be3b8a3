// English translations for address object
export const addressObjectTranslations = {
  fields: {
    title: 'Address',
    street: 'Street Address',
    postalCode: 'Postal Code',
    city: 'City',
    region: 'Region/Canton',
    country: 'Country',
    coordinates: 'GPS Coordinates',
  },
  validation: {
    streetRequired: 'Street address is required',
    postalCodeRequired: 'Postal code is required',
    cityRequired: 'City is required',
  },
  descriptions: {
    street: 'Street name and number',
    postalCode: 'Postal/ZIP code',
    city: 'City or town name',
    region: 'State, province, or canton',
    country: 'Country (default: Switzerland)',
    coordinates: 'Exact GPS coordinates for maps and location services',
  },
} as const;
