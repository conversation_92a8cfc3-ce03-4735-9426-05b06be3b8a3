// English translations for animation config object
export const animationConfigObjectTranslations = {
  fields: {
    title: 'Animation Configuration',
    animationType: 'Animation Type',
    animationType_countUp: 'Count Up',
    animationType_spring: 'Spring',
    animationType_bounce: 'Bounce',
    animationType_typewriter: 'Typewriter',
    animationType_fadeIn: 'Fade In',
    animationType_scaleUp: 'Scale Up',
    duration: 'Duration (seconds)',
    durationDesc: 'Animation duration in seconds',
    delay: 'Delay (seconds)',
    delayDesc: 'Delay before animation starts',
    trigger: 'Animation Trigger',
    trigger_inView: 'In View',
    trigger_load: 'Page Load',
    trigger_hover: 'Hover',
    trigger_manual: 'Manual',
    easing: 'Easing Function',
    easing_ease: 'Ease',
    easing_easeIn: 'Ease In',
    easing_easeOut: 'Ease Out',
    easing_easeInOut: 'Ease In Out',
    easing_linear: 'Linear',
    springConfig: 'Spring Configuration',
    mass: 'Mass',
    massDesc: 'Mass of the spring (heavier = slower)',
    stiffness: 'Stiffness',
    stiffnessDesc: 'Spring stiffness (higher = snappier)',
    damping: 'Damping',
    dampingDesc: 'Spring damping (higher = less oscillation)',
    viewportConfig: 'Viewport Configuration',
    threshold: 'Threshold (%)',
    thresholdDesc: 'Percentage of element visible before triggering',
    once: 'Animate Once',
    onceDesc: 'Only animate once when entering viewport',
  },
  validation: {},
  descriptions: {},
} as const;
