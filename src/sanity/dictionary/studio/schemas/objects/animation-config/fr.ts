// French translations for animation config object
export const animationConfigObjectTranslations = {
  fields: {
    title: 'Configuration d’animation',
    animationType: 'Type d’animation',
    animationType_countUp: 'Décompte',
    animationType_spring: 'Ressort',
    animationType_bounce: 'Rebond',
    animationType_typewriter: 'Machine à écrire',
    animationType_fadeIn: 'Fondu',
    animationType_scaleUp: 'Agrandir',
    duration: 'Durée (secondes)',
    durationDesc: 'Durée de l’animation en secondes',
    delay: '<PERSON><PERSON><PERSON> (secondes)',
    delayDesc: 'Délai avant le début de l’animation',
    trigger: 'Déclencheur d’animation',
    trigger_inView: 'À l’affichage',
    trigger_load: 'Au chargement',
    trigger_hover: 'Au survol',
    trigger_manual: 'Manuel',
    easing: 'Fonction d’assouplissement',
    easing_ease: 'Facile',
    easing_easeIn: 'Entrée facile',
    easing_easeOut: 'Sortie facile',
    easing_easeInOut: 'Entrée/Sortie facile',
    easing_linear: 'Linéaire',
    springConfig: 'Configuration du ressort',
    mass: 'Masse',
    massDesc: 'Masse du ressort (plus lourd = plus lent)',
    stiffness: 'Rigidité',
    stiffnessDesc: 'Rigidité du ressort (plus élevé = plus vif)',
    damping: 'Amortissement',
    dampingDesc: 'Amortissement du ressort (plus élevé = moins d’oscillation)',
    viewportConfig: 'Configuration de la fenêtre',
    threshold: 'Seuil (%)',
    thresholdDesc: 'Pourcentage de l’élément visible avant déclenchement',
    once: 'Animer une fois',
    onceDesc: 'N’animer qu’une seule fois à l’entrée dans la fenêtre',
  },
  validation: {},
  descriptions: {},
} as const;
