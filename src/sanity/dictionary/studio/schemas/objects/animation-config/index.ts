import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.animationConfigObjectTranslations,
  fr: fr.animationConfigObjectTranslations,
});

export const animationConfigObjectDict = dict;
export const createAnimationConfigObjectField = createField;
export const getAnimationConfigObjectTranslations = () => getTranslations();
export type AnimationConfigObjectTranslations = typeof en.animationConfigObjectTranslations;
