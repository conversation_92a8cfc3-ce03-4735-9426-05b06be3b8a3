import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.seoObjectTranslations,
  fr: fr.seoObjectTranslations,
});

export const seoObjectDict = dict;
export const createSeoObjectField = createField;
export const getSeoObjectTranslations = () => getTranslations();
export type SeoObjectTranslations = typeof en.seoObjectTranslations;
