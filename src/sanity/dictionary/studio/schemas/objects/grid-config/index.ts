import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.gridConfigObjectTranslations,
  fr: fr.gridConfigObjectTranslations,
});

export const gridConfigObjectDict = dict;
export const createGridConfigObjectField = createField;
export const getGridConfigObjectTranslations = getTranslations;
export type GridConfigObjectTranslations = typeof en.gridConfigObjectTranslations;
