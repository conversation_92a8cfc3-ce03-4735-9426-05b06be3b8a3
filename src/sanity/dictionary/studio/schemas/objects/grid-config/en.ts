// English translations for grid config object
export const gridConfigObjectTranslations = {
  fields: {
    columns: 'Grid Columns',
    'columns.mobile': 'Mobile Columns',
    'columns.tablet': 'Tablet Columns',
    'columns.desktop': 'Desktop Columns',
    gap: 'Grid Gap',
    'gap.none': 'None',
    'gap.xs': 'Extra Small',
    'gap.sm': 'Small',
    'gap.md': 'Medium',
    'gap.lg': 'Large',
    'gap.xl': 'Extra Large',
    'gap.xxl': 'XXL',
    alignment: 'Grid Items Alignment',
    'alignment.start': 'Start (Top)',
    'alignment.center': 'Center',
    'alignment.end': 'End (Bottom)',
    'alignment.stretch': 'Stretch',
    justifyContent: 'Grid Content Justification',
    'justifyContent.start': 'Start (Left)',
    'justifyContent.center': 'Center',
    'justifyContent.end': 'End (Right)',
    'justifyContent.between': 'Space Between',
    'justifyContent.around': 'Space Around',
    'justifyContent.evenly': 'Space Evenly',
  },
  validation: {},
  descriptions: {},
} as const;
