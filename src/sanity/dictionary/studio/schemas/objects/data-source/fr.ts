// French translations for data-source object schema
export const dataSourceObjectTranslations = {
  fields: {
    title: 'Source de données',
    description: 'Configurer la source des données',
    sourceType: 'Type de source de données',
    sourceType_description: 'Choisissez comment récupérer les données',
    sourceType_static: 'Valeur statique',
    sourceType_documents: 'Requête de document',
    sourceType_api: 'API externe',
    sourceType_groq: 'GROQ personnalisé',
    outputFormat: 'Format de sortie',
    outputFormat_description: 'Quel type de données cette source doit-elle retourner ?',
    outputFormat_number: 'Nombre/Compte',
    outputFormat_documents: 'Liste de documents',
    outputFormat_text: 'Texte/Chaîne',
    outputFormat_raw: 'Données brutes',
    staticConfig: 'Configuration statique',
    staticConfig_value: 'Valeur statique',
    staticConfig_value_description: 'Entrez la valeur statique (nombre, texte ou JSON)',
    documentConfig: 'Configuration de requête de document',
    documentConfig_documentType: 'Type de document',
    documentConfig_documentType_post: 'Articles',
    documentConfig_documentType_project: 'Projets',
    documentConfig_documentType_service: 'Services',
    documentConfig_documentType_testimonial: 'Témoignages',
    documentConfig_documentType_author: 'Auteurs',
    documentConfig_documentType_page: 'Pages',
    documentConfig_documentType_form: 'Formulaires',
    documentConfig_documentType_postCategory: 'Catégories d’articles',
    documentConfig_documentType_projectCategory: 'Catégories de projets',
    documentConfig_documentType_custom: 'Type personnalisé',
    documentConfig_customDocumentType: 'Type de document personnalisé',
    documentConfig_customDocumentType_description: 'Entrez le nom exact du type de document',
    documentConfig_filters: 'Filtres de requête',
    documentConfig_filters_description: 'Ajoutez des filtres pour affiner la requête',
    documentConfig_filters_field: 'Nom du champ',
    documentConfig_filters_field_placeholder: 'ex : _type, status, category._ref',
    documentConfig_filters_operator: 'Opérateur',
    documentConfig_filters_operator_eq: 'Égal',
    documentConfig_filters_operator_neq: 'Différent',
    documentConfig_filters_operator_gt: 'Supérieur à',
    documentConfig_filters_operator_lt: 'Inférieur à',
    documentConfig_filters_operator_gte: 'Supérieur ou égal',
    documentConfig_filters_operator_lte: 'Inférieur ou égal',
    documentConfig_filters_operator_match: 'Contient (correspondance)',
    documentConfig_filters_operator_in: 'Dans le tableau',
    documentConfig_filters_operator_references: 'Références',
    documentConfig_filters_value: 'Valeur',
    documentConfig_filters_value_description: 'La valeur à comparer',
    documentConfig_sortBy: 'Trier par',
    documentConfig_sortBy_field: 'Champ de tri',
    documentConfig_sortBy_field_placeholder: 'ex : _createdAt, publishedAt, title',
    documentConfig_sortBy_field_description: 'Champ à utiliser pour le tri',
    documentConfig_sortBy_order: 'Ordre de tri',
    documentConfig_sortBy_order_asc: 'Ascendant',
    documentConfig_sortBy_order_desc: 'Descendant',
    documentConfig_limit: 'Limiter les résultats',
    documentConfig_limit_description: 'Nombre maximum de résultats à retourner',
    documentConfig_projection: 'Champs à inclure',
    documentConfig_projection_placeholder: '{\n  _id,\n  title,\n  "imageUrl": image.asset->url\n}',
    documentConfig_projection_description: 'Objet de projection GROQ (optionnel) - quels champs retourner',
    apiConfig: 'Configuration API',
    apiConfig_endpoint: 'Point de terminaison API',
    apiConfig_endpoint_description: 'URL complète du point de terminaison API',
    apiConfig_method: 'Méthode HTTP',
    apiConfig_method_get: 'GET',
    apiConfig_method_post: 'POST',
    apiConfig_headers: 'En-têtes de requête',
    apiConfig_headers_key: 'Nom de l’en-tête',
    apiConfig_headers_value: 'Valeur de l’en-tête',
    apiConfig_requestBody: 'Corps de la requête (JSON)',
    apiConfig_requestBody_description: 'Corps JSON pour les requêtes POST',
    apiConfig_responseTransform: 'Transformation de la réponse',
    apiConfig_responseTransform_dataPath: 'Chemin des données',
    apiConfig_responseTransform_dataPath_placeholder: 'data.results ou stats.count',
    apiConfig_responseTransform_dataPath_description: 'Chemin vers les données dans la réponse API',
    apiConfig_responseTransform_transform: 'Fonction de transformation',
    apiConfig_responseTransform_transform_placeholder: '// Fonction JavaScript pour transformer les données\n// return data.length;',
    apiConfig_responseTransform_transform_description: 'JavaScript optionnel pour transformer les données extraites',
    apiConfig_cacheConfig: 'Mise en cache',
    apiConfig_cacheConfig_enabled: 'Activer la mise en cache',
    apiConfig_cacheConfig_duration: 'Durée du cache (minutes)',
    apiConfig_cacheConfig_duration_description: 'Durée en minutes',
    groqConfig: 'Configuration GROQ personnalisée',
    groqConfig_query: 'Requête GROQ',
    groqConfig_query_placeholder: '*[_type == "post" && publishedAt < now()] | order(publishedAt desc) [0...10]',
    groqConfig_query_description: 'Requête GROQ complète',
    groqConfig_params: 'Paramètres de requête',
    groqConfig_params_key: 'Nom du paramètre',
    groqConfig_params_key_placeholder: 'categoryId',
    groqConfig_params_value: 'Valeur du paramètre',
    groqConfig_params_value_placeholder: 'tech-news',
    groqConfig_params_type: 'Type de paramètre',
    groqConfig_params_type_string: 'Chaîne',
    groqConfig_params_type_number: 'Nombre',
    groqConfig_params_type_boolean: 'Booléen',
    groqConfig_params_type_reference: 'ID de référence',
    errorHandling: 'Gestion des erreurs',
    errorHandling_fallbackValue: 'Valeur de repli',
    errorHandling_fallbackValue_description: 'Valeur à utiliser si la source de données échoue',
    errorHandling_retryAttempts: 'Tentatives de réessai',
    errorHandling_retryAttempts_description: 'Nombre de tentatives de réessai en cas d’échec',
    errorHandling_timeout: 'Délai d’attente (secondes)',
    errorHandling_timeout_description: 'Délai d’attente de la requête en secondes'
  },
  validation: {},
  descriptions: {},
} as const;
