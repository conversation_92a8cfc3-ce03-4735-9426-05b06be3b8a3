// English translations for data-source object schema
export const dataSourceObjectTranslations = {
  fields: {
    title: 'Data Source',
    description: 'Configure where to fetch data from',
    sourceType: 'Data Source Type',
    sourceType_description: 'Choose how to fetch the data',
    sourceType_static: 'Static Value',
    sourceType_documents: 'Document Query',
    sourceType_api: 'External API',
    sourceType_groq: 'Custom GROQ',
    outputFormat: 'Output Format',
    outputFormat_description: 'What type of data should this source return?',
    outputFormat_number: 'Number/Count',
    outputFormat_documents: 'Document List',
    outputFormat_text: 'Text/String',
    outputFormat_raw: 'Raw Data',
    staticConfig: 'Static Configuration',
    staticConfig_value: 'Static Value',
    staticConfig_value_description: 'Enter the static value (number, text, or JSON)',
    documentConfig: 'Document Query Configuration',
    documentConfig_documentType: 'Document Type',
    documentConfig_documentType_post: 'Posts',
    documentConfig_documentType_project: 'Projects',
    documentConfig_documentType_service: 'Services',
    documentConfig_documentType_testimonial: 'Testimonials',
    documentConfig_documentType_author: 'Authors',
    documentConfig_documentType_page: 'Pages',
    documentConfig_documentType_form: 'Forms',
    documentConfig_documentType_postCategory: 'Post Categories',
    documentConfig_documentType_projectCategory: 'Project Categories',
    documentConfig_documentType_custom: 'Custom Type',
    documentConfig_customDocumentType: 'Custom Document Type',
    documentConfig_customDocumentType_description: 'Enter the exact document type name',
    documentConfig_filters: 'Query Filters',
    documentConfig_filters_description: 'Add filters to refine the query',
    documentConfig_filters_field: 'Field Name',
    documentConfig_filters_field_placeholder: 'e.g., _type, status, category._ref',
    documentConfig_filters_operator: 'Operator',
    documentConfig_filters_operator_eq: 'Equals',
    documentConfig_filters_operator_neq: 'Not Equals',
    documentConfig_filters_operator_gt: 'Greater Than',
    documentConfig_filters_operator_lt: 'Less Than',
    documentConfig_filters_operator_gte: 'Greater or Equal',
    documentConfig_filters_operator_lte: 'Less or Equal',
    documentConfig_filters_operator_match: 'Contains (match)',
    documentConfig_filters_operator_in: 'In Array',
    documentConfig_filters_operator_references: 'References',
    documentConfig_filters_value: 'Value',
    documentConfig_filters_value_description: 'The value to compare against',
    documentConfig_sortBy: 'Sort By',
    documentConfig_sortBy_field: 'Sort Field',
    documentConfig_sortBy_field_placeholder: 'e.g., _createdAt, publishedAt, title',
    documentConfig_sortBy_field_description: 'Field to sort by',
    documentConfig_sortBy_order: 'Sort Order',
    documentConfig_sortBy_order_asc: 'Ascending',
    documentConfig_sortBy_order_desc: 'Descending',
    documentConfig_limit: 'Limit Results',
    documentConfig_limit_description: 'Maximum number of results to return',
    documentConfig_projection: 'Fields to Include',
    documentConfig_projection_placeholder: '{\n  _id,\n  title,\n  "imageUrl": image.asset->url\n}',
    documentConfig_projection_description: 'GROQ projection object (optional) - what fields to return',
    apiConfig: 'API Configuration',
    apiConfig_endpoint: 'API Endpoint',
    apiConfig_endpoint_description: 'Full URL to the API endpoint',
    apiConfig_method: 'HTTP Method',
    apiConfig_method_get: 'GET',
    apiConfig_method_post: 'POST',
    apiConfig_headers: 'Request Headers',
    apiConfig_headers_key: 'Header Name',
    apiConfig_headers_value: 'Header Value',
    apiConfig_requestBody: 'Request Body (JSON)',
    apiConfig_requestBody_description: 'JSON body for POST requests',
    apiConfig_responseTransform: 'Response Transform',
    apiConfig_responseTransform_dataPath: 'Data Path',
    apiConfig_responseTransform_dataPath_placeholder: 'data.results or stats.count',
    apiConfig_responseTransform_dataPath_description: 'Path to the data in the API response',
    apiConfig_responseTransform_transform: 'Transform Function',
    apiConfig_responseTransform_transform_placeholder: '// JavaScript function to transform the data\n// return data.length;',
    apiConfig_responseTransform_transform_description: 'Optional JavaScript to transform the extracted data',
    apiConfig_cacheConfig: 'Caching',
    apiConfig_cacheConfig_enabled: 'Enable Caching',
    apiConfig_cacheConfig_duration: 'Cache Duration (minutes)',
    apiConfig_cacheConfig_duration_description: 'Duration in minutes',
    groqConfig: 'Custom GROQ Configuration',
    groqConfig_query: 'GROQ Query',
    groqConfig_query_placeholder: '*[_type == "post" && publishedAt < now()] | order(publishedAt desc) [0...10]',
    groqConfig_query_description: 'Complete GROQ query',
    groqConfig_params: 'Query Parameters',
    groqConfig_params_key: 'Parameter Name',
    groqConfig_params_key_placeholder: 'categoryId',
    groqConfig_params_value: 'Parameter Value',
    groqConfig_params_value_placeholder: 'tech-news',
    groqConfig_params_type: 'Parameter Type',
    groqConfig_params_type_string: 'String',
    groqConfig_params_type_number: 'Number',
    groqConfig_params_type_boolean: 'Boolean',
    groqConfig_params_type_reference: 'Reference ID',
    errorHandling: 'Error Handling',
    errorHandling_fallbackValue: 'Fallback Value',
    errorHandling_fallbackValue_description: 'Value to use if the data source fails',
    errorHandling_retryAttempts: 'Retry Attempts',
    errorHandling_retryAttempts_description: 'Number of times to retry failed requests',
    errorHandling_timeout: 'Timeout (seconds)',
    errorHandling_timeout_description: 'Request timeout in seconds'
  },
  validation: {},
  descriptions: {},
} as const;
