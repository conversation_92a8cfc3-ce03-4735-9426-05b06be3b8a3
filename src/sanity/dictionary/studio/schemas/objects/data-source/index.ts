import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.dataSourceObjectTranslations,
  fr: fr.dataSourceObjectTranslations,
});

export const dataSourceObjectDict = dict;
export const createDataSourceObjectField = createField;
export const getDataSourceObjectTranslations = getTranslations;
export type DataSourceObjectTranslations = typeof en.dataSourceObjectTranslations;
