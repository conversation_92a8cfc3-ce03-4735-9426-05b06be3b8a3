import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.headingObjectTranslations,
  fr: fr.headingObjectTranslations,
});

export const headingObjectDict = dict;
export const createHeadingObjectField = createField;
export const getHeadingObjectTranslations = () => getTranslations();
export type HeadingObjectTranslations = typeof en.headingObjectTranslations;
