// English translations for carousel config object
export const carouselConfigObjectTranslations = {
  fields: {
    title: 'Carousel Configuration',
    description: 'Comprehensive carousel configuration using Embla Carousel',
    showNavigation: 'Show Navigation Arrows',
    showNavigationDesc: 'Display previous/next navigation buttons',
    showDots: 'Show Dot Indicators',
    showDotsDesc: 'Display dot navigation indicators',
    showThumbnails: 'Show Thumbnails',
    showThumbnailsDesc: 'Display thumbnail navigation (for images/media)',
    carouselBehavior: 'Carousel Behavior',
    loop: 'Loop',
    loopDesc: 'Enable infinite looping',
    align: 'Slide Alignment',
    align_start: 'Start',
    align_center: 'Center',
    align_end: 'End',
    slidesToScroll: 'Slides to Scroll',
    slidesToScroll_auto: 'Auto',
    slidesToScroll_1: '1',
    slidesToScroll_2: '2',
    slidesToScroll_3: '3',
    dragFree: 'Drag Free',
    dragFreeDesc: 'Allow free dragging without snapping to slides',
    slidesDisplay: 'Slides Display',
    slidesPerView: 'Slides Per View',
    mobile: 'Mobile',
    tablet: 'Tablet',
    desktop: 'Desktop',
    slideSpacing: 'Slide Spacing',
    slideSpacing_none: 'None',
    slideSpacing_small: 'Small',
    slideSpacing_medium: 'Medium',
    slideSpacing_large: 'Large',
    autoplay: 'Autoplay Settings',
    enableAutoplay: 'Enable Autoplay',
    autoplayDelay: 'Autoplay Delay (ms)',
    stopOnInteraction: 'Stop on User Interaction',
    transitionEffect: 'Transition Effect',
    effectType: 'Effect Type',
    effectType_slide: 'Slide (Default)',
    effectType_fade: 'Fade',
    transitionDuration: 'Transition Duration (ms)',
    preview_basic: 'Basic carousel',
    preview_navigation: 'Navigation',
    preview_dots: 'Dots',
    preview_loop: 'Loop',
    preview_autoplay: 'Autoplay',
    preview_fade: 'Fade',
  },
  validation: {},
  descriptions: {},
} as const;
