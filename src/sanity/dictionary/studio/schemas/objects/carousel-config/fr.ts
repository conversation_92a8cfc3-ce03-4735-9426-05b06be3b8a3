// French translations for carousel config object
export const carouselConfigObjectTranslations = {
  fields: {
    title: 'Configuration du carrousel',
    description: 'Configuration complète du carrousel avec Embla Carousel',
    showNavigation: 'Afficher les flèches de navigation',
    showNavigationDesc: 'Afficher les boutons précédent/suivant',
    showDots: 'Afficher les indicateurs',
    showDotsDesc: 'Afficher les indicateurs de navigation',
    showThumbnails: 'Afficher les miniatures',
    showThumbnailsDesc: 'Afficher la navigation par miniatures (pour images/médias)',
    carouselBehavior: 'Comportement du carrousel',
    loop: 'Boucle',
    loopDesc: 'Activer la boucle infinie',
    align: 'Alignement des diapositives',
    align_start: 'Début',
    align_center: 'Centre',
    align_end: 'Fin',
    slidesToScroll: 'Diapositives à faire défiler',
    slidesToScroll_auto: 'Auto',
    slidesToScroll_1: '1',
    slidesToScroll_2: '2',
    slidesToScroll_3: '3',
    dragFree: 'Glisser librement',
    dragFreeDesc: 'Permettre le glissement libre sans accrochage',
    slidesDisplay: 'Affichage des diapositives',
    slidesPerView: 'Diapositives par vue',
    mobile: 'Mobile',
    tablet: 'Tablette',
    desktop: 'Bureau',
    slideSpacing: 'Espacement des diapositives',
    slideSpacing_none: 'Aucun',
    slideSpacing_small: 'Petit',
    slideSpacing_medium: 'Moyen',
    slideSpacing_large: 'Grand',
    autoplay: 'Paramètres d’autoplay',
    enableAutoplay: 'Activer l’autoplay',
    autoplayDelay: 'Délai d’autoplay (ms)',
    stopOnInteraction: 'Arrêter lors d’une interaction',
    transitionEffect: 'Effet de transition',
    effectType: 'Type d’effet',
    effectType_slide: 'Défilement (par défaut)',
    effectType_fade: 'Fondu',
    transitionDuration: 'Durée de transition (ms)',
    preview_basic: 'Carrousel basique',
    preview_navigation: 'Navigation',
    preview_dots: 'Points',
    preview_loop: 'Boucle',
    preview_autoplay: 'Autoplay',
    preview_fade: 'Fondu',
  },
  validation: {},
  descriptions: {},
} as const;
