import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.carouselConfigObjectTranslations,
  fr: fr.carouselConfigObjectTranslations,
});

export const carouselConfigDict = dict;
export const createCarouselConfigField = createField;
export const getCarouselConfigTranslations = () => getTranslations();
export type CarouselConfigTranslations = typeof en.carouselConfigObjectTranslations;
