import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.filterConfigObjectTranslations,
  fr: fr.filterConfigObjectTranslations,
});

export const filterConfigObjectDict = dict;
export const createFilterConfigObjectField = createField;
export const getFilterConfigObjectTranslations = getTranslations;
export type FilterConfigObjectTranslations = typeof en.filterConfigObjectTranslations;
