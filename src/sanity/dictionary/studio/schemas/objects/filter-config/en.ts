// English translations for filter config object
export const filterConfigObjectTranslations = {
  fields: {
    enableSearch: 'Enable Search',
    searchPlaceholder: 'Search Placeholder',
    enableCategoryFilter: 'Enable Category Filter',
    enableTagFilter: 'Enable Tag Filter',
    enableStatusFilter: 'Enable Status Filter',
    enableFeaturedFilter: 'Enable Featured Filter',
    defaultSort: 'Default Sort Order',
    'defaultSort.date-desc': 'Most Recent',
    'defaultSort.date-asc': 'Oldest First',
    'defaultSort.title-asc': 'Alphabetical (A-Z)',
    'defaultSort.title-desc': 'Alphabetical (Z-A)',
    'defaultSort.priority-desc': 'Priority (High to Low)',
    'defaultSort.featured-first': 'Featured First',
    showItemCount: 'Show Item Count',
    enableUrlFilters: 'Enable URL Filters',
    previewTitle: 'Filter Configuration',
    previewSearch: 'Search',
    previewCategories: 'Categories',
    previewTags: 'Tags',
    previewSort: 'Sort',
  },
  validation: {},
  descriptions: {
    enableSearch: 'Allow users to search through content',
    searchPlaceholder: 'Placeholder text for search input',
    enableCategoryFilter: 'Show category filter buttons',
    enableTagFilter: 'Show tag filter dropdown',
    enableStatusFilter: 'Show status filter (published, draft, archived)',
    enableFeaturedFilter: 'Show "Featured Only" toggle',
    defaultSort: 'Default sort order for items',
    showItemCount: 'Display number of items found',
    enableUrlFilters: 'Save filter state in URL for sharing',
  },
} as const;
