// French translations for filter config object
export const filterConfigObjectTranslations = {
  fields: {
    enableSearch: 'Activer la recherche',
    searchPlaceholder: 'Texte indicatif de recherche',
    enableCategoryFilter: 'Activer le filtre de catégorie',
    enableTagFilter: 'Activer le filtre de tag',
    enableStatusFilter: 'Activer le filtre de statut',
    enableFeaturedFilter: 'Activer le filtre "En vedette"',
    defaultSort: 'Ordre de tri par défaut',
    'defaultSort.date-desc': 'Plus récent',
    'defaultSort.date-asc': 'Plus ancien',
    'defaultSort.title-asc': 'Alphabétique (A-Z)',
    'defaultSort.title-desc': 'Alphabétique (Z-A)',
    'defaultSort.priority-desc': 'Priorité (haut-bas)',
    'defaultSort.featured-first': 'En vedette d’abord',
    showItemCount: 'Afficher le nombre d’éléments',
    enableUrlFilters: 'Activer les filtres d’URL',
    previewTitle: 'Configuration du filtre',
    previewSearch: 'Recherche',
    previewCategories: 'Catégories',
    previewTags: 'Tags',
    previewSort: 'Tri',
  },
  validation: {},
  descriptions: {
    enableSearch: 'Permettre aux utilisateurs de rechercher dans le contenu',
    searchPlaceholder: 'Texte indicatif pour la recherche',
    enableCategoryFilter: 'Afficher les boutons de filtre de catégorie',
    enableTagFilter: 'Afficher le menu déroulant de filtre de tag',
    enableStatusFilter: 'Afficher le filtre de statut (publié, brouillon, archivé)',
    enableFeaturedFilter: 'Afficher le bouton "En vedette seulement"',
    defaultSort: 'Ordre de tri par défaut des éléments',
    showItemCount: 'Afficher le nombre d’éléments trouvés',
    enableUrlFilters: 'Enregistrer l’état du filtre dans l’URL pour le partage',
  },
} as const;
