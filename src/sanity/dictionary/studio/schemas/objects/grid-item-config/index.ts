import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.gridItemConfigObjectTranslations,
  fr: fr.gridItemConfigObjectTranslations,
});

export const gridItemConfigObjectDict = dict;
export const createGridItemConfigObjectField = createField;
export const getGridItemConfigObjectTranslations = getTranslations;
export type GridItemConfigObjectTranslations = typeof en.gridItemConfigObjectTranslations;
