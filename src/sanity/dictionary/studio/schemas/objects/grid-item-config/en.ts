// English translations for grid item config object
export const gridItemConfigObjectTranslations = {
  fields: {
    columnSpan: 'Column Span',
    'columnSpan.mobile': 'Mobile',
    'columnSpan.tablet': 'Tablet',
    'columnSpan.desktop': 'Desktop',
    rowSpan: 'Row Span',
    startColumn: 'Start Column',
    'startColumn.mobile': 'Mobile',
    'startColumn.tablet': 'Tablet',
    'startColumn.desktop': 'Desktop',
    startRow: 'Start Row',
    'startRow.mobile': 'Mobile',
    'startRow.tablet': 'Tablet',
    'startRow.desktop': 'Desktop',
    order: 'Display Order',
    'order.mobile': 'Mobile Order',
    'order.tablet': 'Tablet Order',
    'order.desktop': 'Desktop Order',
    alignment: 'Item Alignment',
    'alignment.inherit': 'Default (inherit from section)',
    'alignment.start': 'Top',
    'alignment.center': 'Center',
    'alignment.end': 'Bottom',
    'alignment.stretch': 'Stretch',
    customStyling: 'Custom Styling',
    'customStyling.backgroundColor': 'Background Color',
    'customStyling.textColor': 'Text Color',
    'customStyling.padding': 'Internal Padding',
    'customStyling.padding.none': 'None',
    'customStyling.padding.small': 'Small',
    'customStyling.padding.medium': 'Medium',
    'customStyling.padding.large': 'Large',
    'customStyling.padding.xlarge': 'X-Large',
    'customStyling.borderRadius': 'Border Radius',
    'customStyling.borderRadius.none': 'None',
    'customStyling.borderRadius.sm': 'Small',
    'customStyling.borderRadius.md': 'Medium',
    'customStyling.borderRadius.lg': 'Large',
    'customStyling.borderRadius.full': 'Full',
    'customStyling.shadowSm': 'Drop Shadow',
    'customStyling.shadow.none': 'None',
    'customStyling.shadow.sm': 'Small',
    'customStyling.shadow.md': 'Medium',
    'customStyling.shadow.lg': 'Large',
    'customStyling.shadow.xl': 'Extra Large',
  },
  validation: {},
  descriptions: {},
} as const;
