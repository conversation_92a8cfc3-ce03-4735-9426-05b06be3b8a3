import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.richTextObjectTranslations,
  fr: fr.richTextObjectTranslations,
});

export const richTextObjectDict = dict;
export const createRichTextObjectField = createField;
export const getRichTextObjectTranslations = getTranslations;
export type RichTextObjectTranslations = typeof en.richTextObjectTranslations;
