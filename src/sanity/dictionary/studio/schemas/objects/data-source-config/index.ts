import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.dataSourceConfigObjectTranslations,
  fr: fr.dataSourceConfigObjectTranslations,
});

export const dataSourceConfigObjectDict = dict;
export const createDataSourceConfigObjectField = createField;
export const getDataSourceConfigObjectTranslations = getTranslations;
export type DataSourceConfigObjectTranslations = typeof en.dataSourceConfigObjectTranslations;
