// French translations for data-source-config object schema
export const dataSourceConfigObjectTranslations = {
  fields: {
    title: 'Configuration de la source de données',
    description: 'Configurer la provenance des données statistiques',
    sourceType: 'Type de source de données',
    sourceType_description: 'Choisissez comment calculer cette statistique',
    sourceType_static: 'Nombre statique',
    sourceType_documentCount: 'Nombre de documents',
    sourceType_fieldSum: 'Somme d’un champ',
    sourceType_fieldAverage: 'Moyenne d’un champ',
    sourceType_api: 'API externe',
    sourceType_groq: 'Requête GROQ personnalisée',
    staticValue: 'Valeur statique',
    staticValue_description: 'Entrez une valeur numérique fixe',
    documentCountConfig: 'Configuration du comptage de documents',
    documentCountConfig_documentType: 'Type de document',
    documentCountConfig_documentType_post: 'Articles',
    documentCountConfig_documentType_project: 'Projets',
    documentCountConfig_documentType_service: 'Services',
    documentCountConfig_documentType_testimonial: 'Témoignages',
    documentCountConfig_documentType_author: 'Auteurs',
    documentCountConfig_documentType_page: 'Pages',
    documentCountConfig_documentType_form: 'Formulaires',
    documentCountConfig_documentType_custom: 'Type personnalisé',
    documentCountConfig_customDocumentType: 'Type de document personnalisé',
    documentCountConfig_customDocumentType_description: 'Entrez le nom exact du type de document',
    documentCountConfig_filters: 'Filtres de requête',
    documentCountConfig_filters_description: 'Ajoutez des filtres pour affiner la requête',
    documentCountConfig_filters_field: 'Nom du champ',
    documentCountConfig_filters_field_placeholder: 'ex : _type, status, category._ref',
    documentCountConfig_filters_operator: 'Opérateur',
    documentCountConfig_filters_operator_eq: 'Égal',
    documentCountConfig_filters_operator_neq: 'Différent',
    documentCountConfig_filters_operator_gt: 'Supérieur à',
    documentCountConfig_filters_operator_lt: 'Inférieur à',
    documentCountConfig_filters_operator_gte: 'Supérieur ou égal',
    documentCountConfig_filters_operator_lte: 'Inférieur ou égal',
    documentCountConfig_filters_operator_match: 'Contient (correspondance)',
    documentCountConfig_filters_operator_in: 'Dans le tableau',
    documentCountConfig_filters_operator_references: 'Références',
    documentCountConfig_filters_value: 'Valeur',
    documentCountConfig_filters_value_description: 'La valeur à comparer',
    fieldSumConfig: 'Configuration de la somme d’un champ',
    fieldSumConfig_field: 'Champ à sommer',
    fieldSumConfig_field_description: 'Nom du champ à sommer',
    fieldAverageConfig: 'Configuration de la moyenne d’un champ',
    fieldAverageConfig_field: 'Champ à moyenner',
    fieldAverageConfig_field_description: 'Nom du champ à moyenner',
    apiConfig: 'Configuration API',
    apiConfig_endpoint: 'Point de terminaison API',
    apiConfig_endpoint_description: 'URL complète du point de terminaison API',
    apiConfig_method: 'Méthode HTTP',
    apiConfig_method_get: 'GET',
    apiConfig_method_post: 'POST',
    apiConfig_headers: 'En-têtes de requête',
    apiConfig_headers_key: 'Nom de l’en-tête',
    apiConfig_headers_value: 'Valeur de l’en-tête',
    apiConfig_requestBody: 'Corps de la requête (JSON)',
    apiConfig_requestBody_description: 'Corps JSON pour les requêtes POST',
    apiConfig_responseTransform: 'Transformation de la réponse',
    apiConfig_responseTransform_dataPath: 'Chemin des données',
    apiConfig_responseTransform_dataPath_placeholder: 'data.results ou stats.count',
    apiConfig_responseTransform_dataPath_description: 'Chemin vers les données dans la réponse API',
    apiConfig_responseTransform_transform: 'Fonction de transformation',
    apiConfig_responseTransform_transform_placeholder: '// Fonction JavaScript pour transformer les données\n// return data.length;',
    apiConfig_responseTransform_transform_description: 'JavaScript optionnel pour transformer les données extraites',
    apiConfig_cacheConfig: 'Mise en cache',
    apiConfig_cacheConfig_enabled: 'Activer la mise en cache',
    apiConfig_cacheConfig_duration: 'Durée du cache (minutes)',
    apiConfig_cacheConfig_duration_description: 'Durée en minutes',
    groqConfig: 'Configuration GROQ personnalisée',
    groqConfig_query: 'Requête GROQ',
    groqConfig_query_placeholder: '*[_type == "post" && publishedAt < now()] | order(publishedAt desc) [0...10]',
    groqConfig_query_description: 'Requête GROQ complète',
    groqConfig_params: 'Paramètres de requête',
    groqConfig_params_key: 'Nom du paramètre',
    groqConfig_params_key_placeholder: 'categoryId',
    groqConfig_params_value: 'Valeur du paramètre',
    groqConfig_params_value_placeholder: 'tech-news',
    groqConfig_params_type: 'Type de paramètre',
    groqConfig_params_type_string: 'Chaîne',
    groqConfig_params_type_number: 'Nombre',
    groqConfig_params_type_boolean: 'Booléen',
    groqConfig_params_type_reference: 'ID de référence',
    errorHandling: 'Gestion des erreurs',
    errorHandling_fallbackValue: 'Valeur de repli',
    errorHandling_fallbackValue_description: 'Valeur à utiliser si la source de données échoue',
    errorHandling_retryAttempts: 'Tentatives de réessai',
    errorHandling_retryAttempts_description: 'Nombre de tentatives de réessai en cas d’échec',
    errorHandling_timeout: 'Délai d’attente (secondes)',
    errorHandling_timeout_description: 'Délai d’attente de la requête en secondes'
  },
  validation: {},
  descriptions: {},
} as const;
