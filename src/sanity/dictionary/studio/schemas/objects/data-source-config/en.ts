// English translations for data-source-config object schema
export const dataSourceConfigObjectTranslations = {
  fields: {
    title: 'Data Source Configuration',
    description: 'Configure where the statistic data comes from',
    sourceType: 'Data Source Type',
    sourceType_description: 'Choose how to calculate this statistic',
    sourceType_static: 'Static Number',
    sourceType_documentCount: 'Document Count',
    sourceType_fieldSum: 'Field Sum',
    sourceType_fieldAverage: 'Field Average',
    sourceType_api: 'External API',
    sourceType_groq: 'Custom GROQ Query',
    staticValue: 'Static Value',
    staticValue_description: 'Enter a fixed number value',
    documentCountConfig: 'Document Count Configuration',
    documentCountConfig_documentType: 'Document Type',
    documentCountConfig_documentType_post: 'Posts',
    documentCountConfig_documentType_project: 'Projects',
    documentCountConfig_documentType_service: 'Services',
    documentCountConfig_documentType_testimonial: 'Testimonials',
    documentCountConfig_documentType_author: 'Authors',
    documentCountConfig_documentType_page: 'Pages',
    documentCountConfig_documentType_form: 'Forms',
    documentCountConfig_documentType_custom: 'Custom Type',
    documentCountConfig_customDocumentType: 'Custom Document Type',
    documentCountConfig_customDocumentType_description: 'Enter the exact document type name',
    documentCountConfig_filters: 'Query Filters',
    documentCountConfig_filters_description: 'Add filters to refine the query',
    documentCountConfig_filters_field: 'Field Name',
    documentCountConfig_filters_field_placeholder: 'e.g., _type, status, category._ref',
    documentCountConfig_filters_operator: 'Operator',
    documentCountConfig_filters_operator_eq: 'Equals',
    documentCountConfig_filters_operator_neq: 'Not Equals',
    documentCountConfig_filters_operator_gt: 'Greater Than',
    documentCountConfig_filters_operator_lt: 'Less Than',
    documentCountConfig_filters_operator_gte: 'Greater or Equal',
    documentCountConfig_filters_operator_lte: 'Less or Equal',
    documentCountConfig_filters_operator_match: 'Contains (match)',
    documentCountConfig_filters_operator_in: 'In Array',
    documentCountConfig_filters_operator_references: 'References',
    documentCountConfig_filters_value: 'Value',
    documentCountConfig_filters_value_description: 'The value to compare against',
    fieldSumConfig: 'Field Sum Configuration',
    fieldSumConfig_field: 'Field to Sum',
    fieldSumConfig_field_description: 'Name of the field to sum',
    fieldAverageConfig: 'Field Average Configuration',
    fieldAverageConfig_field: 'Field to Average',
    fieldAverageConfig_field_description: 'Name of the field to average',
    apiConfig: 'API Configuration',
    apiConfig_endpoint: 'API Endpoint',
    apiConfig_endpoint_description: 'Full URL to the API endpoint',
    apiConfig_method: 'HTTP Method',
    apiConfig_method_get: 'GET',
    apiConfig_method_post: 'POST',
    apiConfig_headers: 'Request Headers',
    apiConfig_headers_key: 'Header Name',
    apiConfig_headers_value: 'Header Value',
    apiConfig_requestBody: 'Request Body (JSON)',
    apiConfig_requestBody_description: 'JSON body for POST requests',
    apiConfig_responseTransform: 'Response Transform',
    apiConfig_responseTransform_dataPath: 'Data Path',
    apiConfig_responseTransform_dataPath_placeholder: 'data.results or stats.count',
    apiConfig_responseTransform_dataPath_description: 'Path to the data in the API response',
    apiConfig_responseTransform_transform: 'Transform Function',
    apiConfig_responseTransform_transform_placeholder: '// JavaScript function to transform the data\n// return data.length;',
    apiConfig_responseTransform_transform_description: 'Optional JavaScript to transform the extracted data',
    apiConfig_cacheConfig: 'Caching',
    apiConfig_cacheConfig_enabled: 'Enable Caching',
    apiConfig_cacheConfig_duration: 'Cache Duration (minutes)',
    apiConfig_cacheConfig_duration_description: 'Duration in minutes',
    groqConfig: 'Custom GROQ Configuration',
    groqConfig_query: 'GROQ Query',
    groqConfig_query_placeholder: '*[_type == "post" && publishedAt < now()] | order(publishedAt desc) [0...10]',
    groqConfig_query_description: 'Complete GROQ query',
    groqConfig_params: 'Query Parameters',
    groqConfig_params_key: 'Parameter Name',
    groqConfig_params_key_placeholder: 'categoryId',
    groqConfig_params_value: 'Parameter Value',
    groqConfig_params_value_placeholder: 'tech-news',
    groqConfig_params_type: 'Parameter Type',
    groqConfig_params_type_string: 'String',
    groqConfig_params_type_number: 'Number',
    groqConfig_params_type_boolean: 'Boolean',
    groqConfig_params_type_reference: 'Reference ID',
    errorHandling: 'Error Handling',
    errorHandling_fallbackValue: 'Fallback Value',
    errorHandling_fallbackValue_description: 'Value to use if the data source fails',
    errorHandling_retryAttempts: 'Retry Attempts',
    errorHandling_retryAttempts_description: 'Number of times to retry failed requests',
    errorHandling_timeout: 'Timeout (seconds)',
    errorHandling_timeout_description: 'Request timeout in seconds'
  },
  validation: {},
  descriptions: {},
} as const;
