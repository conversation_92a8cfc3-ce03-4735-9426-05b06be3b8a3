import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.spacerObjectTranslations,
  fr: fr.spacerObjectTranslations,
});

export const spacerObjectDict = dict;
export const createSpacerObjectField = createField;
export const getSpacerObjectTranslations = () => getTranslations();
export type SpacerObjectTranslations = typeof en.spacerObjectTranslations;
