import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.projectsPageTranslations,
  fr: fr.projectsPageTranslations,
});

export const projectsPageDict = dict;
export const createProjectsPageField = createField;
export const getProjectsPageTranslations = getTranslations;
export type ProjectsPageTranslations = typeof en.projectsPageTranslations;
