import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.servicesPageTranslations,
  fr: fr.servicesPageTranslations,
});

export const servicesPageDict = dict;
export const createServicesPageField = createField;
export const getServicesPageTranslations = getTranslations;
export type ServicesPageTranslations = typeof en.servicesPageTranslations;
