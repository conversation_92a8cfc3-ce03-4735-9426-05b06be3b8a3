import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.blogPageTranslations,
  fr: fr.blogPageTranslations,
});

export const blogPageDict = dict;
export const createBlogPageField = createField;
export const getBlogPageTranslations = getTranslations;
export type BlogPageTranslations = typeof en.blogPageTranslations;
