export const navigationSettingsTranslations = {
  document: { name: 'navigationSettings', title: 'Navigation Settings' },
  fields: {
    navbarMenuItems: 'Menu Items',
    menuItemType: 'Menu Item Type',
    menuItemTypeSingle: 'Single',
    menuItemTypeGroup: 'Group',
    title: 'Title',
    pageReference: 'Page',
    pageReferences: 'Page References',
    isButton: 'Show as Button',
    showSlideOutMenu: 'Show Slide-Out Menu',
    slideOutMenuItems: 'Menu Items',
    slideOutMenuButtons: 'Buttons',
    showCompanyDetailsSlideOutMenu: 'Show Company Details',
    footerColumns: 'Footer Columns',
    columnTitle: 'Column Title',
    menuItems: 'Menu Items',
    linkType: 'Link Type',
    linkTypeInternal: 'Internal',
    linkTypeExternal: 'External URL',
    externalUrl: 'External URL',
    footerLegalMenuItems: 'Legal Menu Items',
  },
  descriptions: {
    title: 'The title of the menu item.',
    pageReference: 'The page that the menu item will link to.',
    pageReferences: 'Pages that the menu item group will link to.',
    isButton: 'If checked, the menu item will be shown as a button instead of a link.',
    slideOutMenuButtons: 'Display buttons in the footer of the slide-out menu.',
    showCompanyDetailsSlideOutMenu: 'When enabled, company details (email, phone & socials) added in general settings will be displayed in the slide-out menu below the menu items.',
    externalUrl: 'The external URL that the menu item will link to.'
  },
  validation: {},
} as const;
