import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.navigationSettingsTranslations,
  fr: fr.navigationSettingsTranslations,
});

export const navigationSettingsDict = dict;
export const createNavigationSettingsField = createField;
export const getNavigationSettingsTranslations = getTranslations;
export type NavigationSettingsTranslations = typeof en.navigationSettingsTranslations;
