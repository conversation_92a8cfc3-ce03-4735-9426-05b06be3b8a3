export const navigationSettingsTranslations = {
  document: { name: 'navigationSettings', title: 'Paramètres de navigation' },
  fields: {
    navbarMenuItems: 'Éléments du menu',
    menuItemType: 'Type d’élément de menu',
    menuItemTypeSingle: 'Simple',
    menuItemTypeGroup: 'Groupe',
    title: 'Titre',
    pageReference: 'Page',
    pageReferences: 'Pages',
    isButton: 'Afficher comme bouton',
    showSlideOutMenu: 'Afficher le menu déroulant',
    slideOutMenuItems: 'Éléments du menu',
    slideOutMenuButtons: 'Boutons',
    showCompanyDetailsSlideOutMenu: 'Afficher les coordonnées de l’entreprise',
    footerColumns: 'Colonnes de pied de page',
    columnTitle: 'Titre de la colonne',
    menuItems: 'Éléments du menu',
    linkType: 'Type de lien',
    linkTypeInternal: 'Interne',
    linkTypeExternal: 'URL externe',
    externalUrl: 'URL externe',
    footerLegalMenuItems: 'Éléments légaux du menu',
  },
  descriptions: {
    title: 'Le titre de l’élément du menu.',
    pageReference: 'La page vers laquelle l’élément du menu pointera.',
    pageReferences: 'Pages vers lesquelles le groupe d’éléments du menu pointera.',
    isButton: 'Si coché, l’élément du menu sera affiché comme un bouton au lieu d’un lien.',
    slideOutMenuButtons: 'Afficher les boutons dans le pied de page du menu déroulant.',
    showCompanyDetailsSlideOutMenu: 'Si activé, les coordonnées de l’entreprise (email, téléphone et réseaux sociaux) ajoutées dans les paramètres généraux seront affichées dans le menu déroulant sous les éléments du menu.',
    externalUrl: 'L’URL externe vers laquelle l’élément du menu pointera.'
  },
  validation: {},
} as const;
