import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, getTranslations } = setupSchemaTranslations({
  en: en.paddingFieldsTranslations,
  fr: fr.paddingFieldsTranslations,
});

export const paddingFieldsDict = dict;
export const getPaddingFieldsTranslations = () => getTranslations();
export type PaddingFieldsTranslations = typeof en.paddingFieldsTranslations;
