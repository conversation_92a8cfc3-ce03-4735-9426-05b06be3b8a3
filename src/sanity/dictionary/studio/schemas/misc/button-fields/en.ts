// English translations for button fields
export const buttonFieldsTranslations = {
  fields: {
    showButton: 'Show Button',
    buttonText: 'Button Text',
    buttonType: 'Button Type',
    buttonAnchorLocation: 'Button Anchor Location',
    buttonPageReference: 'Button Page Reference',
    buttonAnchorId: 'Button Anchor ID',
    buttonExternalUrl: 'Button External URL',
    buttonEmailAddress: 'Button Email Address',
    buttonFileUrl: 'Button File URL',
    buttonVariant: 'Button Variant',
    buttonWidth: 'Button Width',
    // Option values
    internal: 'Internal',
    anchor: 'Anchor',
    external: 'External URL',
    fileDownload: 'File Download',
    emailAddress: 'Email Address',
    currentPage: 'Current Page',
    choosePage: 'Choose Page',
    primary: 'Primary',
    secondary: 'Secondary',
    tertiary: 'Tertiary',
    outline: 'Outline',
    underline: 'Underline',
    auto: 'Automatic',
    fullWidth: 'Full-Width',
  },
  validation: {},
  descriptions: {},
} as const;
