import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, getTranslations } = setupSchemaTranslations({
  en: en.buttonFieldsTranslations,
  fr: fr.buttonFieldsTranslations,
});

export const buttonFieldsDict = dict;
export const getButtonFieldsTranslations = () => getTranslations();
export type ButtonFieldsTranslations = typeof en.buttonFieldsTranslations;
