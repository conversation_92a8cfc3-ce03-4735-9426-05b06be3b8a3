// French translations for button fields
export const buttonFieldsTranslations = {
  fields: {
    showButton: 'Afficher le bouton',
    buttonText: 'Texte du bouton',
    buttonType: 'Type de bouton',
    buttonAnchorLocation: 'Emplacement de l’ancre',
    buttonPageReference: 'Référence de page',
    buttonAnchorId: 'ID d’ancre',
    buttonExternalUrl: 'URL externe du bouton',
    buttonEmailAddress: 'Adresse e-mail du bouton',
    buttonFileUrl: 'Fichier du bouton',
    buttonVariant: 'Variante du bouton',
    buttonWidth: 'Largeur du bouton',
    // Option values (matching English keys)
    internal: 'Interne',
    anchor: 'Ancre',
    external: 'URL externe',
    fileDownload: 'Téléchargement de fichier',
    emailAddress: 'Adresse e-mail',
    currentPage: 'Page actuelle',
    choosePage: 'Choisir une page',
    primary: 'Primaire',
    secondary: 'Secondaire',
    tertiary: 'Tertiaire',
    outline: 'Contour',
    underline: '<PERSON><PERSON><PERSON>',
    auto: 'Automatique',
    fullWidth: 'Pleine largeur',
  },
  validation: {},
  descriptions: {},
} as const;
