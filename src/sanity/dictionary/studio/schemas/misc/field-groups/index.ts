import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.fieldGroupsTranslations,
  fr: fr.fieldGroupsTranslations,
});

export const fieldGroupsDict = dict;
export const createFieldGroupField = createField;
export const getFieldGroupsTranslations = () => getTranslations();
export type FieldGroupsTranslations = typeof en.fieldGroupsTranslations;

