import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, getTranslations } = setupSchemaTranslations({
  en: en.fieldsetsTranslations,
  fr: fr.fieldsetsTranslations,
});

export const fieldsetsDict = dict;
export const getFieldsetsTranslations = () => getTranslations();
export type FieldsetsTranslations = typeof en.fieldsetsTranslations;
