# Step-by-Step LLM Refactor Procedure for Schema-Specific Translations

This guide documents the exact process and patterns to follow for refactoring and adding schema-specific translation modules in the Sanity Studio project. Use this as a checklist for each object, document, singleton, or misc schema.

---

## 1. Directory & File Structure

- Each schema gets its own directory under the appropriate group (e.g. `objects/`, `documents/`, `misc/`).
- Each schema directory contains:
  - `en.ts` (English translations)
  - `fr.ts` (French translations)
  - `index.ts` (exports and setup)

Example:
```
src/sanity/dictionary/studio/schemas/objects/[schema]/
  ├─ en.ts
  ├─ fr.ts
  └─ index.ts
```

---

## 2. Translation File Pattern

**en.ts / fr.ts**
```typescript
// English/French translations for [schema] object
export const [schema]ObjectTranslations = {
  fields: {
    fieldName: 'Field Label',
    // ...more fields
  },
  validation: {},
  descriptions: {},
} as const;
```
- Use a flat `fields` object. For nested fields, use dot notation (e.g. `'parent.child': 'Label'`).
- The export name must be `[schema]ObjectTranslations` (e.g. `richTextObjectTranslations`).

---

## 3. index.ts Pattern (MANDATORY)

```typescript
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.[schema]ObjectTranslations,
  fr: fr.[schema]ObjectTranslations,
});

export const [schema]ObjectDict = dict;
export const create[Schema]ObjectField = createField;
export const get[Schema]ObjectTranslations = getTranslations;
export type [Schema]ObjectTranslations = typeof en.[schema]ObjectTranslations;
```
- Replace `[schema]` with the schema's camelCase name, `[Schema]` with PascalCase.
- No custom exports or ad-hoc patterns.

---

## 4. Refactor Schema File

- Import the dictionary: `import { [schema]ObjectDict } from '../../dictionary/studio/schemas/objects/[schema]';`
- Destructure `fields` (and `validation`, `descriptions` if needed):
  ```typescript
  const { fields } = [schema]ObjectDict;
  ```
- Use translation keys for all UI labels:
  ```typescript
  title: fields.fieldName,
  // ...
  ```
- No direct string literals for UI labels.

---

## 5. Validation & Consistency

- Ensure all field labels in the schema are present in the translation files.
- Run type/lint checks after each refactor.
- Keep translation keys flat and consistent.

---

## 6. Naming & Export Conventions

- Main export: `[schema]ObjectDict`
- Helper: `create[Schema]ObjectField`
- Type: `[Schema]ObjectTranslations`
- All translation files must use the same export name as in `index.ts`.

---

## 7. Example (rich-text)

**en.ts**
```typescript
export const richTextObjectTranslations = {
  fields: {
    richTextContent: 'Content',
    'richTextContent.styles.normal': 'Normal',
  },
  validation: {},
  descriptions: {},
} as const;
```

**index.ts**
```typescript
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.richTextObjectTranslations,
  fr: fr.richTextObjectTranslations,
});

export const richTextObjectDict = dict;
export const createRichTextObjectField = createField;
export const getRichTextObjectTranslations = getTranslations;
export type RichTextObjectTranslations = typeof en.richTextObjectTranslations;
```

**schema file**
```typescript
import { richTextObjectDict } from '../../dictionary/studio/schemas/objects/rich-text';
const { fields } = richTextObjectDict;
// ...
title: fields.richTextContent,
```

---

## 8. Checklist for Each Schema

- [ ] Create `en.ts`, `fr.ts`, `index.ts` in the schema directory
- [ ] Use the exact export and naming pattern
- [ ] Refactor schema file to use the dictionary
- [ ] Validate with type/lint checks
- [ ] Repeat for all objects, documents, singletons, misc, and page-builder schemas

---

**Always follow this guide for every schema. No ad-hoc or custom patterns allowed.**
