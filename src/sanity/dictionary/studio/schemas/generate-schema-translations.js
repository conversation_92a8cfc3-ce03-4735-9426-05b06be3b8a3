#!/usr/bin/env node

/**
 * Template generator for creating new schema translations
 * 
 * Usage:
 * node generate-schema-translations.js [schemaName] [schemaType]
 * 
 * Example:
 * node generate-schema-translations.js author documents
 */

import fs from 'fs';
import path from 'path';

function generateSchemaTranslations(schemaName, schemaType = 'documents') {
  const basePath = path.join(__dirname, schemaType, schemaName);
  
  // Create directory
  if (!fs.existsSync(basePath)) {
    fs.mkdirSync(basePath, { recursive: true });
  }

  // English template
  const enTemplate = `// English translations for ${schemaName} schema
export const ${schemaName}Translations = {
  // Document info
  document: {
    name: '${schemaName}',
    title: '${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)}',
  },
  
  // Field translations
  fields: {
    title: 'Title',
    slug: 'Slug',
    // Add your fields here
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Title is required',
    slugRequired: 'Slug is required',
    // Add your validation messages here
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'The main title for this ${schemaName}',
    slug: 'URL-friendly version of the title, used in the ${schemaName} URL',
    // Add your field descriptions here
  },
} as const;`;

  // French template
  const frTemplate = `// French translations for ${schemaName} schema
export const ${schemaName}Translations = {
  // Document info
  document: {
    name: '${schemaName}',
    title: '${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)}',
  },
  
  // Field translations
  fields: {
    title: 'Titre',
    slug: 'Slug',
    // Add your fields here
  },
  
  // Validation messages
  validation: {
    titleRequired: 'Le titre est requis',
    slugRequired: 'Le slug est requis',
    // Add your validation messages here
  },
  
  // Field descriptions/help text
  descriptions: {
    title: 'Le titre principal de ce ${schemaName}',
    slug: 'Version URL-friendly du titre, utilisée dans l\\'URL du ${schemaName}',
    // Add your field descriptions here
  },
} as const;`;

  // Index template
  const indexTemplate = `// ${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)} schema translations index
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.${schemaName}Translations,
  fr: fr.${schemaName}Translations,
});

// Export for use in schemas
export const ${schemaName}Dict = dict;
export const create${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)}Field = createField;
export const get${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)}Translations = getTranslations;

// Export types
export type ${schemaName.charAt(0).toUpperCase() + schemaName.slice(1)}Translations = typeof en.${schemaName}Translations;`;

  // Write files
  fs.writeFileSync(path.join(basePath, 'en.ts'), enTemplate);
  fs.writeFileSync(path.join(basePath, 'fr.ts'), frTemplate);
  fs.writeFileSync(path.join(basePath, 'index.ts'), indexTemplate);

  console.log(`✅ Generated translation files for '${schemaName}' schema:`);
  console.log(`   📁 ${basePath}/`);
  console.log(`   📄 en.ts`);
  console.log(`   📄 fr.ts`);
  console.log(`   📄 index.ts`);
  console.log(``);
  console.log(`📝 Next steps:`);
  console.log(`   1. Edit the translation files to add your fields`);
  console.log(`   2. Export from ${schemaType}/index.ts`);
  console.log(`   3. Import and use in your schema file`);
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  const schemaName = args[0];
  const schemaType = args[1] || 'documents';

  if (!schemaName) {
    console.error('❌ Please provide a schema name');
    console.log('Usage: node generate-schema-translations.js [schemaName] [schemaType]');
    process.exit(1);
  }

  generateSchemaTranslations(schemaName, schemaType);
}

module.exports = { generateSchemaTranslations };
