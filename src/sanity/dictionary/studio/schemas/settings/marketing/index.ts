import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.marketingSettingsTranslations,
  fr: fr.marketingSettingsTranslations,
});

export const marketingSettingsDict = dict;
export const createMarketingSettingsField = createField;
export const getMarketingSettingsTranslations = getTranslations;
export type MarketingSettingsTranslations = typeof en.marketingSettingsTranslations;
