import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.generalSettingsTranslations,
  fr: fr.generalSettingsTranslations,
});

export const generalSettingsDict = dict;
export const createGeneralSettingsField = createField;
export const getGeneralSettingsTranslations = getTranslations;
export type GeneralSettingsTranslations = typeof en.generalSettingsTranslations;
