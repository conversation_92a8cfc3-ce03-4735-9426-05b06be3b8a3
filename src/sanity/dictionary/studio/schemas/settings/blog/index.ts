import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.blogSettingsTranslations,
  fr: fr.blogSettingsTranslations,
});

export const blogSettingsDict = dict;
export const createBlogSettingsField = createField;
export const getBlogSettingsTranslations = getTranslations;
export type BlogSettingsTranslations = typeof en.blogSettingsTranslations;
