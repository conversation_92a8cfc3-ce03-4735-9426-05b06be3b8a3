import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.gridLayoutBlockTranslations,
  fr: fr.gridLayoutBlockTranslations,
});

export const gridLayoutBlockDict = dict;
export const createGridLayoutBlockField = createField;
export const getGridLayoutBlockTranslations = getTranslations;
export type GridLayoutBlockTranslations = typeof en.gridLayoutBlockTranslations;
