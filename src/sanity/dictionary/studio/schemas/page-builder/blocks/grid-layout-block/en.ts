// English translations for grid layout block
export const gridLayoutBlockTranslations = {
  fields: {
    title: 'Internal Title',
    displayTitle: 'Display Title',
    publicTitle: 'Public Title',
    description: 'Description',
    gridConfig: 'Grid Configuration',
    gridStyling: 'Grid Styling',
    gridItems: 'Grid Items',
    gridItem: 'Grid Item',
    gridItemConfig: 'Grid Item Configuration',
    block: 'Block',
    noTitle: 'Grid Layout',
    subtitle: 'Grid Layout',
  },
  validation: {},
  descriptions: {
    title: 'Internal reference name for this grid layout (not shown to users)',
    displayTitle: 'Show the title on the page',
    publicTitle: 'Title shown to users (only if Display Title is enabled)',
    description: 'Optional description shown below the title',
    gridConfig: 'Configure the grid layout properties',
    gridStyling: 'Configure spacing, background, and container style',
    gridItems: 'Blocks to display in the grid',
    gridItemConfig: 'Configure how this item spans rows and columns',
    block: 'Choose exactly one block to place in this grid position',
  },
} as const;
