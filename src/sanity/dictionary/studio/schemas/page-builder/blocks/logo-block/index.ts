import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.logoBlockTranslations,
  fr: fr.logoBlockTranslations,
});

export const logoBlockDict = dict;
export const createLogoBlockField = createField;
export const getLogoBlockTranslations = getTranslations;
export type LogoBlockTranslations = typeof en.logoBlockTranslations;
