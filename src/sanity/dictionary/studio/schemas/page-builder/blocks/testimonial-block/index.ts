import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.testimonialBlockTranslations,
  fr: fr.testimonialBlockTranslations,
});

export const testimonialBlockDict = dict;
export const createTestimonialBlockField = createField;
export const getTestimonialBlockTranslations = getTranslations;
export type TestimonialBlockTranslations = typeof en.testimonialBlockTranslations;
