import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.mediaBlockTranslations,
  fr: fr.mediaBlockTranslations,
});

export const mediaBlockDict = dict;
export const createMediaBlockField = createField;
export const getMediaBlockTranslations = getTranslations;
export type MediaBlockTranslations = typeof en.mediaBlockTranslations;
