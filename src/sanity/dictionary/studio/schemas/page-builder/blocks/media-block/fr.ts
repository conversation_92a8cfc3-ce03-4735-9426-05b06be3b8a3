// French translations for media block
export const mediaBlockTranslations = {
  fields: {
    backgroundType: 'Type d’arrière-plan',
    backgroundWidth: 'Largeur de l’arrière-plan',
    image: 'Image',
    altText: 'Texte alternatif',
    overlayType: 'Type de superposition',
    dialogType: 'Type de dialogue',
    videoUrl: 'URL de la vidéo',
    anchorId: 'ID d’ancre',
    noTitle: 'Aucun titre défini. Ajoutez-en un dans ce bloc',
    subtitle: 'Média',
    backgroundType_image: 'Image',
    backgroundType_video: 'Vidéo',
    backgroundWidth_full: 'Pleine largeur',
    backgroundWidth_contained: 'Contenue',
    overlayType_none: 'Aucune',
    overlayType_dark: 'Sombre',
    dialogType_none: 'Aucun',
    dialogType_video: 'Vidéo',
  },
  validation: {},
  descriptions: {},
} as const;
