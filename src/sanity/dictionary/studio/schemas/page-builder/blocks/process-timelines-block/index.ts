import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.processTimelinesBlockTranslations,
  fr: fr.processTimelinesBlockTranslations,
});

export const processTimelinesBlockDict = dict;
export const createProcessTimelinesBlockField = createField;
export const getProcessTimelinesBlockTranslations = getTranslations;
export type ProcessTimelinesBlockTranslations = typeof en.processTimelinesBlockTranslations;
