// French translations for statistics block
export const statisticsBlockTranslations = {
  fields: {
    title: 'Titre de la section',
    description: 'Description de la section',
    statistics: 'Statistiques',
    statisticItem: 'Élément statistique',
    label: 'Libellé',
    statisticDescription: 'Description',
    dataSource: 'Source de données',
    numberFormatting: 'Formatage du nombre',
    prefix: 'Préfixe',
    suffix: 'Suffixe',
    decimalPlaces: 'Décimales',
    useThousandsSeparator: 'Séparateur de milliers',
    abbreviateLargeNumbers: 'Abréger les grands nombres',
    animation: 'Configuration de l’animation',
    icon: 'Icône',
    iconType: 'Type d’icône',
    lucideIcon: 'Nom de l’icône Lucide',
    customImage: 'Icône personnalisée',
    emoji: 'Émoji',
    position: 'Position de l’icône',
    styling: 'Style personnalisé',
    numberColor: 'Couleur du nombre',
    labelColor: 'Couleur du libellé',
    numberSize: 'Taille du nombre',
    fontWeight: 'Graisse de police',
    layout: 'Configuration de la mise en page',
    columns: 'Colonnes par point de rupture',
    mobile: 'Colonnes mobile',
    tablet: 'Colonnes tablette',
    desktop: 'Colonnes bureau',
    spacing: 'Espacement de la grille',
    alignment: 'Alignement du texte',
    globalAnimation: 'Paramètres d’animation globaux',
    enableStagger: 'Activer l’animation échelonnée',
    staggerDelay: 'Délai d’échelonnement (secondes)',
    backgroundColor: 'Couleur de fond',
    anchorId: 'ID d’ancre',
  },
  validation: {
    dataSourceRequired: 'La source de données est requise',
    dataSourceNumber: 'Les statistiques nécessitent une source de données qui retourne un nombre',
    labelRequired: 'Le libellé qui décrit cette statistique',
    minStatistics: 'Au moins une statistique est requise',
    maxStatistics: 'Pas plus de 12 statistiques autorisées',
    minMobile: 'Minimum 1 colonne pour mobile',
    maxMobile: 'Maximum 3 colonnes pour mobile',
    minTablet: 'Minimum 1 colonne pour tablette',
    maxTablet: 'Maximum 4 colonnes pour tablette',
    minDesktop: 'Minimum 1 colonne pour bureau',
    maxDesktop: 'Maximum 6 colonnes pour bureau',
    minStaggerDelay: 'Le délai minimum est de 0 seconde',
    maxStaggerDelay: 'Le délai maximum est de 2 secondes',
    maxEmoji: 'Un seul caractère émoji',
  },
  descriptions: {
    title: 'Titre optionnel pour la section des statistiques',
    description: 'Description ou contexte optionnel pour les statistiques',
    label: 'Le libellé qui décrit cette statistique',
    statisticDescription: 'Description ou contexte supplémentaire optionnel',
    dataSource: 'Configurer la source de données de cette statistique',
    prefix: 'Texte à afficher avant le nombre (ex : "$", "+", "#")',
    suffix: 'Texte à afficher après le nombre (ex : "%", "+", "K")',
    decimalPlaces: 'Nombre de décimales à afficher',
    useThousandsSeparator: 'Afficher les virgules pour les milliers (1 000 vs 1000)',
    abbreviateLargeNumbers: 'Afficher 1K, 1M, 1B au lieu des nombres complets',
    animation: 'Configurer l’animation de cette statistique',
    lucideIcon: 'Nom de l’icône Lucide React (ex : Users, Star, TrendingUp)',
    customImage: 'Télécharger une icône personnalisée',
    emoji: 'Un seul caractère émoji',
    numberColor: 'Couleur personnalisée pour le nombre',
    labelColor: 'Couleur personnalisée pour le libellé',
    numberSize: 'Taille du nombre',
    fontWeight: 'Graisse de police pour le nombre',
    columns: 'Définir les colonnes pour chaque point de rupture',
    spacing: 'Espacement entre les éléments de la grille',
    alignment: 'Alignement du texte pour les statistiques',
    enableStagger: 'Animer les statistiques une après l’autre',
    staggerDelay: 'Délai entre chaque animation de statistique',
    backgroundColor: 'Couleur de fond optionnelle pour la section',
  },
} as const;
