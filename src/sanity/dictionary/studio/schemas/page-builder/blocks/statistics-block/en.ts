// English translations for statistics block
export const statisticsBlockTranslations = {
  fields: {
    title: 'Section Title',
    description: 'Section Description',
    statistics: 'Statistics',
    statisticItem: 'Statistic Item',
    label: 'Label',
    statisticDescription: 'Description',
    dataSource: 'Data Source',
    numberFormatting: 'Number Formatting',
    prefix: 'Prefix',
    suffix: 'Suffix',
    decimalPlaces: 'Decimal Places',
    useThousandsSeparator: 'Use Thousands Separator',
    abbreviateLargeNumbers: 'Abbreviate Large Numbers',
    animation: 'Animation Configuration',
    icon: 'Icon',
    iconType: 'Icon Type',
    lucideIcon: 'Lucide Icon Name',
    customImage: 'Custom Icon Image',
    emoji: 'Emoji',
    position: 'Icon Position',
    styling: 'Custom Styling',
    numberColor: 'Number Color',
    labelColor: 'Label Color',
    numberSize: 'Number Size',
    fontWeight: 'Font Weight',
    layout: 'Layout Configuration',
    columns: 'Columns per Breakpoint',
    mobile: 'Mobile Columns',
    tablet: 'Tablet Columns',
    desktop: 'Desktop Columns',
    spacing: 'Grid Spacing',
    alignment: 'Text Alignment',
    globalAnimation: 'Global Animation Settings',
    enableStagger: 'Enable Stagger Animation',
    staggerDelay: 'Stagger Delay (seconds)',
    backgroundColor: 'Background Color',
    anchorId: 'Anchor ID',
  },
  validation: {
    dataSourceRequired: 'Data source is required',
    dataSourceNumber: 'Statistics require a data source that returns a number',
    labelRequired: 'The label that describes this statistic',
    minStatistics: 'At least one statistic is required',
    maxStatistics: 'No more than 12 statistics allowed',
    minMobile: 'Minimum 1 column for mobile',
    maxMobile: 'Maximum 3 columns for mobile',
    minTablet: 'Minimum 1 column for tablet',
    maxTablet: 'Maximum 4 columns for tablet',
    minDesktop: 'Minimum 1 column for desktop',
    maxDesktop: 'Maximum 6 columns for desktop',
    minStaggerDelay: 'Minimum delay is 0 seconds',
    maxStaggerDelay: 'Maximum delay is 2 seconds',
    maxEmoji: 'Single emoji character',
  },
  descriptions: {
    title: 'Optional title for the statistics section',
    description: 'Optional description or context for the statistics',
    label: 'The label that describes this statistic',
    statisticDescription: 'Optional additional description or context',
    dataSource: 'Configure where this statistic gets its data',
    prefix: 'Text to show before the number (e.g., "$", "+", "#")',
    suffix: 'Text to show after the number (e.g., "%", "+", "K")',
    decimalPlaces: 'Number of decimal places to show',
    useThousandsSeparator: 'Show commas for thousands (1,000 vs 1000)',
    abbreviateLargeNumbers: 'Show 1K, 1M, 1B instead of full numbers',
    animation: 'Configure how this statistic animates',
    lucideIcon: 'Icon name from Lucide React (e.g., Users, Star, TrendingUp)',
    customImage: 'Upload a custom icon image',
    emoji: 'Single emoji character',
    numberColor: 'Custom color for the number',
    labelColor: 'Custom color for the label',
    numberSize: 'Size of the number',
    fontWeight: 'Font weight for the number',
    columns: 'Set columns for each breakpoint',
    spacing: 'Spacing between grid items',
    alignment: 'Text alignment for statistics',
    enableStagger: 'Animate statistics one after another',
    staggerDelay: 'Delay between each statistic animation',
    backgroundColor: 'Optional background color for the section',
  },
} as const;
