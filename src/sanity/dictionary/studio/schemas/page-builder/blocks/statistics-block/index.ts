import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.statisticsBlockTranslations,
  fr: fr.statisticsBlockTranslations,
});

export const statisticsBlockDict = dict;
export const createStatisticsBlockField = createField;
export const getStatisticsBlockTranslations = getTranslations;
export type StatisticsBlockTranslations = typeof en.statisticsBlockTranslations;
