import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.servicesBlockTranslations,
  fr: fr.servicesBlockTranslations,
});

export const servicesBlockDict = dict;
export const createServicesBlockField = createField;
export const getServicesBlockTranslations = getTranslations;
export type ServicesBlockTranslations = typeof en.servicesBlockTranslations;
