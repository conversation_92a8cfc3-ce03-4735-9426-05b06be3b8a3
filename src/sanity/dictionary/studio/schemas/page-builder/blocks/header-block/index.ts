import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.headerBlockTranslations,
  fr: fr.headerBlockTranslations,
});

export const headerBlockDict = dict;
export const createHeaderBlockField = createField;
export const getHeaderBlockTranslations = getTranslations;
export type HeaderBlockTranslations = typeof en.headerBlockTranslations;
