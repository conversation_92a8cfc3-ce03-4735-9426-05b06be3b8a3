import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.featureCardsBlockTranslations,
  fr: fr.featureCardsBlockTranslations,
});

export const featureCardsBlockDict = dict;
export const createFeatureCardsBlockField = createField;
export const getFeatureCardsBlockTranslations = getTranslations;
export type FeatureCardsBlockTranslations = typeof en.featureCardsBlockTranslations;
