import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.featuresMinimalBlockTranslations,
  fr: fr.featuresMinimalBlockTranslations,
});

export const featuresMinimalBlockDict = dict;
export const createFeaturesMinimalBlockField = createField;
export const getFeaturesMinimalBlockTranslations = getTranslations;
export type FeaturesMinimalBlockTranslations = typeof en.featuresMinimalBlockTranslations;
