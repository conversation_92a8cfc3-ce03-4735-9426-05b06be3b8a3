import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.freeformBlockTranslations,
  fr: fr.freeformBlockTranslations,
});

export const freeformBlockDict = dict;
export const createFreeformBlockField = createField;
export const getFreeformBlockTranslations = getTranslations;
export type FreeformBlockTranslations = typeof en.freeformBlockTranslations;
