// English translations for freeform block
export const freeformBlockTranslations = {
  fields: {
    title: 'Title',
    columnsPerRow: 'Columns Per Row',
    columns: 'Columns',
    columnTitle: 'Column Title',
    spacing: 'Spacing Between Items',
    alignment: 'Alignment',
    items: 'Items',
    spacerObject: 'Spacer',
    headingObject: 'Heading',
    richTextObject: 'Rich Text',
    buttonObject: 'Button',
    singleImageObject: 'Image',
    border: 'Border',
    border_none: 'None',
    border_topBottom: 'Top & Bottom',
    border_top: 'Top Only',
    border_bottom: 'Bottom Only',
    anchorId: 'Anchor ID',
    noTitle: 'No title set. Add one inside this block',
    subtitle: 'Freeform',
  },
  validation: {},
  descriptions: {
    columnTitle: 'For internal purposes.',
    spacing: 'Add some default spacing between items (optional).',
    border: 'Display a border to seperate this block with blocks above and below.',
  },
} as const;
