import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.carouselBlockTranslations,
  fr: fr.carouselBlockTranslations,
});

export const carouselBlockDict = dict;
export const createCarouselBlockField = createField;
export const getCarouselBlockTranslations = getTranslations;
export type CarouselBlockTranslations = typeof en.carouselBlockTranslations;
