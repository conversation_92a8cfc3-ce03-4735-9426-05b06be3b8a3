// English translations for carousel block
export const carouselBlockTranslations = {
  fields: {
    title: 'Interactive Carousel',
    carouselTitle: 'Carousel Title',
    carouselTitleDesc: 'Optional title displayed above the carousel',
    description: 'Description',
    descriptionDesc: 'Optional description text',
    carouselConfig: 'Carousel Configuration',
    carouselConfigDesc: 'Configure carousel behavior and appearance',
    contentType: 'Content Type',
    contentTypeDesc: 'Type of content this carousel will display',
    contentType_mixed: 'Mixed Content',
    contentType_images: 'Images Only',
    contentType_videos: 'Videos Only',
    contentType_cards: 'Text Cards',
    contentType_blocks: 'Block References',
    items: 'Items',
    anchorId: 'Anchor ID',
    noTitle: 'No title set. Add one inside this block',
    subtitle: 'Interactive Carousel',
    
    // Slide type fields
    slideType: 'Slide Type',
    slideType_image: 'Image',
    slideType_video: 'Video', 
    slideType_card: 'Content Card',
    
    // Image fields
    image: 'Image',
    imageAlt: 'Alt Text',
    imageCaption: 'Caption',
    
    // Video fields
    video: 'Video',
    videoFile: 'Video File',
    videoThumbnail: 'Video Thumbnail',
    videoCaption: 'Video Caption',
    
    // Card fields
    cardTitle: 'Card Title',
    cardDescription: 'Card Description',
    cardImage: 'Card Image',
    cardImageAlt: 'Card Image Alt Text',
    cardButton: 'Card Button',
    cardButtonText: 'Button Text',
    cardButtonUrl: 'Button URL',
    cardButtonStyle: 'Button Style',
    cardButtonStyle_primary: 'Primary',
    cardButtonStyle_secondary: 'Secondary',
    cardButtonStyle_outline: 'Outline',
    
    // Content field
    content: 'Content',
    
    // Slide titles
    mixedSlideTitle: 'Mixed Content Slide',
    imageSlideTitle: 'Image Slide',
    cardSlideTitle: 'Card Slide',
    blockSlideTitle: 'Block Reference Slide',
  },
  validation: {},
  descriptions: {},
} as const;
