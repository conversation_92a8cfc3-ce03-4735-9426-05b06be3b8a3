// French translations for carousel block
export const carouselBlockTranslations = {
  fields: {
    title: 'Carrousel interactif',
    carouselTitle: 'Titre du carrousel',
    carouselTitleDesc: 'Titre optionnel affiché au-dessus du carrousel',
    description: 'Description',
    descriptionDesc: 'Texte descriptif optionnel',
    carouselConfig: 'Configuration du carrousel',
    carouselConfigDesc: 'Configurer le comportement et l’apparence du carrousel',
    contentType: 'Type de contenu',
    contentTypeDesc: 'Type de contenu affiché par ce carrousel',
    contentType_mixed: 'Contenu mixte',
    contentType_images: 'Images uniquement',
    contentType_videos: 'Vidéos uniquement',
    contentType_cards: 'Cartes de texte',
    contentType_blocks: 'Références de blocs',
    items: 'Éléments',
    anchorId: 'ID d’ancre',
    noTitle: 'Aucun titre défini. Ajoutez-en un dans ce bloc',
    subtitle: 'Carrousel interactif',

    // Slide type fields
    slideType: 'Type de diapositive',
    slideType_image: 'Image',
    slideType_video: 'Vidéo',
    slideType_card: 'Carte de contenu',

    // Image fields
    image: 'Image',
    imageAlt: 'Texte alternatif',
    imageCaption: 'Légende',

    // Video fields
    video: 'Vidéo',
    videoFile: 'Fichier vidéo',
    videoThumbnail: 'Miniature vidéo',
    videoCaption: 'Légende vidéo',

    // Card fields
    cardTitle: 'Titre de la carte',
    cardDescription: 'Description de la carte',
    cardImage: 'Image de la carte',
    cardImageAlt: 'Texte alternatif de l\'image de la carte',
    cardButton: 'Bouton de la carte',
    cardButtonText: 'Texte du bouton',
    cardButtonUrl: 'URL du bouton',
    cardButtonStyle: 'Style du bouton',
    cardButtonStyle_primary: 'Primaire',
    cardButtonStyle_secondary: 'Secondaire',
    cardButtonStyle_outline: 'Contour',

    // Content field
    content: 'Contenu',

    // Slide titles
    mixedSlideTitle: 'Diapositive de contenu mixte',
    imageSlideTitle: 'Diapositive d\'image',
    cardSlideTitle: 'Diapositive de carte',
    blockSlideTitle: 'Diapositive de référence de bloc',
  },
  validation: {},
  descriptions: {},
} as const;
