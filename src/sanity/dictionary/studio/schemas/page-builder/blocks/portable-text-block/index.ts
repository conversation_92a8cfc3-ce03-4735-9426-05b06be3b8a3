import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.portableTextBlockTranslations,
  fr: fr.portableTextBlockTranslations,
});

export const portableTextBlockDict = dict;
export const createPortableTextBlockField = createField;
export const getPortableTextBlockTranslations = getTranslations;
export type PortableTextBlockTranslations = typeof en.portableTextBlockTranslations;
