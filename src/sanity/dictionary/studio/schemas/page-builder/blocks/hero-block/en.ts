// English translations for hero block
export const heroBlockTranslations = {
  fields: {
    heading: 'Heading',
    content: 'Content',
    mediaType: 'Media',
    mediaType_image: 'Image',
    mediaType_none: 'None',
    bottomCornerRadius: 'Corner Radius - Bottom L/R',
    bottomCornerRadius_straight: 'Straight',
    bottomCornerRadius_rounded: 'Rounded',
    image: 'Image',
    altText: 'Alternative Text',
    height: 'Height',
    height_full: 'Full',
    height_short: 'Short',
    buttons: 'Buttons',
    dialogType: 'Dialog Type',
    dialogType_none: 'None',
    dialogType_video: 'Video',
    videoUrl: 'Video URL',
    overlayType: 'Overlay Type',
    overlayType_none: 'None',
    overlayType_dark: 'Dark',
    anchorId: 'Anchor ID',
    noTitle: 'No title set. Add one inside this block',
    subtitle: 'Hero',
  },
  validation: {},
  descriptions: {},
} as const;
