import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.heroBlockTranslations,
  fr: fr.heroBlockTranslations,
});

export const heroBlockDict = dict;
export const createHeroBlockField = createField;
export const getHeroBlockTranslations = getTranslations;
export type HeroBlockTranslations = typeof en.heroBlockTranslations;
