// French translations for hero block
export const heroBlockTranslations = {
  fields: {
    heading: 'Titre',
    content: 'Contenu',
    mediaType: 'Média',
    mediaType_image: 'Image',
    mediaType_none: 'Aucun',
    bottomCornerRadius: 'Rayon d’angle - Bas G/D',
    bottomCornerRadius_straight: 'Droit',
    bottomCornerRadius_rounded: 'Arrondi',
    image: 'Image',
    altText: 'Texte alternatif',
    height: 'Hauteur',
    height_full: 'Pleine',
    height_short: 'Courte',
    buttons: 'Boutons',
    dialogType: 'Type de dialogue',
    dialogType_none: 'Aucun',
    dialogType_video: 'Vidéo',
    videoUrl: 'URL de la vidéo',
    overlayType: 'Type de superposition',
    overlayType_none: 'Aucune',
    overlayType_dark: 'Sombre',
    anchorId: 'ID d’ancre',
    noTitle: 'Aucun titre défini. Ajoutez-en un dans ce bloc',
    subtitle: '<PERSON><PERSON><PERSON>',
  },
  validation: {},
  descriptions: {},
} as const;
