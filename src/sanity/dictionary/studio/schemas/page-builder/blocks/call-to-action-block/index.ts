import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.callToActionBlockTranslations,
  fr: fr.callToActionBlockTranslations,
});

export const callToActionBlockDict = dict;
export const createCallToActionBlockField = createField;
export const getCallToActionBlockTranslations = getTranslations;
export type CallToActionBlockTranslations = typeof en.callToActionBlockTranslations;
