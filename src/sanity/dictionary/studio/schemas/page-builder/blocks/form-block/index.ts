import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.formBlockTranslations,
  fr: fr.formBlockTranslations,
});

export const formBlockDict = dict;
export const createFormBlockField = createField;
export const getFormBlockTranslations = getTranslations;
export type FormBlockTranslations = typeof en.formBlockTranslations;
