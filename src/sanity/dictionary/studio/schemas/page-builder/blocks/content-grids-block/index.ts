import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../../utils';

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.contentGridsBlockTranslations,
  fr: fr.contentGridsBlockTranslations,
});

export const contentGridsBlockDict = dict;
export const createContentGridsBlockField = createField;
export const getContentGridsBlockTranslations = getTranslations;
export type ContentGridsBlockTranslations = typeof en.contentGridsBlockTranslations;
