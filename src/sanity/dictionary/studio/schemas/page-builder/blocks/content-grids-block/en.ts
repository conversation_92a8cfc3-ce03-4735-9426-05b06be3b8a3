// English translations for content grids block
export const contentGridsBlockTranslations = {
  fields: {
    title: 'Content Grids',
    sectionTitle: 'Section Title',
    sectionTitleDesc: 'Optional title for the content grid section',
    sectionSubtitle: 'Section Subtitle',
    sectionSubtitleDesc: 'Optional subtitle or description',
    dataSource: 'Content Data Source',
    dataSourceDesc: 'Configure where to fetch the grid content from',
    manualItems: 'Additional Manual Items',
    manualItemsDesc: 'Optional manual items to add to the grid (will be combined with data source results)',
    gridConfig: 'Grid Configuration',
  },
  validation: {
    dataSourceRequired: 'Data source is required',
    dataSourceFormat: 'Content grids require a data source that returns documents',
    manualItemsMax: 'Maximum 20 manual items allowed',
  },
  descriptions: {},
} as const;
