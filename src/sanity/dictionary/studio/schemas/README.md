# Schema-Specific Translation System

## Overview

This new approach breaks down the large translation files into smaller, schema-specific files, making translations more maintainable and allowing for better organization. **The system uses a generic factory pattern that eliminates boilerplate code for each schema.**

## Structure

```
src/sanity/dictionary/studio/schemas/
├── factory.ts                 # Generic translation factory
├── utils.ts                   # Convenient setup utility
├── index.ts                   # Main schemas translations export
└── documents/
    ├── index.ts              # Documents translations export
    ├── page/
    │   ├── index.ts          # Page translations setup (3 lines!)
    │   ├── en.ts            # English page translations
    │   └── fr.ts            # French page translations
    └── post/
        ├── index.ts          # Post translations setup (3 lines!)
        ├── en.ts            # English post translations
        └── fr.ts            # French post translations
```

## Benefits

1. **No Boilerplate**: Each schema index file is only ~15 lines
2. **Generic Factory**: One factory handles all schemas
3. **Type Safety**: Full TypeScript support with proper inference
4. **Easy Maintenance**: Changes to translation logic affect all schemas
5. **Scalability**: Adding new schemas requires minimal code

## Usage Examples

### Setting Up a New Schema (Super Simple!)

```typescript
// src/sanity/dictionary/studio/schemas/documents/[schema]/index.ts
import * as en from './en';
import * as fr from './fr';
import { setupSchemaTranslations } from '../../utils';

// Set up translations using the utility
const { dict, createField, getTranslations } = setupSchemaTranslations({
    en: en.schemaTranslations,
    fr: fr.schemaTranslations,
});
// Export for use in schemas
export const schemaDict = dict; // Naming: [schema]Dict (e.g. projectCategoryDict)
export const createSchemaField = createField;
export const getSchemaTranslations = getTranslations;
// Export types
export type SchemaTranslations = typeof en.schemaTranslations;
```

**Naming Convention:**
- Always export the main dictionary as `[schema]Dict` (e.g. `projectCategoryDict`, `pageDict`, `postDict`).
- Helper functions should be named accordingly (e.g. `createProjectCategoryField`).

### Using in Schema Files

```typescript
// src/sanity/schemas/documents/project-category.ts
import { projectCategoryDict } from "../../dictionary";

const { document, fields, validation, descriptions } = projectCategoryDict;

export default defineType({
  name: 'projectCategory',
  title: document.title,
  // ...
});
```

### Using Helper Functions (Optional)

```typescript
import { createProjectCategoryField, projectCategoryDict } from "../../dictionary";

export default defineType({
  name: 'projectCategory',
  title: projectCategoryDict.document.title,
  fields: [
    defineField({
      ...createProjectCategoryField('title', {
        type: 'string',
        validation: rule => rule.required().error(projectCategoryDict.validation.titleRequired)
      })
    }),
    // ...more fields
  ]
});
```

## Translation File Structure

Each schema has consistent translation files:

### English (en.ts)
```typescript
export const schemaTranslations = {
  document: { name: 'schema', title: 'Schema Title' },
  fields: { fieldName: 'Field Title', /* ... */ },
  validation: { fieldRequired: 'Field is required', /* ... */ },
  descriptions: { fieldName: 'Field description', /* ... */ },
} as const;
```

### French (fr.ts)
```typescript
export const schemaTranslations = {
  document: { name: 'schema', title: 'Titre du Schéma' },
  fields: { fieldName: 'Titre du Champ', /* ... */ },
  validation: { fieldRequired: 'Le champ est requis', /* ... */ },
  descriptions: { fieldName: 'Description du champ', /* ... */ },
} as const;
```

## Core Factory (factory.ts)

The `SchemaTranslationFactory` class handles:
- ✅ Language detection from environment variables
- ✅ Fallback to English if language not supported
- ✅ Type-safe translation access
- ✅ Generic field creation utilities
- ✅ Available languages enumeration

## Adding New Schema Translations

1. **Create schema directory**: `src/sanity/dictionary/studio/schemas/documents/[schemaName]/`
2. **Add language files**: `en.ts`, `fr.ts` with translations
3. **Create index.ts**: Copy pattern from existing schemas (see above for naming)
4. **Export**: Add to parent `index.ts` files
5. **Use**: Import and use in schema file

## Language Detection

The system automatically detects language from `NEXT_PUBLIC_DEFAULT_LANGUAGE` environment variable, falling back to English if not set or unsupported.

## Migration Strategy

1. ✅ Create generic factory system
2. ✅ Create utility helpers  
3. ✅ Refactor existing schemas (page, post)
4. ⏳ Gradually migrate other schemas using the same pattern
5. ⏳ Update main dictionary to reference schema-specific translations
6. ⏳ Remove old monolithic translation files

**This approach provides maximum scalability with minimal boilerplate - each new schema requires only 3-4 lines of setup code!**
