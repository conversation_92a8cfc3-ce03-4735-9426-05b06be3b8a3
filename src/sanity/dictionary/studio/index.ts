// Clean SOLID implementation of the studio dictionary system

// IMPORTANT: All field, validation, and description translations should now be accessed via schema-specific dictionaries (see ./schemas/*) and not from global dictionaries.
// This file only provides language config utilities and exports schema translation modules.

// Utility functions for language config
export const getLanguageTitle = (language: string): string => {
  const titles: Record<string, string> = {
    en: 'English',
    fr: 'Français',
  };
  return titles[language] || language;
};

export const getStudioLanguageConfig = () => {
  // This should be replaced with your actual supported language logic if needed
  const supportedLanguages = ['en', 'fr'];
  const defaultLanguage = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || 'en';
  return {
    defaultLanguage,
    supportedLanguages,
    getLanguageTitle,
  };
};

export * from './schemas';
