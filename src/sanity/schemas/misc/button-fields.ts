import {defineField} from "sanity";
import {pageReferenceTypes} from "./page-reference-types";
import {buttonFieldsDict} from '../../dictionary/studio/schemas/misc/button-fields';

const { fields } = buttonFieldsDict;

export const buttonFields = [
  defineField({
    name: 'showButton',
    title: fields.showButton,
    type: 'boolean',
    initialValue: false
  }),
  defineField({
    name: 'buttonText',
    title: fields.buttonText,
    type: 'string',
    hidden: ({parent}) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonType,
    name: "buttonType",
    type: "string",
    options: {
      list: [
        { title: fields.internal, value: "internal" },
        { title: fields.anchor, value: "anchor" },
        { title: fields.external, value: "external" },
        { title: fields.fileDownload, value: "fileDownload" },
        { title: fields.emailAddress, value: "emailAddress" },
      ],
    },
    initialValue: 'internal',
    hidden: ({parent}) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonAnchorLocation,
    name: "buttonAnchorLocation",
    type: "string",
    options: {
      list: [
        { title: fields.currentPage, value: "currentPage" },
        { title: fields.choosePage, value: "choosePage" },
      ],
    },
    initialValue: 'currentPage',
    hidden: ({ parent }) => !parent?.showButton || parent?.buttonType !== 'anchor',
  }),
  defineField({
    name: 'buttonPageReference',
    title: fields.buttonPageReference,
    type: 'reference',
    to: [ ...pageReferenceTypes ],
    hidden: ({ parent }) => !parent?.showButton || 
      (parent?.buttonType !== 'internal' && 
       !(parent?.buttonType === 'anchor' && parent?.buttonAnchorLocation === 'choosePage')),
  }),
  defineField({
    name: 'buttonAnchorId',
    title: fields.buttonAnchorId,
    type: 'string',
  }),
  defineField({
    name: 'buttonExternalUrl',
    title: fields.buttonExternalUrl,
    type: 'url',
    validation: Rule => Rule.uri({ scheme: ['http', 'https', 'mailto', 'tel'] }),
    hidden: ({ parent }) => !parent?.showButton || parent?.buttonType !== 'external',
  }),
  defineField({
    name: 'buttonEmailAddress',
    title: fields.buttonEmailAddress,
    type: 'string',
    hidden: ({ parent }) => !parent?.showButton || parent?.buttonType !== 'emailAddress',
  }),
  defineField({
    name: 'buttonFileUrl',
    title: fields.buttonFileUrl,
    type: 'file',  
    hidden: ({ parent }) => !parent?.showButton || parent?.buttonType !== 'fileDownload',
  }),
  defineField({
    title: fields.buttonVariant,
    name: "buttonVariant",
    type: "string",
    options: {
      list: [
        { title: fields.primary, value: "primary" },
        { title: fields.secondary, value: "secondary" },
        { title: fields.tertiary, value: "tertiary" },
        { title: fields.outline, value: "outline-solid" },
        { title: fields.underline, value: "underline" },
      ],
    },
    initialValue: 'primary',
    hidden: ({parent}) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonWidth,
    name: "buttonWidth",
    type: "string",
    options: {
      list: [
        { title: fields.auto, value: "auto" },
        { title: fields.fullWidth, value: "fullWidth" },
      ],
    },
    initialValue: 'auto',
    hidden: ({parent}) => !parent?.showButton,
  }),
]