import {defineField} from "sanity";
import {paddingFieldsDict} from '../../dictionary/studio/schemas/misc/padding-fields';

const { fields } = paddingFieldsDict;

export const paddingFields = [
  defineField({
    title: fields.paddingTop,
    name: "paddingTop",
    type: "string",
    fieldset: 'padding',
    group: 'layout',
    options: {
      list: [
        { title: fields.none, value: "none" },
        { title: fields.small, value: "small" },
        { title: fields.medium, value: "medium" },
        { title: fields.default, value: "default" },
        { title: fields.large, value: "large" },
      ],
    },
    initialValue: 'default'
  }),
  defineField({
    title: fields.paddingBottom,
    name: "paddingBottom",
    type: "string",
    fieldset: 'padding',
    group: 'layout',
    options: {
      list: [
        { title: fields.none, value: "none" },
        { title: fields.small, value: "small" },
        { title: fields.medium, value: "medium" },
        { title: fields.default, value: "default" },
        { title: fields.large, value: "large" },
      ],
    },
    initialValue: 'default'
  }),   
]