import {Users} from "lucide-react";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {authorDict} from "../../dictionary/studio/schemas/documents/author";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = authorDict;

export default defineType({
  name: 'author',
  title: document.title,
  type: 'document',
  icon: Users,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'name',
      title: fields.name,
      type: 'string',
      description: descriptions.name,
      validation: rule => rule.required().error(validation.nameRequired)
    }),
    defineField({
      name: 'username',
      title: fields.username,
      type: 'string',
      description: descriptions.username,
      validation: rule => rule.required().error(validation.usernameRequired)
    }),
    defineField({
      name: 'bio',
      title: fields.bio,
      type: 'text',
      description: descriptions.bio,
      rows: 4,
      validation: rule => rule.required().error(validation.bioRequired)
    }),
    define<PERSON>ield({
      name: 'avatar',
      title: fields.avatar,
      type: 'image',
      description: descriptions.avatar,
    }),
  ]
})