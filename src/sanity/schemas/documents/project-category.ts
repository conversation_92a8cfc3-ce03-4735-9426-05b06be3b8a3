import {FiFile} from "react-icons/fi";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {orderRankField, orderRankOrdering} from "@sanity/orderable-document-list";
import {projectCategoryDict} from "../../dictionary/studio/schemas/documents/project-category";

const { document, fields, validation, descriptions } = projectCategoryDict;

export default defineType({
  name: 'projectCategory',
  title: document.title,
  type: 'document',
  icon: FiFile,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  orderings: [orderRankOrdering],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      options: {
        source: 'title',
      },
      description: descriptions.slug,
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    orderRankField({ 
      type: 'projectCategory' 
    }),
  ]
})