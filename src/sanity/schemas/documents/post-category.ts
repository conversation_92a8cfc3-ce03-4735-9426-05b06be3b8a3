import {FiFile} from "react-icons/fi";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {orderRankField, orderRankOrdering} from "@sanity/orderable-document-list";
import {postCategoryDict} from "../../dictionary/studio/schemas/documents/post-category";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = postCategoryDict;

export default defineType({
  name: 'postCategory',
  title: document.title,
  type: 'document',
  icon: FiFile,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  orderings: [orderRankOrdering],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'title',
      },
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    defineField({
      name: 'categoryColor',
      title: fields.categoryColor,
      type: 'simplerColor',
      description: descriptions.categoryColor
    }),
    orderRankField({ 
      type: 'postCategory' 
    }),
  ]
})