import {Star} from "lucide-react";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {orderRankField, orderRankOrdering} from "@sanity/orderable-document-list";
import {testimonialDict} from "../../dictionary/studio/schemas/documents/testimonial";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = testimonialDict;

export default defineType({
  name: 'testimonial',
  title: document.title,
  type: 'document',
  icon: Star,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  orderings: [orderRankOrdering],
  fields: [
    defineField({
      name: 'name',
      title: fields.name,
      type: 'string',
      description: descriptions.name,
      validation: rule => rule.required().error(validation.nameRequired)
    }),
    defineField({
      name: 'jobTitle',
      title: fields.jobTitle,
      type: 'string',
      description: descriptions.jobTitle,
      validation: rule => rule.required().error(validation.jobTitleRequired)
    }),
    define<PERSON>ield({
      name: 'company',
      title: fields.company,
      type: 'string',
      description: descriptions.company,
    }),
    defineField({
      name: 'quote',
      title: fields.quote,
      type: 'text',
      description: descriptions.quote,
      rows: 6,
      validation: rule => rule.required().error(validation.quoteRequired)
    }),
    defineField({
      name: 'avatar',
      title: fields.avatar,
      type: 'image',
      description: descriptions.avatar,
      validation: rule => rule.required().error(validation.avatarRequired)
    }),
    defineField({
      name: 'logo',
      title: fields.logo,
      type: 'image',
      description: descriptions.logo,
      validation: rule => rule.required().error(validation.logoRequired)
    }),
    orderRankField({ 
      type: 'postCategory' 
    }),
  ],
})