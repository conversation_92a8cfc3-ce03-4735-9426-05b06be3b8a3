import {FiFile} from "react-icons/fi";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {postDict} from "../../dictionary/studio/schemas/documents/post";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = postDict;

export default defineType({
  name: 'post',
  title: document.title,
  type: 'document',
  icon: FiFile,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'title',
      },
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    defineField({
      name: 'category',
      title: fields.category,
      type: 'reference',
      description: descriptions.category,
      to: { type: 'postCategory' },
      validation: rule => rule.required().error(validation.categoryRequired)
    }),
    defineField({
      name: 'author',
      title: fields.author,
      type: 'reference',
      description: descriptions.author,
      to: { type: 'author' },
      validation: rule => rule.required().error(validation.authorRequired)
    }),
    defineField({
      name: 'excerpt',
      title: fields.excerpt,
      type: 'text',
      description: descriptions.excerpt,
      rows: 4  
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      description: descriptions.content,
      of: [
        { type: 'block' },
        { type: 'callToActionObject' },
        { type: 'singleImageObject' },
        { type: 'videoObject' }
      ],
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      description: descriptions.image,
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string',
          description: descriptions.altText,
        }),
        defineField({
          name: 'caption',
          title: fields.caption,
          type: 'string',
          description: descriptions.caption,
        }),
      ],
    }),
    defineField({
      title: fields.relatedPostsType,
      name: "relatedPostsType",
      type: "string",
      description: descriptions.relatedPostsType,
      options: {
        list: [
          { title: fields.autofill, value: "autofill" },
          { title: fields.custom, value: "custom" },
        ],
      },
      initialValue: 'autofill',
    }),
    defineField({
      title: fields.chooseRelatedPosts,
      name: "customRelatedPosts",
      type: "array",
      description: descriptions.chooseRelatedPosts,
      of: [{ 
        type: 'reference', 
        to: [{ type: 'post' }] 
      }],
      validation: rule => rule.max(3).error(validation.maxRelatedPosts),
      hidden: ({ parent }) => parent?.relatedPosts !== 'custom'
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
  ]
})