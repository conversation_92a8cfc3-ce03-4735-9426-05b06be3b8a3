import {fieldsets} from "../misc/fieldsets";
import {BriefcaseBusiness} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {projectDict} from "../../dictionary/studio/schemas/documents/project";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = projectDict;

export default defineType({
  name: 'project',
  title: document.title,
  type: 'document',
  icon: BriefcaseBusiness,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'title',
      },
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    defineField({
      name: 'category',
      title: fields.category,
      type: 'reference',
      description: descriptions.category,
      to: [{ type: 'projectCategory' }],
      validation: rule => rule.required().error(validation.categoryRequired)
    }),
    defineField({
      name: 'excerpt',
      title: fields.excerpt,
      type: 'text',
      description: descriptions.excerpt,
      rows: 4  
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      description: descriptions.image,
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string',
          description: descriptions.altText,
        }),
        defineField({
          name: 'caption',
          title: fields.caption,
          type: 'string',
          description: descriptions.caption,
        }),
      ],
    }),
    defineField({
      name: 'pageBuilder',
      title: fields.pageBuilder,
      type: 'pageBuilder',
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
  ]
})