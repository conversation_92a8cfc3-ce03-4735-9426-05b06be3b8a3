import {File} from "lucide-react";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {formDict} from "../../dictionary/studio/schemas/documents/form";

// Extract constants for cleaner code
const { document, fields, validation } = formDict;

export default defineType({
  name: 'form',
  title: document.title,
  type: 'document',
  icon: File,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.formTitle,
      type: 'string',
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'submitButtonText',
      title: fields.submitButtonText,
      type: 'string',
      validation: rule => rule.required().error(validation.submitButtonTextRequired)
    }),
    defineField({
      name: 'fields',
      title: fields.formFields,
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'name',
              title: fields.name,
              type: 'string',
            }),
            defineField({
              name: 'placeholder',
              title: fields.placeholder,
              type: 'string',
            }),
            defineField({
              name: "inputType",
              title: fields.inputType,
              type: "string",
              options: {
                list: [
                  { title: fields.text, value: "text" },
                  { title: fields.textarea, value: "textarea" },
                  { title: fields.email, value: "email" },
                  { title: fields.telephone, value: "tel" },
                ],
              },
              initialValue: 'text',
            }),
            defineField({
              name: 'isRequired',
              title: fields.isRequired,
              type: 'boolean',
              initialValue: false
            }),
          ],
        },
      ],
    }),
    
  ]
})