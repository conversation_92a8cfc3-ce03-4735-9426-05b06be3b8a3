import { MapPin } from "lucide-react";
import { defineField, defineType } from "sanity";
import { fieldsets } from "../misc/fieldsets";
import { fieldGroups } from "../misc/field-groups";
import { centerDict } from "../../dictionary/studio/schemas/documents/center";

const { document, fields, validation, descriptions } = centerDict;

export default defineType({
  name: 'center',
  title: document.title,
  type: 'document',
  icon: MapPin,
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    // Group 1: General Information (content)
    defineField({
      name: 'name',
      title: fields.name,
      type: 'string',
      description: descriptions.name,
      validation: rule => rule.required().error(validation.nameRequired),
      group: 'general'
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'name',
      },
      validation: rule => rule.required().error(validation.slugRequired),
      group: 'general'
    }),
    defineField({
      name: 'centerType',
      title: fields.centerType,
      type: 'string',
      description: descriptions.centerType,
      options: {
        list: [
          { title: fields.centerType_audi, value: 'audi' },
          { title: fields.centerType_skoda, value: 'skoda' },
          { title: fields.centerType_occasions, value: 'occasions' },
          { title: fields.centerType_multibrands, value: 'multibrands' },
        ],
        layout: 'radio'
      },
      validation: rule => rule.required().error(validation.centerTypeRequired),
      group: 'general'
    }),
    defineField({
      name: 'autoscoutSellerId',
      title: fields.autoscoutSellerId,
      type: 'string',
      description: descriptions.autoscoutSellerId,
      validation: rule => rule.required().error(validation.autoscoutSellerIdRequired),
      group: 'general'
    }),

    // Group 2: Content & Page Building (content)
    defineField({
      name: 'shortDescription',
      title: fields.shortDescription,
      type: 'text',
      description: descriptions.shortDescription,
      rows: 4,
      group: 'content'
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      description: descriptions.image,
      options: {
        hotspot: true
      },
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string',
          validation: rule => rule.required(),
        }),
        defineField({
          name: 'caption',
          title: fields.caption,
          type: 'string',
        }),
      ],
      group: 'content'
    }),
    defineField({
      name: 'pageBuilder',
      title: fields.pageBuilder,
      type: 'pageBuilder',
      description: descriptions.pageBuilder,
      group: 'content'
    }),

    // Group 3: Address & Contact (addressContact)
    defineField({
      name: 'address',
      title: fields.address,
      type: 'addressObject',
      description: descriptions.address,
      group: 'addressContact'
    }),
    defineField({
      name: 'contact',
      title: fields.contact,
      type: 'contactObject',
      description: descriptions.contact,
      group: 'addressContact'
    }),
    defineField({
      name: 'openingHours',
      title: fields.openingHours,
      type: 'array',
      description: descriptions.openingHours,
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'day',
              title: fields.day,
              type: 'string',
              options: {
                list: [
                  { title: fields.monday, value: 'Monday' },
                  { title: fields.tuesday, value: 'Tuesday' },
                  { title: fields.wednesday, value: 'Wednesday' },
                  { title: fields.thursday, value: 'Thursday' },
                  { title: fields.friday, value: 'Friday' },
                  { title: fields.saturday, value: 'Saturday' },
                  { title: fields.sunday, value: 'Sunday' },
                ]
              }
            }),
            defineField({
              name: 'hours',
              title: fields.hours,
              type: 'string',
              placeholder: descriptions.hours
            })
          ],
          preview: {
            select: { day: 'day', hours: 'hours' },
            prepare: ({ day, hours }) => ({ title: day, subtitle: hours })
          }
        }
      ],
      group: 'addressContact'
    }),

    // Group 4: Brand & Visual Identity (branding)
    defineField({
      name: 'brandConfig',
      title: fields.brandConfig,
      type: 'object',
      description: descriptions.brandConfig,
      fields: [
        defineField({
          name: 'primaryColor',
          title: fields.primaryColor,
          type: 'simplerColor'
        }),
        defineField({
          name: 'secondaryColor',
          title: fields.secondaryColor,
          type: 'simplerColor'
        }),
        defineField({
          name: 'logo',
          title: fields.logo,
          type: 'image',
          options: {
            hotspot: true
          },
          fields: [
            defineField({
              name: 'altText',
              title: fields.altText,
              type: 'string',
              validation: rule => rule.required()
            })
          ]
        })
      ],
      group: 'branding'
    }),
    defineField({
      name: 'certifications',
      title: fields.certifications,
      type: 'array',
      description: descriptions.certifications,
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'name',
              title: fields.certificationName,
              type: 'string'
            }),
            defineField({
              name: 'logo',
              title: fields.certificationLogo,
              type: 'image',
              options: { hotspot: true }
            }),
            defineField({
              name: 'description',
              title: fields.certificationDescription,
              type: 'text',
              rows: 2
            })
          ],
          preview: {
            select: { name: 'name', logo: 'logo' },
            prepare: ({ name, logo }) => ({ title: name, media: logo })
          }
        }
      ],
      group: 'branding'
    }),

    // Group 5: Services & Team (services)
    defineField({
      name: 'specialties',
      title: fields.specialties,
      type: 'array',
      description: descriptions.specialties,
      of: [
        {
          type: 'string',
          options: {
            list: [
              { title: fields.specialty_newVehicles, value: 'New Vehicles' },
              { title: fields.specialty_usedVehicles, value: 'Used Vehicles' },
              { title: fields.specialty_electricVehicles, value: 'Electric Vehicles' },
              { title: fields.specialty_sportVehicles, value: 'Sport Vehicles' },
              { title: fields.specialty_luxuryVehicles, value: 'Luxury Vehicles' },
              { title: fields.specialty_familyVehicles, value: 'Family Vehicles' },
              { title: fields.specialty_professionalLeasing, value: 'Professional Leasing' },
              { title: fields.specialty_afterSalesService, value: 'After-Sales Service' },
              { title: fields.specialty_bodyShop, value: 'Body Shop' },
              { title: fields.specialty_partsAccessories, value: 'Parts & Accessories' },
              { title: fields.specialty_vehicleTradeIn, value: 'Vehicle Trade-In' },
              { title: fields.specialty_customFinancing, value: 'Custom Financing' },
            ]
          }
        }
      ],
      group: 'services'
    }),
    defineField({
      name: 'team',
      title: fields.team,
      type: 'array',
      description: descriptions.team,
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'name',
              title: fields.memberName,
              type: 'string',
              validation: rule => rule.required().error(validation.memberNameRequired)
            }),
            defineField({
              name: 'role',
              title: fields.role,
              type: 'string',
              validation: rule => rule.required().error(validation.roleRequired),
              options: {
                list: [
                  { title: fields.role_centerDirector, value: 'Center Director' },
                  { title: fields.role_salesAdvisor, value: 'Sales Advisor' },
                  { title: fields.role_serviceAdvisor, value: 'Service Advisor' },
                  { title: fields.role_workshopManager, value: 'Workshop Manager' },
                  { title: fields.role_financeAdvisor, value: 'Finance Advisor' },
                  { title: fields.role_serviceReception, value: 'Service Reception' },
                ]
              }
            }),
            defineField({
              name: 'photo',
              title: fields.photo,
              type: 'image',
              options: { hotspot: true }
            }),
            defineField({
              name: 'directPhone',
              title: fields.directPhone,
              type: 'string'
            }),
            defineField({
              name: 'directEmail',
              title: fields.directEmail,
              type: 'string',
              validation: rule => rule.email()
            }),
            defineField({
              name: 'specialization',
              title: fields.specialization,
              type: 'array',
              description: descriptions.specialization,
              of: [{ type: 'string' }]
            })
          ],
          preview: {
            select: {
              name: 'name',
              role: 'role',
              photo: 'photo'
            },
            prepare: ({ name, role, photo }) => ({
              title: name,
              subtitle: role,
              media: photo
            })
          }
        }
      ],
      group: 'services'
    }),
    defineField({
      name: 'serviceAreas',
      title: fields.serviceAreas,
      type: 'array',
      description: descriptions.serviceAreas,
      of: [{ type: 'string' }],
      group: 'services'
    }),

    // Group 6: Integration & API (integration)
    defineField({
      name: 'apiSettings',
      title: fields.apiSettings,
      type: 'object',
      description: descriptions.apiSettings,
      fields: [
        defineField({
          name: 'enableAutoSync',
          title: fields.enableAutoSync,
          type: 'boolean',
          description: descriptions.enableAutoSync,
          initialValue: true
        }),
        defineField({
          name: 'syncFrequency',
          title: fields.syncFrequency,
          type: 'string',
          options: {
            list: [
              { title: fields.syncFreq_15min, value: '15min' },
              { title: fields.syncFreq_1hour, value: '1hour' },
              { title: fields.syncFreq_6hours, value: '6hours' },
              { title: fields.syncFreq_daily, value: 'daily' },
            ]
          },
          initialValue: '1hour'
        })
      ],
      group: 'integration'
    }),
    defineField({
      name: 'searchDefaults',
      title: fields.searchDefaults,
      type: 'object',
      description: descriptions.searchDefaults,
      fields: [
        defineField({
          name: 'defaultSort',
          title: fields.defaultSort,
          type: 'string',
          options: {
            list: [
              { title: fields.sort_priceAsc, value: 'price_asc' },
              { title: fields.sort_priceDesc, value: 'price_desc' },
              { title: fields.sort_yearDesc, value: 'year_desc' },
              { title: fields.sort_mileageAsc, value: 'mileage_asc' },
              { title: fields.sort_relevance, value: 'relevance' },
            ]
          },
          initialValue: 'relevance'
        }),
        defineField({
          name: 'itemsPerPage',
          title: fields.itemsPerPage,
          type: 'number',
          validation: rule => rule.min(6).max(50).error(validation.itemsPerPageRange),
          initialValue: 12
        })
      ],
      group: 'integration'
    }),
    defineField({
      name: 'displaySettings',
      title: fields.displaySettings,
      type: 'object',
      description: descriptions.displaySettings,
      fields: [
        defineField({
          name: 'showPrices',
          title: fields.showPrices,
          type: 'boolean',
          initialValue: true
        }),
        defineField({
          name: 'showFinancing',
          title: fields.showFinancing,
          type: 'boolean',
          initialValue: true
        }),
        defineField({
          name: 'featuredBadge',
          title: fields.featuredBadge,
          type: 'string',
          initialValue: 'Featured'
        })
      ],
      group: 'integration'
    }),

    // Group 7: SEO & Marketing (seo)
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
      group: 'seo'
    }),
  ],
  preview: {
    select: {
      title: 'name',
      centerType: 'centerType',
      city: 'address.city',
      image: 'image'
    },
    prepare: ({ title, centerType, city, image }) => ({
      title,
      subtitle: `${centerType?.toUpperCase() || ''} • ${city || ''}`,
      media: image || MapPin
    })
  },
  orderings: [
    {
      title: 'Name',
      name: 'nameAsc',
      by: [{ field: 'name', direction: 'asc' }]
    },
    {
      title: 'Center Type',
      name: 'typeAsc',
      by: [{ field: 'centerType', direction: 'asc' }]
    },
    {
      title: 'City',
      name: 'cityAsc', 
      by: [{ field: 'address.city', direction: 'asc' }]
    }
  ]
});
