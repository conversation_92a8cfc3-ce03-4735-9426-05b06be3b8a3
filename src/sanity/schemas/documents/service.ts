import {fieldsets} from "../misc/fieldsets";
import {BriefcaseBusiness} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {orderRankField, orderRankOrdering} from "@sanity/orderable-document-list";
import {serviceDict} from "../../dictionary/studio/schemas/documents/service";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = serviceDict;

export default defineType({
  name: 'service',
  title: document.title,
  type: 'document',
  icon: BriefcaseBusiness,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  orderings: [orderRankOrdering],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'title',
      },
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    defineField({
      name: 'shortDescription',
      title: fields.shortDescription,
      type: 'text',
      description: descriptions.shortDescription,
      rows: 4
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      description: descriptions.image,
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string',
          description: descriptions.altText,
        }),
        defineField({
          name: 'caption',
          title: fields.caption,
          type: 'string',
          description: descriptions.caption,
        }),
      ],
    }),
    defineField({
      name: 'pageBuilder',
      title: fields.pageBuilder,
      type: 'pageBuilder',
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
    orderRankField({ 
      type: 'service' 
    }),
  ]
})