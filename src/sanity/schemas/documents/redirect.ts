import {Link} from "lucide-react";
import {defineField, defineType, SanityDocumentLike} from "sanity";
import {redirectDict} from "../../dictionary/studio/schemas/documents/redirect";

const { document, fields, validation, descriptions } = redirectDict;

function isValidInternalPath(value: string | undefined) {
  if (!value) {
    return validation.sourceRequired;
  } else if (!value.startsWith("/")) {
    return validation.invalidSource;
  } else if (/[^a-zA-Z0-9\-_/:]/.test(value)) {
    return validation.invalidSource;
  } else if (/:[^/]+:/.test(value)) {
    return validation.invalidSource;
  } else if (
    value.split("/").some((part) => part.includes(":") && !part.startsWith(":"))
  ) {
    return validation.invalidSource;
  }
  return true;
}

function isValidUrl(value: string | undefined) {
  try {
    new URL(value || "");
    return true;
  } catch {
    return validation.invalidDestination;
  }
}

export default defineType({
  name: "redirect",
  title: document.title,
  type: "document",
  icon: Link,
  validation: (Rule) => 
    Rule.custom((doc: SanityDocumentLike | undefined) => {
      if (doc && doc.source === doc.destination) {
        return ["source", "destination"].map((field) => ({
          message: validation.sameSourceDestination,
          path: [field],
        }));
      }
      return true;
    }),
  fields: [
    defineField({
      name: "source",
      title: fields.source,
      type: "string",
      description: descriptions.source,
      validation: (Rule) => Rule.required().custom(isValidInternalPath),
    }),
    defineField({
      name: "destination",
      title: fields.destination,
      type: "string",
      description: descriptions.destination,
      validation: (Rule) =>
        Rule.required().custom((value: string | undefined) => {
          const urlValidation = isValidUrl(value);
          const pathValidation = isValidInternalPath(value);

          if (urlValidation === true || pathValidation === true) {
            return true;
          }
          return typeof urlValidation === "boolean"
            ? pathValidation
            : urlValidation;
        }),
    }),
    defineField({
      name: "permanent",
      title: fields.permanent ?? "Permanent",
      type: "boolean",
      initialValue: true,
      description: descriptions.permanent ?? undefined,
    }),
    defineField({
      name: "isEnabled",
      title: fields.isEnabled ?? "Enabled",
      description: descriptions.isEnabled ?? undefined,
      type: "boolean",
      initialValue: true,
    }),
  ],
});