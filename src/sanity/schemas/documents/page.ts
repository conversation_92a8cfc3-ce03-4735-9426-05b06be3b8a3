import {File} from "lucide-react";
import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {pageDict} from "../../dictionary/studio/schemas/documents/page";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = pageDict;

export default defineType({
  name: 'page',
  title: document.title,
  type: 'document',
  icon: File,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      validation: rule => rule.required().error(validation.titleRequired)
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      description: descriptions.slug,
      options: {
        source: 'title',
      },
      validation: rule => rule.required().error(validation.slugRequired)
    }),
    // Parent page reference for hierarchy
    defineField({
      name: 'parent',
      title: '<PERSON><PERSON> Page',
      type: 'reference',
      to: [{type: 'page'}],
      description: 'Select a parent page to create a page hierarchy',
      validation: rule => rule.custom((current, context) => {
        // Prevent self-reference
        if (current?._ref === context.document?._id) {
          return 'A page cannot be its own parent';
        }
        return true;
      })
    }),
    // Virtual field to show child pages (computed automatically)
    defineField({
      name: 'children',
      title: 'Child Pages',
      type: 'array',
      of: [{
        type: 'reference',
        to: [{type: 'page'}]
      }],
      description: 'Pages that have this page as their parent (automatically populated)',
      readOnly: true,
      hidden: true, // Hide from form, but available in queries
    }),
    defineField({
      name: 'pageBuilder',
      title: fields.pageBuilder,
      type: 'pageBuilder',
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
  ],
  preview: {
    select: {
      title: 'title',
      parent: 'parent.title',
      slug: 'slug.current'
    },
    prepare(selection) {
      const { title, parent, slug } = selection;
      return {
        title: title,
        subtitle: parent ? `Child of: ${parent}` : `/${slug || ''}`,
      };
    }
  }
})