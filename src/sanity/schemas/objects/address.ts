import { defineField, defineType } from "sanity";
import { addressObjectDict } from '../../dictionary/studio/schemas/objects/address';

const { fields, validation, descriptions } = addressObjectDict;

export default defineType({
  name: "addressObject",
  title: fields.title,
  type: "object",
  fields: [
    defineField({
      name: "street",
      title: fields.street,
      type: "string",
      description: descriptions.street,
      validation: rule => rule.required().error(validation.streetRequired),
    }),
    defineField({
      name: "postalCode",
      title: fields.postalCode,
      type: "string",
      description: descriptions.postalCode,
      validation: rule => rule.required().error(validation.postalCodeRequired),
    }),
    defineField({
      name: "city",
      title: fields.city,
      type: "string",
      description: descriptions.city,
      validation: rule => rule.required().error(validation.cityRequired),
    }),
    defineField({
      name: "region",
      title: fields.region,
      type: "string",
      description: descriptions.region,
    }),
    defineField({
      name: "country",
      title: fields.country,
      type: "string",
      description: descriptions.country,
      initialValue: "Switzerland",
    }),
    defineField({
      name: "coordinates",
      title: fields.coordinates,
      type: "geopoint",
      description: descriptions.coordinates,
    }),
  ],
});
