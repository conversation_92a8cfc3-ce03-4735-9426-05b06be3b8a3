import {Video} from "lucide-react";
import {defineField, defineType} from "sanity";
import {videoObjectDict} from '../../dictionary/studio/schemas/objects/video';

const { fields } = videoObjectDict;

export default defineType({
  name: 'videoObject',
  title: fields.title,
  type: 'object',
  fields: [
    defineField({
      name: 'title',
      title: fields.videoTitle,
      type: 'string',
    }),
    defineField({
      name: 'videoUrl',
      title: fields.videoUrl,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'title',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Video,
      }
    },
  },
})