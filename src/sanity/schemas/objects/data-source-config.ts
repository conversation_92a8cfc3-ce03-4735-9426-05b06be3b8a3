import {defineField, defineType} from 'sanity'
import {Database} from 'lucide-react'
import {dataSourceConfigObjectDict} from '../../dictionary/studio/schemas/objects/data-source-config';

const { fields } = dataSourceConfigObjectDict;

export const dataSourceConfigObject = defineType({
  name: 'dataSourceConfig',
  title: fields.title,
  type: 'object',
  icon: Database,
  description: fields.description,
  fields: [
    defineField({
      name: 'sourceType',
      title: fields.sourceType,
      type: 'string',
      options: {
        list: [
          { title: fields.sourceType_static, value: 'static' },
          { title: fields.sourceType_documentCount, value: 'documentCount' },
          { title: fields.sourceType_fieldSum, value: 'fieldSum' },
          { title: fields.sourceType_fieldAverage, value: 'fieldAverage' },
          { title: fields.sourceType_api, value: 'api' },
          { title: fields.sourceType_groq, value: 'groq' }
        ]
      },
      initialValue: 'static',
      description: fields.sourceType_description
    }),
    // Static number configuration
    defineField({
      name: 'staticValue',
      title: fields.staticValue,
      type: 'number',
      hidden: ({ parent }) => parent?.sourceType !== 'static',
      description: fields.staticValue_description
    }),
    // Document count configuration
    defineField({
      name: 'documentCountConfig',
      title: fields.documentCountConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'documentCount',
      fields: [
        defineField({
          name: 'documentType',
          title: fields.documentCountConfig_documentType,
          type: 'string',
          options: {
            list: [
              { title: fields.documentCountConfig_documentType_post, value: 'post' },
              { title: fields.documentCountConfig_documentType_project, value: 'project' },
              { title: fields.documentCountConfig_documentType_service, value: 'service' },
              { title: fields.documentCountConfig_documentType_testimonial, value: 'testimonial' },
              { title: fields.documentCountConfig_documentType_author, value: 'author' },
              { title: fields.documentCountConfig_documentType_page, value: 'page' },
              { title: fields.documentCountConfig_documentType_form, value: 'form' },
              { title: fields.documentCountConfig_documentType_custom, value: 'custom' }
            ]
          },
          initialValue: 'post'
        }),
        defineField({
          name: 'customDocumentType',
          title: fields.documentCountConfig_customDocumentType,
          type: 'string',
          hidden: ({ parent }) => parent?.documentType !== 'custom',
          description: fields.documentCountConfig_customDocumentType_description
        }),
        defineField({
          name: 'filters',
          title: fields.documentCountConfig_filters,
          type: 'array',
          description: fields.documentCountConfig_filters_description,
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'field',
                  title: fields.documentCountConfig_filters_field,
                  type: 'string',
                  placeholder: fields.documentCountConfig_filters_field_placeholder
                }),
                defineField({
                  name: 'operator',
                  title: fields.documentCountConfig_filters_operator,
                  type: 'string',
                  options: {
                    list: [
                      { title: fields.documentCountConfig_filters_operator_eq, value: '==' },
                      { title: fields.documentCountConfig_filters_operator_neq, value: '!=' },
                      { title: fields.documentCountConfig_filters_operator_gt, value: '>' },
                      { title: fields.documentCountConfig_filters_operator_lt, value: '<' },
                      { title: fields.documentCountConfig_filters_operator_gte, value: '>=' },
                      { title: fields.documentCountConfig_filters_operator_lte, value: '<=' },
                      { title: fields.documentCountConfig_filters_operator_match, value: 'match' },
                      { title: fields.documentCountConfig_filters_operator_in, value: 'in' },
                      { title: fields.documentCountConfig_filters_operator_references, value: 'references' }
                    ]
                  },
                  initialValue: '=='
                }),
                defineField({
                  name: 'value',
                  title: fields.documentCountConfig_filters_value,
                  type: 'string',
                  description: fields.documentCountConfig_filters_value_description
                })
              ]
            }
          ]
        })
      ]
    }),
    // Field sum configuration
    defineField({
      name: 'fieldSumConfig',
      title: fields.fieldSumConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'fieldSum',
      fields: [
        defineField({
          name: 'field',
          title: fields.fieldSumConfig_field,
          type: 'string',
          description: fields.fieldSumConfig_field_description
        })
      ]
    }),
    // Field average configuration
    defineField({
      name: 'fieldAverageConfig',
      title: fields.fieldAverageConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'fieldAverage',
      fields: [
        defineField({
          name: 'field',
          title: fields.fieldAverageConfig_field,
          type: 'string',
          description: fields.fieldAverageConfig_field_description
        })
      ]
    }),
    // API configuration
    defineField({
      name: 'apiConfig',
      title: fields.apiConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'api',
      fields: [
        defineField({
          name: 'endpoint',
          title: fields.apiConfig_endpoint,
          type: 'url',
          description: fields.apiConfig_endpoint_description
        }),
        defineField({
          name: 'method',
          title: fields.apiConfig_method,
          type: 'string',
          options: {
            list: [
              { title: fields.apiConfig_method_get, value: 'GET' },
              { title: fields.apiConfig_method_post, value: 'POST' }
            ]
          },
          initialValue: 'GET'
        }),
        defineField({
          name: 'headers',
          title: fields.apiConfig_headers,
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'key',
                  title: fields.apiConfig_headers_key,
                  type: 'string'
                }),
                defineField({
                  name: 'value',
                  title: fields.apiConfig_headers_value,
                  type: 'string'
                })
              ]
            }
          ]
        }),
        defineField({
          name: 'requestBody',
          title: fields.apiConfig_requestBody,
          type: 'text',
          rows: 4,
          hidden: ({ parent }) => parent?.method !== 'POST',
          description: fields.apiConfig_requestBody_description
        }),
        defineField({
          name: 'responseTransform',
          title: fields.apiConfig_responseTransform,
          type: 'object',
          fields: [
            defineField({
              name: 'dataPath',
              title: fields.apiConfig_responseTransform_dataPath,
              type: 'string',
              placeholder: fields.apiConfig_responseTransform_dataPath_placeholder,
              description: fields.apiConfig_responseTransform_dataPath_description
            }),
            defineField({
              name: 'transform',
              title: fields.apiConfig_responseTransform_transform,
              type: 'text',
              rows: 3,
              placeholder: fields.apiConfig_responseTransform_transform_placeholder,
              description: fields.apiConfig_responseTransform_transform_description
            })
          ]
        }),
        defineField({
          name: 'cacheConfig',
          title: fields.apiConfig_cacheConfig,
          type: 'object',
          fields: [
            defineField({
              name: 'enabled',
              title: fields.apiConfig_cacheConfig_enabled,
              type: 'boolean',
              initialValue: true
            }),
            defineField({
              name: 'duration',
              title: fields.apiConfig_cacheConfig_duration,
              type: 'number',
              initialValue: 15,
              validation: Rule => Rule.min(1).max(1440),
              hidden: ({ parent }) => !parent?.enabled,
              description: fields.apiConfig_cacheConfig_duration_description
            })
          ]
        })
      ]
    }),
    // Custom GROQ configuration
    defineField({
      name: 'groqConfig',
      title: fields.groqConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'groq',
      fields: [
        defineField({
          name: 'query',
          title: fields.groqConfig_query,
          type: 'text',
          rows: 6,
          placeholder: fields.groqConfig_query_placeholder,
          description: fields.groqConfig_query_description
        }),
        defineField({
          name: 'params',
          title: fields.groqConfig_params,
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'key',
                  title: fields.groqConfig_params_key,
                  type: 'string',
                  placeholder: fields.groqConfig_params_key_placeholder
                }),
                defineField({
                  name: 'value',
                  title: fields.groqConfig_params_value,
                  type: 'string',
                  placeholder: fields.groqConfig_params_value_placeholder
                }),
                defineField({
                  name: 'type',
                  title: fields.groqConfig_params_type,
                  type: 'string',
                  options: {
                    list: [
                      { title: fields.groqConfig_params_type_string, value: 'string' },
                      { title: fields.groqConfig_params_type_number, value: 'number' },
                      { title: fields.groqConfig_params_type_boolean, value: 'boolean' },
                      { title: fields.groqConfig_params_type_reference, value: 'reference' }
                    ]
                  },
                  initialValue: 'string'
                })
              ]
            }
          ]
        })
      ]
    }),
    // Error handling and fallbacks
    defineField({
      name: 'errorHandling',
      title: fields.errorHandling,
      type: 'object',
      fields: [
        defineField({
          name: 'fallbackValue',
          title: fields.errorHandling_fallbackValue,
          type: 'string',
          description: fields.errorHandling_fallbackValue_description
        }),
        defineField({
          name: 'retryAttempts',
          title: fields.errorHandling_retryAttempts,
          type: 'number',
          validation: Rule => Rule.min(0).max(5),
          initialValue: 2,
          description: fields.errorHandling_retryAttempts_description
        }),
        defineField({
          name: 'timeout',
          title: fields.errorHandling_timeout,
          type: 'number',
          validation: Rule => Rule.min(1).max(60),
          initialValue: 10,
          description: fields.errorHandling_timeout_description
        })
      ]
    })
  ]
});
