import {defineField, defineType} from "sanity";
import {seoObjectDict} from '../../dictionary/studio/schemas/objects/seo';

const { fields } = seoObjectDict;

export default defineType({
  name: "seoObject",
  title: fields.title,
  type: "object",
  fields: [
    defineField({
      name: "title",
      title: fields.seoTitle,
      description: fields.seoTitleDesc,
      type: "string",
    }),
    defineField({
      name: "description",
      title: fields.description,
      type: "text",
      rows: 3
    }),
    defineField({
      name: "noIndex",
      title: fields.noIndex,
      type: "boolean",
      initialValue: false,
      description: fields.noIndexDesc,
    }),
    defineField({
      name: "image",
      title: fields.image,
      type: "image",
      options: { hotspot: true },
      description: fields.imageDesc,
    }),
  ],
});