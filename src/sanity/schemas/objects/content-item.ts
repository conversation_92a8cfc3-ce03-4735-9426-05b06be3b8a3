import {defineField, defineType} from 'sanity'
import {FileText} from 'lucide-react'
import {contentItemObjectDict} from '../../dictionary/studio/schemas/objects/content-item';

const { fields } = contentItemObjectDict;

export const contentItemObject = defineType({
  name: 'contentItem',
  title: fields.title,
  type: 'object',
  icon: FileText,
  fields: [
    defineField({
      name: 'type',
      title: fields.type,
      type: 'string',
      options: {
        list: [
          { title: fields.type_manual, value: 'manual' },
          { title: fields.type_reference, value: 'reference' }
        ]
      },
      initialValue: 'manual',
      description: fields.type_description
    }),

    // Manual content fields
    defineField({
      name: 'title',
      title: fields.title_field,
      type: 'string',
      hidden: ({ parent }) => parent?.type !== 'manual',
      validation: Rule => Rule.custom((value, context) => {
        const parent = context.parent as { type: string | undefined }
        return parent?.type  === 'manual' && !value ? fields.title_required : true
      })
    }),
    defineField({
      name: 'excerpt',
      title: fields.excerpt,
      type: 'text',
      rows: 3,
      hidden: ({ parent }) => parent?.type !== 'manual',
      description: fields.excerpt_description
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      hidden: ({ parent }) => parent?.type !== 'manual',
      fields: [
        defineField({
          name: 'alt',
          title: fields.image_alt,
          type: 'string'
        })
      ]
    }),

    // Reference field
    defineField({
      name: 'reference',
      title: fields.reference,
      type: 'reference',
      hidden: ({ parent }) => parent?.type !== 'reference',
      to: [
        { type: 'post' },
        { type: 'project' },
        { type: 'service' },
        { type: 'page' },
        { type: 'testimonial' }
      ],
      validation: Rule => Rule.custom((value, context) => {
        const parent = context.parent as { type: string | undefined }
        return parent?.type === 'reference' && !value ? fields.reference_required : true
      })
    }),

    // Common fields for both types
    defineField({
      name: 'categories',
      title: fields.categories,
      type: 'array',
      of: [{ type: 'string' }],
      description: fields.categories_description
    }),
    defineField({
      name: 'tags',
      title: fields.tags,
      type: 'array',
      of: [{ type: 'string' }],
      description: fields.tags_description
    }),
    defineField({
      name: 'featured',
      title: fields.featured,
      type: 'boolean',
      initialValue: false,
      description: fields.featured_description
    }),
    defineField({
      name: 'link',
      title: fields.link,
      type: 'object',
      fields: [
        defineField({
          name: 'label',
          title: fields.link_label,
          type: 'string',
          initialValue: fields.link_label_default
        }),
        defineField({
          name: 'url',
          title: fields.link_url,
          type: 'url',
          description: fields.link_url_description
        }),
        defineField({
          name: 'openInNewTab',
          title: fields.link_openInNewTab,
          type: 'boolean',
          initialValue: false
        })
      ]
    }),
    defineField({
      name: 'metadata',
      title: fields.metadata,
      type: 'object',
      fields: [
        defineField({
          name: 'author',
          title: fields.metadata_author,
          type: 'string'
        }),
        defineField({
          name: 'publishDate',
          title: fields.metadata_publishDate,
          type: 'datetime'
        }),
        defineField({
          name: 'priority',
          title: fields.metadata_priority,
          type: 'number',
          description: fields.metadata_priority_description,
          validation: Rule => Rule.min(0).max(100),
          initialValue: 0
        }),
        defineField({
          name: 'status',
          title: fields.metadata_status,
          type: 'string',
          options: {
            list: [
              { title: fields.metadata_status_published, value: 'published' },
              { title: fields.metadata_status_draft, value: 'draft' },
              { title: fields.metadata_status_archived, value: 'archived' }
            ]
          },
          initialValue: 'published'
        })
      ]
    })
  ],
  preview: {
    select: {
      title: 'title',
      referenceTitle: 'reference.title',
      type: 'type',
      image: 'image',
      referenceImage: 'reference.image',
      featured: 'featured'
    },
    prepare({ title, referenceTitle, type, image, referenceImage, featured }) {
      const displayTitle = type === 'reference' ? referenceTitle : title
      const displayImage = type === 'reference' ? referenceImage : image
      
      return {
        title: displayTitle || fields.title,
        subtitle: `${type === 'reference' ? fields.type_reference : fields.type_manual} ${fields.title}${featured ? ' • ' + fields.featured : ''}`,
        media: displayImage || FileText
      }
    }
  }
})
