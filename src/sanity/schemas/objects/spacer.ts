import {defineField, defineType} from "sanity";
import {BetweenHorizonalEnd} from "lucide-react";
import {SpacingInput, spacingOptions} from "@/sanity/components/spacing-input";
import {spacerObjectDict} from '../../dictionary/studio/schemas/objects/spacer';

const { fields } = spacerObjectDict;

export default defineType({
  name: 'spacerObject',
  title: fields.title,
  type: 'object',
  fields: [
    defineField({
      title: fields.spacing,
      name: "spacing",
      type: "string",
      options: {
        list: spacingOptions.map(({ title, value }) => ({ title, value })),
        layout: 'radio',
      },
      components: { input: SpacingInput },
      initialValue: 'small',
    }),
  ],
  preview: {
    select: {
      title: 'spacing',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: (title ?? fields.noTitle).charAt(0).toUpperCase() + 
              (title ?? fields.noTitle).slice(1),
        subtitle: fields.subtitle,
        media: BetweenHorizonalEnd,
      }
    },
  },
})