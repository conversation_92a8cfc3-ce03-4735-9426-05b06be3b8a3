import {LetterText} from "lucide-react";
import {defineField, defineType} from "sanity";
import {richTextObjectDict} from '../../dictionary/studio/schemas/objects/rich-text';

const { fields } = richTextObjectDict;

export default defineType({
  name: 'richTextObject',
  title: fields.richTextContent,
  type: 'object',
  fields: [
    defineField({
      name: 'richTextContent',
      type: 'array',
      title: fields.richTextContent,
      of: [
        {
          type: 'block',
          styles: [{ title: fields['richTextContent.styles.normal'], value: 'normal' }],
          lists: [],
        },
      ],
    }),
  ],
  preview: {
    prepare() {
      return {
        title: fields.richTextContent,
        subtitle: fields.richTextContent,
        media: LetterText,
      }
    },
  },
})