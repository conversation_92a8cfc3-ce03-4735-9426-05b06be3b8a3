import {defineField, defineType} from "sanity";
import {gridConfigObjectDict} from '../../dictionary/studio/schemas/objects/grid-config';

const { fields } = gridConfigObjectDict;

export const gridConfig = defineType({
  name: 'gridConfig',
  title: fields.columns,
  type: 'object',
  description: 'Configure how blocks are arranged in a grid layout',
  fields: [
    defineField({
      name: 'columns',
      title: fields.columns,
      type: 'object',
      fields: [
        {
          name: 'mobile',
          title: fields['columns.mobile'],
          type: 'number',
          validation: Rule => Rule.min(1).max(2),
          initialValue: 1
        },
        {
          name: 'tablet',
          title: fields['columns.tablet'],
          type: 'number',
          validation: Rule => Rule.min(1).max(4),
          initialValue: 2
        },
        {
          name: 'desktop',
          title: fields['columns.desktop'],
          type: 'number',
          validation: Rule => Rule.min(1).max(12),
          initialValue: 3
        }
      ]
    }),
    
    defineField({
      name: 'gap',
      title: fields.gap,
      type: 'string',
      options: {
        list: [
          { title: fields['gap.none'], value: '0' },
          { title: fields['gap.xs'], value: '1' },
          { title: fields['gap.sm'], value: '2' },
          { title: fields['gap.md'], value: '4' },
          { title: fields['gap.lg'], value: '6' },
          { title: fields['gap.xl'], value: '8' },
          { title: fields['gap.xxl'], value: '12' }
        ]
      },
      initialValue: '6'
    }),
    
    defineField({
      name: 'alignment',
      title: fields.alignment,
      type: 'string',
      options: {
        list: [
          { title: fields['alignment.start'], value: 'start' },
          { title: fields['alignment.center'], value: 'center' },
          { title: fields['alignment.end'], value: 'end' },
          { title: fields['alignment.stretch'], value: 'stretch' }
        ]
      },
      initialValue: 'stretch'
    }),
    
    defineField({
      name: 'justifyContent',
      title: fields.justifyContent,
      type: 'string',
      options: {
        list: [
          { title: fields['justifyContent.start'], value: 'start' },
          { title: fields['justifyContent.center'], value: 'center' },
          { title: fields['justifyContent.end'], value: 'end' },
          { title: fields['justifyContent.between'], value: 'between' },
          { title: fields['justifyContent.around'], value: 'around' },
          { title: fields['justifyContent.evenly'], value: 'evenly' }
        ]
      },
      initialValue: 'start'
    })
  ]
});

export default gridConfig;
