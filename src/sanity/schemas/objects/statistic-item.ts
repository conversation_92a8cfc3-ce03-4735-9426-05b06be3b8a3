import {defineField, defineType} from 'sanity'
import {Hash, TrendingUp} from 'lucide-react'

export const statisticItemObject = defineType({
  name: 'statisticItem',
  title: 'Statistic Item',
  type: 'object',
  icon: Hash,
  fields: [
    defineField({
      name: 'label',
      title: 'Label',
      type: 'string',
      validation: Rule => Rule.required(),
      description: 'The label that describes this statistic'
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 2,
      description: 'Optional additional description or context'
    }),
    defineField({
      name: 'dataSource',
      title: 'Data Source',
      type: 'dataSourceConfig',
      validation: Rule => Rule.required(),
      description: 'Configure where this statistic gets its data'
    }),
    defineField({
      name: 'numberFormatting',
      title: 'Number Formatting',
      type: 'object',
      fields: [
        defineField({
          name: 'prefix',
          title: 'Prefix',
          type: 'string',
          description: 'Text to show before the number (e.g., "$", "+", "#")'
        }),
        defineField({
          name: 'suffix',
          title: 'Suffix',
          type: 'string',
          description: 'Text to show after the number (e.g., "%", "+", "K")'
        }),
        defineField({
          name: 'decimalPlaces',
          title: 'Decimal Places',
          type: 'number',
          validation: Rule => Rule.min(0).max(4),
          initialValue: 0,
          description: 'Number of decimal places to show'
        }),
        defineField({
          name: 'useThousandsSeparator',
          title: 'Use Thousands Separator',
          type: 'boolean',
          initialValue: true,
          description: 'Show commas for thousands (1,000 vs 1000)'
        }),
        defineField({
          name: 'abbreviateLargeNumbers',
          title: 'Abbreviate Large Numbers',
          type: 'boolean',
          initialValue: false,
          description: 'Show 1K, 1M, 1B instead of full numbers'
        })
      ]
    }),
    defineField({
      name: 'animation',
      title: 'Animation Configuration',
      type: 'animationConfig',
      description: 'Configure how this statistic animates'
    }),
    defineField({
      name: 'icon',
      title: 'Icon',
      type: 'object',
      fields: [
        defineField({
          name: 'iconType',
          title: 'Icon Type',
          type: 'string',
          options: {
            list: [
              { title: 'None', value: 'none' },
              { title: 'Lucide Icon', value: 'lucide' },
              { title: 'Custom Image', value: 'image' },
              { title: 'Emoji', value: 'emoji' }
            ]
          },
          initialValue: 'none'
        }),
        defineField({
          name: 'lucideIcon',
          title: 'Lucide Icon Name',
          type: 'string',
          hidden: ({ parent }) => parent?.iconType !== 'lucide',
          description: 'Icon name from Lucide React (e.g., Users, Star, TrendingUp)'
        }),
        defineField({
          name: 'customImage',
          title: 'Custom Icon Image',
          type: 'image',
          hidden: ({ parent }) => parent?.iconType !== 'image',
          options: {
            hotspot: true
          }
        }),
        defineField({
          name: 'emoji',
          title: 'Emoji',
          type: 'string',
          hidden: ({ parent }) => parent?.iconType !== 'emoji',
          validation: Rule => Rule.max(2),
          description: 'Single emoji character'
        }),
        defineField({
          name: 'position',
          title: 'Icon Position',
          type: 'string',
          options: {
            list: [
              { title: 'Above', value: 'above' },
              { title: 'Left', value: 'left' },
              { title: 'Right', value: 'right' }
            ]
          },
          hidden: ({ parent }) => parent?.iconType === 'none',
          initialValue: 'above'
        })
      ]
    }),
    defineField({
      name: 'styling',
      title: 'Custom Styling',
      type: 'object',
      fields: [
        defineField({
          name: 'numberColor',
          title: 'Number Color',
          type: 'simplerColor',
          description: 'Custom color for the number'
        }),
        defineField({
          name: 'labelColor',
          title: 'Label Color',
          type: 'simplerColor',
          description: 'Custom color for the label'
        }),
        defineField({
          name: 'numberSize',
          title: 'Number Size',
          type: 'string',
          options: {
            list: [
              { title: 'Small', value: 'text-2xl' },
              { title: 'Medium', value: 'text-3xl' },
              { title: 'Large', value: 'text-4xl' },
              { title: 'Extra Large', value: 'text-5xl' },
              { title: 'Huge', value: 'text-6xl' }
            ]
          },
          initialValue: 'text-4xl'
        }),
        defineField({
          name: 'fontWeight',
          title: 'Font Weight',
          type: 'string',
          options: {
            list: [
              { title: 'Normal', value: 'font-normal' },
              { title: 'Medium', value: 'font-medium' },
              { title: 'Semibold', value: 'font-semibold' },
              { title: 'Bold', value: 'font-bold' },
              { title: 'Extra Bold', value: 'font-extrabold' }
            ]
          },
          initialValue: 'font-bold'
        })
      ]
    })
  ],
  preview: {
    select: {
      label: 'label',
      sourceType: 'dataSource.sourceType',
      staticValue: 'dataSource.staticValue',
      prefix: 'numberFormatting.prefix',
      suffix: 'numberFormatting.suffix',
      animationType: 'animation.animationType'
    },
    prepare({ label, sourceType, staticValue, prefix, suffix, animationType }) {
      let subtitle = sourceType
      if (sourceType === 'static' && staticValue !== undefined) {
        const formatted = `${prefix || ''}${staticValue}${suffix || ''}`
        subtitle = `Static: ${formatted}`
      }
      if (animationType) {
        subtitle += ` • ${animationType}`
      }
      
      return {
        title: label || 'Untitled Statistic',
        subtitle,
        media: TrendingUp
      }
    }
  }
})
