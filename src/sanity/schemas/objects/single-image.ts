import {Image} from "lucide-react";
import {defineField, defineType} from "sanity";
import {RatioInput, ratioOptions} from "@/sanity/components/ratio-input";
import {singleImageObjectDict} from '../../dictionary/studio/schemas/objects/single-image';

const { fields } = singleImageObjectDict;

export default defineType({
  name: 'singleImageObject',
  title: fields.title,
  type: 'object',
  fields: [
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string'
        }),
        defineField({
          title: fields.aspectRatio,
          name: "aspectRatio",
          type: "string",
          options: {
            list: ratioOptions.map(({ title, value }) => ({ title, value })),
            layout: 'radio',
          },
          components: { input: RatioInput },
          initialValue: 'square',
        }),
      ],
    }),
  ],
  preview: {
    select: {
      title: 'image',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title?.altText ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Image,
      }
    },
  },
})