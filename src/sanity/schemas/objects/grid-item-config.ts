import {defineField, defineType} from "sanity";
import {gridItemConfigObjectDict} from '../../dictionary/studio/schemas/objects/grid-item-config';

const { fields } = gridItemConfigObjectDict;

export const gridItemConfig = defineType({
  name: 'gridItemConfig',
  title: fields.columnSpan,
  type: 'object',
  description: 'Custom grid positioning and styling for this block',
  fields: [
    // Column spanning
    defineField({
      name: 'columnSpan',
      title: fields.columnSpan,
      type: 'object',
      description: 'How many columns this item should span',
      fields: [
        { name: 'mobile', title: fields['columnSpan.mobile'], type: 'number', validation: Rule => Rule.min(1).max(2), initialValue: 1 },
        { name: 'tablet', title: fields['columnSpan.tablet'], type: 'number', validation: Rule => Rule.min(1).max(4), initialValue: 1 },
        { name: 'desktop', title: fields['columnSpan.desktop'], type: 'number', validation: Rule => Rule.min(1).max(12), initialValue: 1 }
      ]
    }),
    // Row spanning
    defineField({
      name: 'rowSpan',
      title: fields.rowSpan,
      type: 'number',
      description: 'How many rows this item should span',
      validation: Rule => Rule.min(1).max(4),
      initialValue: 1
    }),
    // Grid positioning
    defineField({
      name: 'startColumn',
      title: fields.startColumn,
      type: 'object',
      description: 'Specific column to start at (optional)',
      fields: [
        { name: 'mobile', title: fields['startColumn.mobile'], type: 'number' },
        { name: 'tablet', title: fields['startColumn.tablet'], type: 'number' },
        { name: 'desktop', title: fields['startColumn.desktop'], type: 'number' }
      ]
    }),
    defineField({
      name: 'startRow',
      title: fields.startRow,
      type: 'object',
      description: 'Specific row to start at (optional)',
      fields: [
        { name: 'mobile', title: fields['startRow.mobile'], type: 'number' },
        { name: 'tablet', title: fields['startRow.tablet'], type: 'number' },
        { name: 'desktop', title: fields['startRow.desktop'], type: 'number' }
      ]
    }),
    // Order control
    defineField({
      name: 'order',
      title: fields.order,
      type: 'object',
      description: 'Control the visual order of this item',
      fields: [
        { name: 'mobile', title: fields['order.mobile'], type: 'number' },
        { name: 'tablet', title: fields['order.tablet'], type: 'number' },
        { name: 'desktop', title: fields['order.desktop'], type: 'number' }
      ]
    }),
    // Item-specific alignment
    defineField({
      name: 'alignment',
      title: fields.alignment,
      type: 'string',
      description: 'Override section alignment for this specific item',
      options: {
        list: [
          { title: fields['alignment.inherit'], value: 'inherit' },
          { title: fields['alignment.start'], value: 'start' },
          { title: fields['alignment.center'], value: 'center' },
          { title: fields['alignment.end'], value: 'end' },
          { title: fields['alignment.stretch'], value: 'stretch' }
        ]
      },
      initialValue: 'inherit'
    }),
    // Custom styling for this grid item
    defineField({
      name: 'customStyling',
      title: fields.customStyling,
      type: 'object',
      description: 'Additional styling for this grid item',
      fields: [
        defineField({
          name: 'backgroundColor',
          title: fields['customStyling.backgroundColor'],
          type: 'simplerColor'
        }),
        defineField({
          name: 'textColor',
          title: fields['customStyling.textColor'],
          type: 'simplerColor'
        }),
        defineField({
          name: 'padding',
          title: fields['customStyling.padding'],
          type: 'string',
          options: {
            list: [
              { title: fields['customStyling.padding.none'], value: 'none' },
              { title: fields['customStyling.padding.small'], value: 'small' },
              { title: fields['customStyling.padding.medium'], value: 'medium' },
              { title: fields['customStyling.padding.large'], value: 'large' },
              { title: fields['customStyling.padding.xlarge'], value: 'xlarge' }
            ]
          }
        }),
        defineField({
          name: 'borderRadius',
          title: fields['customStyling.borderRadius'],
          type: 'string',
          options: {
            list: [
              { title: fields['customStyling.borderRadius.none'], value: 'none' },
              { title: fields['customStyling.borderRadius.sm'], value: 'sm' },
              { title: fields['customStyling.borderRadius.md'], value: 'md' },
              { title: fields['customStyling.borderRadius.lg'], value: 'lg' },
              { title: fields['customStyling.borderRadius.full'], value: 'full' }
            ]
          }
        }),
        defineField({
          name: 'shadowSm',
          title: fields['customStyling.shadowSm'],
          type: 'string',
          options: {
            list: [
              { title: fields['customStyling.shadow.none'], value: 'none' },
              { title: fields['customStyling.shadow.sm'], value: 'sm' },
              { title: fields['customStyling.shadow.md'], value: 'md' },
              { title: fields['customStyling.shadow.lg'], value: 'lg' },
              { title: fields['customStyling.shadow.xl'], value: 'xl' }
            ]
          }
        })
      ]
    })
  ]
});

export default gridItemConfig;
