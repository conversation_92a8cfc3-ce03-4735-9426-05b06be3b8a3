import {defineField, defineType} from 'sanity'
import {Layout, Settings} from 'lucide-react'
import {timelineConfigObjectDict} from '../../dictionary/studio/schemas/objects/timeline-config';

const { fields } = timelineConfigObjectDict;

export const timelineConfigObject = defineType({
  name: 'timelineConfig',
  title: fields.title,
  type: 'object',
  icon: Settings,
  fields: [
    defineField({
      name: 'layout',
      title: fields.layout,
      type: 'string',
      options: {
        list: [
          { title: fields.layout_vertical, value: 'vertical' },
          { title: fields.layout_horizontal, value: 'horizontal' },
          { title: fields.layout_alternating, value: 'alternating' },
          { title: fields.layout_stepped, value: 'stepped' }
        ]
      },
      initialValue: 'vertical',
      description: fields.layout_description
    }),
    defineField({
      name: 'visualStyle',
      title: fields.visualStyle,
      type: 'object',
      fields: [
        defineField({
          name: 'dotStyle',
          title: fields.visualStyle_dotStyle,
          type: 'string',
          options: {
            list: [
              { title: fields.visualStyle_dotStyle_circle, value: 'circle' },
              { title: fields.visualStyle_dotStyle_square, value: 'square' },
              { title: fields.visualStyle_dotStyle_diamond, value: 'diamond' }
            ]
          },
          initialValue: 'circle'
        }),
        defineField({
          name: 'lineStyle',
          title: fields.visualStyle_lineStyle,
          type: 'string',
          options: {
            list: [
              { title: fields.visualStyle_lineStyle_solid, value: 'solid' },
              { title: fields.visualStyle_lineStyle_dashed, value: 'dashed' },
              { title: fields.visualStyle_lineStyle_dotted, value: 'dotted' },
              { title: fields.visualStyle_lineStyle_gradient, value: 'gradient' }
            ]
          },
          initialValue: 'solid'
        }),
        defineField({
          name: 'cardStyle',
          title: fields.visualStyle_cardStyle,
          type: 'string',
          options: {
            list: [
              { title: fields.visualStyle_cardStyle_clean, value: 'clean' },
              { title: fields.visualStyle_cardStyle_bordered, value: 'bordered' },
              { title: fields.visualStyle_cardStyle_shadowed, value: 'shadowed' },
              { title: fields.visualStyle_cardStyle_glass, value: 'glass' }
            ]
          },
          initialValue: 'clean'
        }),
        defineField({
          name: 'colorScheme',
          title: fields.visualStyle_colorScheme,
          type: 'string',
          options: {
            list: [
              { title: fields.visualStyle_colorScheme_default, value: 'default' },
              { title: fields.visualStyle_colorScheme_monochrome, value: 'monochrome' },
              { title: fields.visualStyle_colorScheme_colorful, value: 'colorful' },
              { title: fields.visualStyle_colorScheme_brand, value: 'brand' }
            ]
          },
          initialValue: 'default'
        })
      ]
    }),
    defineField({
      name: 'animations',
      title: fields.animations,
      type: 'object',
      fields: [
        defineField({
          name: 'enableAnimations',
          title: fields.animations_enableAnimations,
          type: 'boolean',
          initialValue: true
        }),
        defineField({
          name: 'animationType',
          title: fields.animations_animationType,
          type: 'string',
          options: {
            list: [
              { title: fields.animations_animationType_fadeIn, value: 'fadeIn' },
              { title: fields.animations_animationType_slideIn, value: 'slideIn' },
              { title: fields.animations_animationType_scaleIn, value: 'scaleIn' },
              { title: fields.animations_animationType_progressive, value: 'progressive' }
            ]
          },
          hidden: ({ parent }) => !parent?.enableAnimations,
          initialValue: 'fadeIn'
        }),
        defineField({
          name: 'staggerDelay',
          title: fields.animations_staggerDelay,
          type: 'number',
          validation: Rule => Rule.min(0).max(1000),
          initialValue: 200,
          hidden: ({ parent }) => !parent?.enableAnimations,
          description: fields.animations_staggerDelay_description
        }),
        defineField({
          name: 'enableScrollProgress',
          title: fields.animations_enableScrollProgress,
          type: 'boolean',
          initialValue: true,
          hidden: ({ parent }) => !parent?.enableAnimations,
          description: fields.animations_enableScrollProgress_description
        })
      ]
    }),
    defineField({
      name: 'responsiveSettings',
      title: fields.responsiveSettings,
      type: 'object',
      fields: [
        defineField({
          name: 'mobileLayout',
          title: fields.responsiveSettings_mobileLayout,
          type: 'string',
          options: {
            list: [
              { title: fields.responsiveSettings_mobileLayout_same, value: 'same' },
              { title: fields.responsiveSettings_mobileLayout_vertical, value: 'vertical' },
              { title: fields.responsiveSettings_mobileLayout_compact, value: 'compact' }
            ]
          },
          initialValue: 'same'
        }),
        defineField({
          name: 'hideElementsOnMobile',
          title: fields.responsiveSettings_hideElementsOnMobile,
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            list: [
              { title: fields.responsiveSettings_hideElementsOnMobile_icons, value: 'icons' },
              { title: fields.responsiveSettings_hideElementsOnMobile_dates, value: 'dates' },
              { title: fields.responsiveSettings_hideElementsOnMobile_assignees, value: 'assignees' },
              { title: fields.responsiveSettings_hideElementsOnMobile_progress, value: 'progress' }
            ]
          }
        })
      ]
    }),
    defineField({
      name: 'interactivity',
      title: fields.interactivity,
      type: 'object',
      fields: [
        defineField({
          name: 'expandableContent',
          title: fields.interactivity_expandableContent,
          type: 'boolean',
          initialValue: false,
          description: fields.interactivity_expandableContent_description
        }),
        defineField({
          name: 'clickableSteps',
          title: fields.interactivity_clickableSteps,
          type: 'boolean',
          initialValue: false,
          description: fields.interactivity_clickableSteps_description
        }),
        defineField({
          name: 'showProgress',
          title: fields.interactivity_showProgress,
          type: 'boolean',
          initialValue: true,
          description: fields.interactivity_showProgress_description
        })
      ]
    })
  ],
  preview: {
    select: {
      layout: 'layout',
      dotStyle: 'visualStyle.dotStyle',
      animationsEnabled: 'animations.enableAnimations',
      scrollProgress: 'animations.enableScrollProgress'
    },
    prepare({ layout, dotStyle, animationsEnabled, scrollProgress }) {
      const features = []
      if (animationsEnabled) features.push(fields.animations_enableAnimations)
      if (scrollProgress) features.push(fields.animations_enableScrollProgress)
      return {
        title: fields.title,
        subtitle: `${fields[`layout_${layout}` as keyof typeof fields]} • ${fields[`visualStyle_dotStyle_${dotStyle}` as keyof typeof fields]}${features.length ? ' • ' + features.join(' • ') : ''}`,
        media: Layout
      }
    }
  }
})
