import {defineField, defineType} from 'sanity'
import {Zap} from 'lucide-react'
import {animationConfigObjectDict} from '../../dictionary/studio/schemas/objects/animation-config'

const { fields } = animationConfigObjectDict

export const animationConfigObject = defineType({
  name: 'animationConfig',
  title: fields.title,
  type: 'object',
  icon: Zap,
  fields: [
    defineField({
      name: 'animationType',
      title: fields.animationType,
      type: 'string',
      options: {
        list: [
          { title: fields.animationType_countUp, value: 'countUp' },
          { title: fields.animationType_spring, value: 'spring' },
          { title: fields.animationType_bounce, value: 'bounce' },
          { title: fields.animationType_typewriter, value: 'typewriter' },
          { title: fields.animationType_fadeIn, value: 'fadeIn' },
          { title: fields.animationType_scaleUp, value: 'scaleUp' }
        ]
      },
      initialValue: 'countUp'
    }),
    defineField({
      name: 'duration',
      title: fields.duration,
      type: 'number',
      validation: Rule => Rule.min(0.1).max(10),
      initialValue: 2,
      description: fields.durationDesc
    }),
    defineField({
      name: 'delay',
      title: fields.delay,
      type: 'number',
      validation: Rule => Rule.min(0).max(5),
      initialValue: 0,
      description: fields.delayDesc
    }),
    defineField({
      name: 'trigger',
      title: fields.trigger,
      type: 'string',
      options: {
        list: [
          { title: fields.trigger_inView, value: 'inView' },
          { title: fields.trigger_load, value: 'load' },
          { title: fields.trigger_hover, value: 'hover' },
          { title: fields.trigger_manual, value: 'manual' }
        ]
      },
      initialValue: 'inView'
    }),
    defineField({
      name: 'easing',
      title: fields.easing,
      type: 'string',
      options: {
        list: [
          { title: fields.easing_ease, value: 'ease' },
          { title: fields.easing_easeIn, value: 'easeIn' },
          { title: fields.easing_easeOut, value: 'easeOut' },
          { title: fields.easing_easeInOut, value: 'easeInOut' },
          { title: fields.easing_linear, value: 'linear' }
        ]
      },
      initialValue: 'easeOut'
    }),
    defineField({
      name: 'springConfig',
      title: fields.springConfig,
      type: 'object',
      hidden: ({ parent }) => !['spring', 'bounce'].includes(parent?.animationType),
      fields: [
        defineField({
          name: 'mass',
          title: fields.mass,
          type: 'number',
          validation: Rule => Rule.min(0.1).max(5),
          initialValue: 0.8,
          description: fields.massDesc
        }),
        defineField({
          name: 'stiffness',
          title: fields.stiffness,
          type: 'number',
          validation: Rule => Rule.min(1).max(200),
          initialValue: 75,
          description: fields.stiffnessDesc
        }),
        defineField({
          name: 'damping',
          title: fields.damping,
          type: 'number',
          validation: Rule => Rule.min(1).max(50),
          initialValue: 15,
          description: fields.dampingDesc
        })
      ]
    }),
    defineField({
      name: 'viewportConfig',
      title: fields.viewportConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.trigger !== 'inView',
      fields: [
        defineField({
          name: 'threshold',
          title: fields.threshold,
          type: 'number',
          validation: Rule => Rule.min(0).max(100),
          initialValue: 20,
          description: fields.thresholdDesc
        }),
        defineField({
          name: 'once',
          title: fields.once,
          type: 'boolean',
          initialValue: true,
          description: fields.onceDesc
        })
      ]
    })
  ],
  preview: {
    select: {
      animationType: 'animationType',
      duration: 'duration',
      trigger: 'trigger'
    },
    prepare({ animationType, duration, trigger }) {
      return {
        title: fields.title,
        subtitle: `${animationType} • ${duration}s • ${trigger}`,
        media: Zap
      }
    }
  }
})
