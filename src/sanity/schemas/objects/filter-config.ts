import {defineField, defineType} from 'sanity';
import {Filter} from 'lucide-react';
import {filterConfigObjectDict} from '../../dictionary/studio/schemas/objects/filter-config';

const { fields, descriptions } = filterConfigObjectDict;

export const filterConfigObject = defineType({
  name: 'filterConfig',
  title: fields.previewTitle,
  type: 'object',
  icon: Filter,
  fields: [
    defineField({
      name: 'enableSearch',
      title: fields.enableSearch,
      type: 'boolean',
      initialValue: true,
      description: descriptions.enableSearch
    }),
    defineField({
      name: 'searchPlaceholder',
      title: fields.searchPlaceholder,
      type: 'string',
      hidden: ({ parent }) => !parent?.enableSearch,
      initialValue: 'Search content...',
      description: descriptions.searchPlaceholder
    }),
    defineField({
      name: 'enableCategoryFilter',
      title: fields.enableCategoryFilter,
      type: 'boolean',
      initialValue: true,
      description: descriptions.enableCategoryFilter
    }),
    defineField({
      name: 'enableTagFilter',
      title: fields.enableTagFilter,
      type: 'boolean',
      initialValue: true,
      description: descriptions.enableTagFilter
    }),
    defineField({
      name: 'enableStatusFilter',
      title: fields.enableStatusFilter,
      type: 'boolean',
      initialValue: false,
      description: descriptions.enableStatusFilter
    }),
    defineField({
      name: 'enableFeaturedFilter',
      title: fields.enableFeaturedFilter,
      type: 'boolean',
      initialValue: true,
      description: descriptions.enableFeaturedFilter
    }),
    defineField({
      name: 'defaultSort',
      title: fields.defaultSort,
      type: 'string',
      options: {
        list: [
          { title: fields['defaultSort.date-desc'], value: 'date-desc' },
          { title: fields['defaultSort.date-asc'], value: 'date-asc' },
          { title: fields['defaultSort.title-asc'], value: 'title-asc' },
          { title: fields['defaultSort.title-desc'], value: 'title-desc' },
          { title: fields['defaultSort.priority-desc'], value: 'priority-desc' },
          { title: fields['defaultSort.featured-first'], value: 'featured-first' }
        ]
      },
      initialValue: 'date-desc',
      description: descriptions.defaultSort
    }),
    defineField({
      name: 'showItemCount',
      title: fields.showItemCount,
      type: 'boolean',
      initialValue: true,
      description: descriptions.showItemCount
    }),
    defineField({
      name: 'enableUrlFilters',
      title: fields.enableUrlFilters,
      type: 'boolean',
      initialValue: false,
      description: descriptions.enableUrlFilters
    })
  ],
  preview: {
    select: {
      enableSearch: 'enableSearch',
      enableCategoryFilter: 'enableCategoryFilter',
      enableTagFilter: 'enableTagFilter',
      defaultSort: 'defaultSort'
    } as Record<string, string>,
    prepare({
      enableSearch,
      enableCategoryFilter,
      enableTagFilter,
      defaultSort
    }: Record<string, string | boolean>) {
      const features = [];
      if (enableSearch) features.push(fields.previewSearch);
      if (enableCategoryFilter) features.push(fields.previewCategories);
      if (enableTagFilter) features.push(fields.previewTags);
      const sortKey = `defaultSort.${defaultSort}` as keyof typeof fields;
      return {
        title: fields.previewTitle,
        subtitle: `${features.join(' • ')} • ${fields.previewSort}: ${fields[sortKey] ?? defaultSort}`,
        media: Filter
      };
    }
  }
});
