import { defineField, defineType } from "sanity";
import { contactObjectDict } from '../../dictionary/studio/schemas/objects/contact';

const { fields, validation, descriptions } = contactObjectDict;

export default defineType({
  name: "contactObject",
  title: fields.title,
  type: "object",
  fields: [
    defineField({
      name: "phone",
      title: fields.phone,
      type: "string",
      description: descriptions.phone,
      validation: rule => rule.required().error(validation.phoneRequired),
    }),
    defineField({
      name: "email",
      title: fields.email,
      type: "string",
      description: descriptions.email,
      validation: rule => rule.required().email().error(validation.emailValid),
    }),
    defineField({
      name: "website",
      title: fields.website,
      type: "url",
      description: descriptions.website,
      validation: rule => rule.uri({ allowRelative: false }).error(validation.websiteValid),
    }),
    defineField({
      name: "whatsapp",
      title: fields.whatsapp,
      type: "string",
      description: descriptions.whatsapp,
    }),
  ],
});
