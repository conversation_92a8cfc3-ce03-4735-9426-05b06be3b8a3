import {defineField, defineType} from 'sanity'
import {Database, Hash} from 'lucide-react'
import {dataSourceObjectDict} from '../../dictionary/studio/schemas/objects/data-source';

const { fields } = dataSourceObjectDict;

export const dataSourceObject = defineType({
  name: 'dataSource',
  title: fields.title,
  type: 'object',
  icon: Database,
  description: fields.description,
  fields: [
    defineField({
      name: 'sourceType',
      title: fields.sourceType,
      type: 'string',
      options: {
        list: [
          { title: fields.sourceType_static, value: 'static' },
          { title: fields.sourceType_documents, value: 'documents' },
          { title: fields.sourceType_api, value: 'api' },
          { title: fields.sourceType_groq, value: 'groq' }
        ]
      },
      initialValue: 'static',
      description: fields.sourceType_description
    }),
    defineField({
      name: 'outputFormat',
      title: fields.outputFormat,
      type: 'string',
      options: {
        list: [
          { title: fields.outputFormat_number, value: 'number' },
          { title: fields.outputFormat_documents, value: 'documents' },
          { title: fields.outputFormat_text, value: 'text' },
          { title: fields.outputFormat_raw, value: 'raw' }
        ]
      },
      initialValue: 'number',
      description: fields.outputFormat_description
    }),
    // Static value configuration
    defineField({
      name: 'staticConfig',
      title: fields.staticConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'static',
      fields: [
        defineField({
          name: 'value',
          title: fields.staticConfig_value,
          type: 'string',
          description: fields.staticConfig_value_description
        })
      ]
    }),
    // Document query configuration
    defineField({
      name: 'documentConfig',
      title: fields.documentConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'documents',
      fields: [
        defineField({
          name: 'documentType',
          title: fields.documentConfig_documentType,
          type: 'string',
          options: {
            list: [
              { title: fields.documentConfig_documentType_post, value: 'post' },
              { title: fields.documentConfig_documentType_project, value: 'project' },
              { title: fields.documentConfig_documentType_service, value: 'service' },
              { title: fields.documentConfig_documentType_testimonial, value: 'testimonial' },
              { title: fields.documentConfig_documentType_author, value: 'author' },
              { title: fields.documentConfig_documentType_page, value: 'page' },
              { title: fields.documentConfig_documentType_form, value: 'form' },
              { title: fields.documentConfig_documentType_postCategory, value: 'postCategory' },
              { title: fields.documentConfig_documentType_projectCategory, value: 'projectCategory' },
              { title: fields.documentConfig_documentType_custom, value: 'custom' }
            ]
          },
          initialValue: 'post'
        }),
        defineField({
          name: 'customDocumentType',
          title: fields.documentConfig_customDocumentType,
          type: 'string',
          hidden: ({ parent }) => parent?.documentType !== 'custom',
          description: fields.documentConfig_customDocumentType_description
        }),
        defineField({
          name: 'filters',
          title: fields.documentConfig_filters,
          type: 'array',
          description: fields.documentConfig_filters_description,
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'field',
                  title: fields.documentConfig_filters_field,
                  type: 'string',
                  placeholder: fields.documentConfig_filters_field_placeholder
                }),
                defineField({
                  name: 'operator',
                  title: fields.documentConfig_filters_operator,
                  type: 'string',
                  options: {
                    list: [
                      { title: fields.documentConfig_filters_operator_eq, value: '==' },
                      { title: fields.documentConfig_filters_operator_neq, value: '!=' },
                      { title: fields.documentConfig_filters_operator_gt, value: '>' },
                      { title: fields.documentConfig_filters_operator_lt, value: '<' },
                      { title: fields.documentConfig_filters_operator_gte, value: '>=' },
                      { title: fields.documentConfig_filters_operator_lte, value: '<=' },
                      { title: fields.documentConfig_filters_operator_match, value: 'match' },
                      { title: fields.documentConfig_filters_operator_in, value: 'in' },
                      { title: fields.documentConfig_filters_operator_references, value: 'references' }
                    ]
                  },
                  initialValue: '=='
                }),
                defineField({
                  name: 'value',
                  title: fields.documentConfig_filters_value,
                  type: 'string',
                  description: fields.documentConfig_filters_value_description
                })
              ]
            }
          ]
        }),
        defineField({
          name: 'sortBy',
          title: fields.documentConfig_sortBy,
          type: 'object',
          fields: [
            defineField({
              name: 'field',
              title: fields.documentConfig_sortBy_field,
              type: 'string',
              placeholder: fields.documentConfig_sortBy_field_placeholder,
              description: fields.documentConfig_sortBy_field_description
            }),
            defineField({
              name: 'order',
              title: fields.documentConfig_sortBy_order,
              type: 'string',
              options: {
                list: [
                  { title: fields.documentConfig_sortBy_order_asc, value: 'asc' },
                  { title: fields.documentConfig_sortBy_order_desc, value: 'desc' }
                ]
              },
              initialValue: 'desc'
            })
          ]
        }),
        defineField({
          name: 'limit',
          title: fields.documentConfig_limit,
          type: 'number',
          validation: Rule => Rule.min(1).max(100),
          description: fields.documentConfig_limit_description
        }),
        defineField({
          name: 'projection',
          title: fields.documentConfig_projection,
          type: 'text',
          rows: 3,
          placeholder: fields.documentConfig_projection_placeholder,
          description: fields.documentConfig_projection_description
        })
      ]
    }),
    // API configuration
    defineField({
      name: 'apiConfig',
      title: fields.apiConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'api',
      fields: [
        defineField({
          name: 'endpoint',
          title: fields.apiConfig_endpoint,
          type: 'url',
          description: fields.apiConfig_endpoint_description
        }),
        defineField({
          name: 'method',
          title: fields.apiConfig_method,
          type: 'string',
          options: {
            list: [
              { title: fields.apiConfig_method_get, value: 'GET' },
              { title: fields.apiConfig_method_post, value: 'POST' }
            ]
          },
          initialValue: 'GET'
        }),
        defineField({
          name: 'headers',
          title: fields.apiConfig_headers,
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'key',
                  title: fields.apiConfig_headers_key,
                  type: 'string'
                }),
                defineField({
                  name: 'value',
                  title: fields.apiConfig_headers_value,
                  type: 'string'
                })
              ]
            }
          ]
        }),
        defineField({
          name: 'requestBody',
          title: fields.apiConfig_requestBody,
          type: 'text',
          rows: 4,
          hidden: ({ parent }) => parent?.method !== 'POST',
          description: fields.apiConfig_requestBody_description
        }),
        defineField({
          name: 'responseTransform',
          title: fields.apiConfig_responseTransform,
          type: 'object',
          fields: [
            defineField({
              name: 'dataPath',
              title: fields.apiConfig_responseTransform_dataPath,
              type: 'string',
              placeholder: fields.apiConfig_responseTransform_dataPath_placeholder,
              description: fields.apiConfig_responseTransform_dataPath_description
            }),
            defineField({
              name: 'transform',
              title: fields.apiConfig_responseTransform_transform,
              type: 'text',
              rows: 3,
              placeholder: fields.apiConfig_responseTransform_transform_placeholder,
              description: fields.apiConfig_responseTransform_transform_description
            })
          ]
        }),
        defineField({
          name: 'cacheConfig',
          title: fields.apiConfig_cacheConfig,
          type: 'object',
          fields: [
            defineField({
              name: 'enabled',
              title: fields.apiConfig_cacheConfig_enabled,
              type: 'boolean',
              initialValue: true
            }),
            defineField({
              name: 'duration',
              title: fields.apiConfig_cacheConfig_duration,
              type: 'number',
              initialValue: 15,
              validation: Rule => Rule.min(1).max(1440),
              hidden: ({ parent }) => !parent?.enabled,
              description: fields.apiConfig_cacheConfig_duration_description
            })
          ]
        })
      ]
    }),
    // Custom GROQ configuration
    defineField({
      name: 'groqConfig',
      title: fields.groqConfig,
      type: 'object',
      hidden: ({ parent }) => parent?.sourceType !== 'groq',
      fields: [
        defineField({
          name: 'query',
          title: fields.groqConfig_query,
          type: 'text',
          rows: 6,
          placeholder: fields.groqConfig_query_placeholder,
          description: fields.groqConfig_query_description
        }),
        defineField({
          name: 'params',
          title: fields.groqConfig_params,
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                defineField({
                  name: 'key',
                  title: fields.groqConfig_params_key,
                  type: 'string',
                  placeholder: fields.groqConfig_params_key_placeholder
                }),
                defineField({
                  name: 'value',
                  title: fields.groqConfig_params_value,
                  type: 'string',
                  placeholder: fields.groqConfig_params_value_placeholder
                }),
                defineField({
                  name: 'type',
                  title: fields.groqConfig_params_type,
                  type: 'string',
                  options: {
                    list: [
                      { title: fields.groqConfig_params_type_string, value: 'string' },
                      { title: fields.groqConfig_params_type_number, value: 'number' },
                      { title: fields.groqConfig_params_type_boolean, value: 'boolean' },
                      { title: fields.groqConfig_params_type_reference, value: 'reference' }
                    ]
                  },
                  initialValue: 'string'
                })
              ]
            }
          ]
        })
      ]
    }),
    // Error handling and fallbacks
    defineField({
      name: 'errorHandling',
      title: fields.errorHandling,
      type: 'object',
      fields: [
        defineField({
          name: 'fallbackValue',
          title: fields.errorHandling_fallbackValue,
          type: 'string',
          description: fields.errorHandling_fallbackValue_description
        }),
        defineField({
          name: 'retryAttempts',
          title: fields.errorHandling_retryAttempts,
          type: 'number',
          validation: Rule => Rule.min(0).max(5),
          initialValue: 2,
          description: fields.errorHandling_retryAttempts_description
        }),
        defineField({
          name: 'timeout',
          title: fields.errorHandling_timeout,
          type: 'number',
          validation: Rule => Rule.min(1).max(60),
          initialValue: 10,
          description: fields.errorHandling_timeout_description
        })
      ]
    })
  ],
  preview: {
    select: {
      sourceType: 'sourceType',
      outputFormat: 'outputFormat',
      documentType: 'documentConfig.documentType',
      apiEndpoint: 'apiConfig.endpoint',
      staticValue: 'staticConfig.value'
    },
    prepare({ sourceType, outputFormat, documentType, apiEndpoint, staticValue }) {
      let subtitle = `${sourceType} → ${outputFormat}`
      if (sourceType === 'static' && staticValue) {
        subtitle = `Static: ${staticValue}`
      } else if (sourceType === 'documents' && documentType) {
        subtitle = `Query: ${documentType} → ${outputFormat}`
      } else if (sourceType === 'api' && apiEndpoint) {
        try {
          subtitle = `API: ${new URL(apiEndpoint).hostname} → ${outputFormat}`
        } catch {
          subtitle = `API → ${outputFormat}`
        }
      } else if (sourceType === 'groq') {
        subtitle = `GROQ → ${outputFormat}`
      }
      return {
        title: fields.title,
        subtitle,
        media: outputFormat === 'number' ? Hash : Database
      }
    }
  }
})
