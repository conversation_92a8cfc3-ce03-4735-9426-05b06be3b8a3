import {defineField, defineType} from 'sanity'
import {<PERSON>ertCircle, CheckCircle, Clock, Pause} from 'lucide-react'
import {timelineStepObjectDict} from '../../dictionary/studio/schemas/objects/timeline-step';

const { fields } = timelineStepObjectDict;

export const timelineStepObject = defineType({
  name: 'timelineStep',
  title: fields.title,
  type: 'object',
  icon: Clock,
  fields: [
    defineField({
      name: 'title',
      title: fields.stepTitle,
      type: 'string',
      validation: Rule => Rule.required().max(100)
    }),
    defineField({
      name: 'description',
      title: fields.stepDescription,
      type: 'text',
      rows: 3,
      description: fields.stepDescriptionDesc
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      description: fields.contentDesc,
      of: [
        { type: 'block' },
        { type: 'image' }
      ]
    }),
    defineField({
      name: 'status',
      title: fields.status,
      type: 'string',
      options: {
        list: [
          { title: fields.status_completed, value: 'completed' },
          { title: fields.status_inProgress, value: 'in-progress' },
          { title: fields.status_pending, value: 'pending' },
          { title: fields.status_blocked, value: 'blocked' }
        ]
      },
      initialValue: 'pending'
    }),
    defineField({
      name: 'isMilestone',
      title: fields.isMilestone,
      type: 'boolean',
      initialValue: false,
      description: fields.isMilestoneDesc
    }),
    defineField({
      name: 'icon',
      title: fields.icon,
      type: 'object',
      fields: [
        defineField({
          name: 'type',
          title: fields.iconType,
          type: 'string',
          options: {
            list: [
              { title: fields.iconType_number, value: 'number' },
              { title: fields.iconType_lucide, value: 'lucide' },
              { title: fields.iconType_image, value: 'image' },
              { title: fields.iconType_none, value: 'none' }
            ]
          },
          initialValue: 'number'
        }),
        defineField({
          name: 'number',
          title: fields.number,
          type: 'number',
          hidden: ({ parent }) => parent?.type !== 'number',
          description: fields.numberDesc
        }),
        defineField({
          name: 'lucideIcon',
          title: fields.lucideIcon,
          type: 'string',
          hidden: ({ parent }) => parent?.type !== 'lucide',
          description: fields.lucideIconDesc
        }),
        defineField({
          name: 'customImage',
          title: fields.customImage,
          type: 'image',
          hidden: ({ parent }) => parent?.type !== 'image',
          options: {
            hotspot: true
          }
        })
      ]
    }),
    defineField({
      name: 'timeline',
      title: fields.timeline,
      type: 'object',
      fields: [
        defineField({
          name: 'startDate',
          title: fields.startDate,
          type: 'datetime',
          description: fields.startDateDesc
        }),
        defineField({
          name: 'endDate',
          title: fields.endDate,
          type: 'datetime',
          description: fields.endDateDesc
        }),
        defineField({
          name: 'duration',
          title: fields.duration,
          type: 'string',
          description: fields.durationDesc
        }),
        defineField({
          name: 'assignee',
          title: fields.assignee,
          type: 'object',
          fields: [
            defineField({
              name: 'name',
              title: fields.name,
              type: 'string'
            }),
            defineField({
              name: 'role',
              title: fields.role,
              type: 'string'
            }),
            defineField({
              name: 'avatar',
              title: fields.avatar,
              type: 'image'
            })
          ]
        })
      ]
    }),
    defineField({
      name: 'customColor',
      title: fields.customColor,
      type: 'string',
      options: {
        list: [
          { title: fields.customColor_default, value: 'default' },
          { title: fields.customColor_blue, value: 'blue' },
          { title: fields.customColor_green, value: 'green' },
          { title: fields.customColor_orange, value: 'orange' },
          { title: fields.customColor_red, value: 'red' },
          { title: fields.customColor_purple, value: 'purple' }
        ]
      },
      initialValue: 'default'
    }),
    defineField({
      name: 'progress',
      title: fields.progress,
      type: 'number',
      validation: Rule => Rule.min(0).max(100),
      description: fields.progressDesc,
      hidden: ({ parent }) => parent?.status !== 'in-progress'
    })
  ],
  preview: {
    select: {
      title: 'title',
      status: 'status',
      isMilestone: 'isMilestone',
      duration: 'timeline.duration'
    },
    prepare({ title, status, isMilestone, duration }) {
      const statusIcons = {
        completed: CheckCircle,
        'in-progress': Clock,
        pending: Pause,
        blocked: AlertCircle
      }
      const icon = statusIcons[status as keyof typeof statusIcons] || Clock;
      return {
        title: title || fields.untitled,
        subtitle: `${status}${isMilestone ? ' • ' + fields.milestone : ''}${duration ? ` • ${duration}` : ''}`,
        media: icon
      }
    }
  }
})
