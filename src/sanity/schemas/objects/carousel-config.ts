import {defineField, defineType} from 'sanity'
import {Play} from 'lucide-react'
import {carouselConfigDict} from '../../dictionary/studio/schemas/objects/carousel-config';

const { fields } = carouselConfigDict;

export const carouselConfigObject = defineType({
  name: 'carouselConfig',
  title: fields.title,
  type: 'object',
  description: fields.description,
  fields: [
    // Basic Navigation Options
    defineField({
      name: 'showNavigation',
      title: fields.showNavigation,
      type: 'boolean',
      initialValue: true,
      description: fields.showNavigationDesc
    }),
    defineField({
      name: 'showDots',
      title: fields.showDots,
      type: 'boolean',
      initialValue: true,
      description: fields.showDotsDesc
    }),
    defineField({
      name: 'showThumbnails',
      title: fields.showThumbnails,
      type: 'boolean',
      initialValue: false,
      description: fields.showThumbnailsDesc
    }),
    // Embla Core Options
    defineField({
      name: 'emblaOptions',
      title: fields.carouselBehavior,
      type: 'object',
      fields: [
        defineField({
          name: 'loop',
          title: fields.loop,
          type: 'boolean',
          initialValue: false,
          description: fields.loopDesc
        }),
        defineField({
          name: 'align',
          title: fields.align,
          type: 'string',
          options: {
            list: [
              { title: fields.align_start, value: 'start' },
              { title: fields.align_center, value: 'center' },
              { title: fields.align_end, value: 'end' }
            ]
          },
          initialValue: 'center'
        }),
        defineField({
          name: 'slidesToScroll',
          title: fields.slidesToScroll,
          type: 'string',
          options: {
            list: [
              { title: fields.slidesToScroll_auto, value: 'auto' },
              { title: fields.slidesToScroll_1, value: '1' },
              { title: fields.slidesToScroll_2, value: '2' },
              { title: fields.slidesToScroll_3, value: '3' }
            ]
          },
          initialValue: '1'
        }),
        defineField({
          name: 'dragFree',
          title: fields.dragFree,
          type: 'boolean',
          initialValue: false,
          description: fields.dragFreeDesc
        })
      ]
    }),
    // Responsive Slides Display
    defineField({
      name: 'slidesDisplay',
      title: fields.slidesDisplay,
      type: 'object',
      fields: [
        defineField({
          name: 'slidesPerView',
          title: fields.slidesPerView,
          type: 'object',
          fields: [
            defineField({
              name: 'mobile',
              title: fields.mobile,
              type: 'number',
              validation: Rule => Rule.min(1).max(5),
              initialValue: 1
            }),
            defineField({
              name: 'tablet',
              title: fields.tablet,
              type: 'number',
              validation: Rule => Rule.min(1).max(5),
              initialValue: 2
            }),
            defineField({
              name: 'desktop',
              title: fields.desktop,
              type: 'number',
              validation: Rule => Rule.min(1).max(8),
              initialValue: 3
            })
          ]
        }),
        defineField({
          name: 'slideSpacing',
          title: fields.slideSpacing,
          type: 'string',
          options: {
            list: [
              { title: fields.slideSpacing_none, value: '0' },
              { title: fields.slideSpacing_small, value: '1rem' },
              { title: fields.slideSpacing_medium, value: '1.5rem' },
              { title: fields.slideSpacing_large, value: '2rem' }
            ]
          },
          initialValue: '1rem'
        })
      ]
    }),
    // Autoplay Configuration
    defineField({
      name: 'autoplay',
      title: fields.autoplay,
      type: 'object',
      fields: [
        defineField({
          name: 'enabled',
          title: fields.enableAutoplay,
          type: 'boolean',
          initialValue: false
        }),
        defineField({
          name: 'delay',
          title: fields.autoplayDelay,
          type: 'number',
          validation: Rule => Rule.min(1000).max(10000),
          initialValue: 4000,
          hidden: ({ parent }) => !parent?.enabled
        }),
        defineField({
          name: 'stopOnInteraction',
          title: fields.stopOnInteraction,
          type: 'boolean',
          initialValue: true,
          hidden: ({ parent }) => !parent?.enabled
        })
      ]
    }),
    // Transition Effects
    defineField({
      name: 'transitionEffect',
      title: fields.transitionEffect,
      type: 'object',
      fields: [
        defineField({
          name: 'type',
          title: fields.effectType,
          type: 'string',
          options: {
            list: [
              { title: fields.effectType_slide, value: 'slide' },
              { title: fields.effectType_fade, value: 'fade' }
            ]
          },
          initialValue: 'slide'
        }),
        defineField({
          name: 'duration',
          title: fields.transitionDuration,
          type: 'number',
          validation: Rule => Rule.min(100).max(2000),
          initialValue: 300
        })
      ]
    })
  ],
  preview: {
    select: {
      showNavigation: 'showNavigation',
      showDots: 'showDots',
      loop: 'emblaOptions.loop',
      autoplayEnabled: 'autoplay.enabled',
      effect: 'transitionEffect.type'
    },
    prepare({ showNavigation, showDots, loop, autoplayEnabled, effect }) {
      const features = []
      if (showNavigation) features.push(fields.preview_navigation)
      if (showDots) features.push(fields.preview_dots)
      if (loop) features.push(fields.preview_loop)
      if (autoplayEnabled) features.push(fields.preview_autoplay)
      if (effect === 'fade') features.push(fields.preview_fade)
      return {
        title: fields.title,
        subtitle: features.length ? features.join(' • ') : fields.preview_basic,
        media: Play
      }
    }
  }
})
