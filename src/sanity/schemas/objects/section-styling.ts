import {defineField, defineType} from "sanity";
import {sectionStylingObjectDict} from '../../dictionary/studio/schemas/objects/section-styling';

const { fields } = sectionStylingObjectDict;

export default defineType({
  name: 'sectionStyling',
  title: fields.title,
  type: 'object',
  description: fields.description,
  fields: [
    defineField({
      name: 'spacing',
      title: fields.spacing,
      type: 'string',
      options: {
        list: [
          { title: fields.spacing_none, value: 'none' },
          { title: fields.spacing_small, value: 'small' },
          { title: fields.spacing_medium, value: 'medium' },
          { title: fields.spacing_large, value: 'large' },
          { title: fields.spacing_xlarge, value: 'xlarge' }
        ]
      },
      initialValue: 'medium'
    }),
    defineField({
      name: 'backgroundColor',
      title: fields.backgroundColor,
      type: 'string',
      options: {
        list: [
          { title: fields.backgroundColor_transparent, value: 'transparent' },
          { title: fields.backgroundColor_white, value: 'white' },
          { title: fields.backgroundColor_gray, value: 'gray' },
          { title: fields.backgroundColor_dark, value: 'dark' },
          { title: fields.backgroundColor_primary, value: 'primary' },
          { title: fields.backgroundColor_secondary, value: 'secondary' }
        ]
      },
      initialValue: 'transparent'
    }),
    defineField({
      name: 'containerStyle',
      title: fields.containerStyle,
      type: 'string',
      options: {
        list: [
          { title: fields.containerStyle_full, value: 'full' },
          { title: fields.containerStyle_contained, value: 'contained' }
        ]
      },
      initialValue: 'contained'
    }),
    defineField({
      name: 'containerWidth',
      title: fields.containerWidth,
      type: 'string',
      options: {
        list: [
          { title: fields.containerWidth_standard, value: 'standard' },
          { title: fields.containerWidth_wide, value: 'wide' },
          { title: fields.containerWidth_full, value: 'full' }
        ]
      },
      initialValue: 'standard'
    }),
    defineField({
      name: 'verticalPadding',
      title: fields.verticalPadding,
      type: 'string',
      options: {
        list: [
          { title: fields.verticalPadding_none, value: 'none' },
          { title: fields.verticalPadding_small, value: 'small' },
          { title: fields.verticalPadding_medium, value: 'medium' },
          { title: fields.verticalPadding_large, value: 'large' },
          { title: fields.verticalPadding_xlarge, value: 'xlarge' }
        ]
      },
      initialValue: 'medium'
    }),
    defineField({
      name: 'horizontalPadding',
      title: fields.horizontalPadding,
      type: 'string',
      options: {
        list: [
          { title: fields.horizontalPadding_none, value: 'none' },
          { title: fields.horizontalPadding_small, value: 'small' },
          { title: fields.horizontalPadding_medium, value: 'medium' },
          { title: fields.horizontalPadding_large, value: 'large' },
          { title: fields.horizontalPadding_xlarge, value: 'xlarge' }
        ]
      },
      initialValue: 'medium'
    }),
    defineField({
      name: 'borderRadius',
      title: fields.borderRadius,
      type: 'string',
      options: {
        list: [
          { title: fields.borderRadius_none, value: 'none' },
          { title: fields.borderRadius_small, value: 'small' },
          { title: fields.borderRadius_medium, value: 'medium' },
          { title: fields.borderRadius_large, value: 'large' },
          { title: fields.borderRadius_full, value: 'full' }
        ]
      },
      initialValue: 'none'
    }),
    defineField({
      name: 'shadowSm',
      title: fields.shadow,
      type: 'string',
      options: {
        list: [
          { title: fields.shadow_none, value: 'none' },
          { title: fields.shadow_small, value: 'small' },
          { title: fields.shadow_medium, value: 'medium' },
          { title: fields.shadow_large, value: 'large' },
          { title: fields.shadow_xlarge, value: 'xlarge' }
        ]
      },
      initialValue: 'none'
    })
  ]
});
