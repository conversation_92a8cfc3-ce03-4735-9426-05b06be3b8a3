import {Heading1} from "lucide-react";
import {defineField, defineType} from "sanity";
import {headingObjectDict} from '../../dictionary/studio/schemas/objects/heading';

const { fields } = headingObjectDict;

export default defineType({
  name: 'headingObject',
  title: fields.title,
  type: 'object',
  fields: [
    defineField({
      name: 'headingText',
      type: 'string',
      title: fields.headingText,
    }),
    defineField({
      name: "headingTag",
      type: "string",
      title: fields.headingTag,
      options: {
        list: [
          { title: fields.headingTag_h2, value: "h2" },
          { title: fields.headingTag_h3, value: "h3" },
          { title: fields.headingTag_h4, value: "h4" },
          { title: fields.headingTag_h5, value: "h5" },
          { title: fields.headingTag_h6, value: "h6" },
        ],
      },
      initialValue: 'h2',
    }),
    defineField({
      name: "headingSize",
      type: "string",
      title: fields.headingSize,
      options: {
        list: [
          { title: fields.headingSize_xxxl, value: "xxxl" },
          { title: fields.headingSize_xxl, value: "xxl" },
          { title: fields.headingSize_xl, value: "xl" },
          { title: fields.headingSize_lg, value: "lg" },
          { title: fields.headingSize_md, value: "md" },
          { title: fields.headingSize_sm, value: "sm" },
          { title: fields.headingSize_xs, value: "xs" },
        ],
      },
      initialValue: 'xl',
    }),
  ],
  preview: {
    select: {
      title: 'headingText',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Heading1,
      }
    },
  },
})