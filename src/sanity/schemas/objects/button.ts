import {defineType} from "sanity";
import {RectangleHorizontal} from "lucide-react";
import {buttonFields} from "../misc/button-fields";
import {buttonObjectDict} from '../../dictionary/studio/schemas/objects/button';

const { fields } = buttonObjectDict.en;

export default defineType({
  name: 'buttonObject',
  title: fields.title,
  type: 'object',
  fields: [ ...buttonFields ],
  preview: {
    select: {
      title: 'buttonText',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: RectangleHorizontal,
      }
    },
  },
})