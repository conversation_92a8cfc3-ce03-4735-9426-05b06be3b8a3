import {fieldsets} from "../misc/fieldsets";
import {fieldGroups} from "../misc/field-groups";
import {pageReferenceTypes} from "../misc/page-reference-types";
import {defineArrayMember, defineField, defineType} from "sanity";
import {navigationSettingsDict} from '../../dictionary/studio/schemas/singletons/navigation-settings';

const { document, fields, descriptions } = navigationSettingsDict;

export default defineType({
  name: document.name,
  title: document.title,
  type: 'document',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'navbarMenuItems',
      title: fields.navbarMenuItems,
      type: 'array',
      group: 'navbar',
      fieldset: 'navbar',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              title: fields.menuItemType,
              name: "menuItemType",
              type: "string",
              options: {
                list: [
                  { title: fields.menuItemTypeSingle, value: "single" },
                  { title: fields.menuItemTypeGroup, value: "group" },
                ],
              },
              initialValue: 'single',
            }),
            defineField({
              name: 'title',
              title: fields.title,
              type: 'string',
              description: descriptions.title
            }),
            defineField({
              name: 'pageReference',
              title: fields.pageReference,
              description: descriptions.pageReference,
              type: 'reference',
              to: [ ...pageReferenceTypes ],
              hidden: ({ parent }) => parent?.menuItemType !== 'single'
            }),
            defineField({
              name: 'showChildren',
              title: 'Show Child Pages',
              type: 'boolean',
              description: 'Display child pages as a dropdown menu',
              initialValue: false,
              hidden: ({ parent }) => parent?.menuItemType !== 'single'
            }),
            defineField({
              title: fields.pageReferences,
              name: "pageReferences",
              type: "array",
              of: [{ 
                type: 'reference', 
                to: [ ...pageReferenceTypes ]
              }],
              description: descriptions.pageReferences,
              hidden: ({ parent }) => parent?.menuItemType !== 'group'
            }),
            defineField({
              name: 'isButton',
              title: fields.isButton,
              type: 'boolean',
              description: descriptions.isButton,
              initialValue: false,
              hidden: ({ parent }) => parent?.menuItemType !== 'single'
            })
          ]
        }),
      ],
    }),
    defineField({
      name: 'showSlideOutMenu',
      title: fields.showSlideOutMenu,
      type: 'boolean',
      group: 'slideOutMenu',
      fieldset: 'slideOutMenu',
      initialValue: false
    }),
    defineField({
      name: 'slideOutMenuItems',
      title: fields.slideOutMenuItems,
      type: 'array',
      group: 'slideOutMenu',
      fieldset: 'slideOutMenu',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              title: fields.menuItemType,
              name: "menuItemType",
              type: "string",
              options: {
                list: [
                  { title: fields.menuItemTypeSingle, value: "single" },
                  { title: fields.menuItemTypeGroup, value: "group" },
                ],
              },
              initialValue: 'single',
            }),
            defineField({
              name: 'title',
              title: fields.title,
              type: 'string',
              description: descriptions.title
            }),
            defineField({
              name: 'pageReference',
              title: fields.pageReference,
              description: descriptions.pageReference,
              type: 'reference',
              to: [ ...pageReferenceTypes ],
              hidden: ({ parent }) => parent?.menuItemType !== 'single'
            }),
            defineField({
              name: 'showChildren',
              title: 'Show Child Pages',
              type: 'boolean',
              description: 'Display child pages as a dropdown menu',
              initialValue: false,
              hidden: ({ parent }) => parent?.menuItemType !== 'single'
            }),
            defineField({
              title: fields.pageReferences,
              name: "pageReferences",
              type: "array",
              of: [{ 
                type: 'reference', 
                to: [ ...pageReferenceTypes ]
              }],
              description: descriptions.pageReferences,
              hidden: ({ parent }) => parent?.menuItemType !== 'group'
            }),
            defineField({
              name: 'isButton',
              title: fields.isButton,
              type: 'boolean',
              description: descriptions.isButton,
              initialValue: false
            })
          ]
        }),
      ],
      hidden: ({ parent }) => !parent.showSlideOutMenu
    }),
    defineField({
      name: 'slideOutMenuButtons',
      title: fields.slideOutMenuButtons,
      type: 'array',
      group: 'slideOutMenu',
      fieldset: 'slideOutMenu',
      of: [{ type: 'buttonObject' }],
      description: descriptions.slideOutMenuButtons
    }),
    defineField({
      name: 'showCompanyDetailsSlideOutMenu',
      title: fields.showCompanyDetailsSlideOutMenu,
      type: 'boolean',
      group: 'slideOutMenu',
      fieldset: 'slideOutMenu',
      description: descriptions.showCompanyDetailsSlideOutMenu,
      initialValue: false
    }),
    defineField({
      name: 'footerColumns',
      title: fields.footerColumns,
      type: 'array',
      group: 'footer',
      fieldset: 'footer',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'title',
              title: fields.columnTitle,
              type: 'string',
            }),
            defineField({
              name: 'menuItems',
              title: fields.menuItems,
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    defineField({
                      name: 'title',
                      title: fields.title,
                      type: 'string',
                      description: descriptions.title
                    }),
                    defineField({
                      title: fields.linkType,
                      name: "linkType",
                      type: "string",
                      options: {
                        list: [
                          { title: fields.linkTypeInternal, value: "internal" },
                          { title: fields.linkTypeExternal, value: "external" },
                        ],
                      },
                      initialValue: 'internal',
                    }),
                    defineField({
                      name: 'pageReference',
                      title: fields.pageReference,
                      description: descriptions.pageReference,
                      type: 'reference',
                      to: [ ...pageReferenceTypes ],
                      hidden: ({ parent }) => parent?.linkType !== 'internal',
                    }),
                    defineField({
                      name: 'externalUrl',
                      title: fields.externalUrl,
                      description: descriptions.externalUrl,
                      type: 'url',
                      validation: Rule => Rule.uri({ scheme: ['http', 'https', 'mailto', 'tel'] }),
                      hidden: ({ parent }) => parent?.linkType !== 'external',
                    }),
                  ]
                }
              ]
            })
          ]
        })
      ]
    }),
    defineField({
      name: 'footerLegalMenuItems',
      title: fields.footerLegalMenuItems,
      type: 'array',
      group: 'footer',
      fieldset: 'footer',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'title',
              title: fields.title,
              type: 'string',
              description: descriptions.title
            }),
            defineField({
              name: 'pageReference',
              title: fields.pageReference,
              description: descriptions.pageReference,
              type: 'reference',
              to: [ ...pageReferenceTypes ]
            }),
          ]
        }),
      ],
    }),
  ],
  preview: {
    prepare() {
      return {
        title: document.title,
      }
    },
  },
})
