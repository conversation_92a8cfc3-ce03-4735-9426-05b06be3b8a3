import {defineField, defineType} from "sanity";
import {blogSettingsDict} from "../../dictionary/studio/schemas/settings/blog";

const { document, fields, descriptions } = blogSettingsDict;

export default defineType({
  name: 'blogSettings',
  title: document.title,
  type: 'document',
  fields: [
    defineField({
      title: fields.showRelatedPosts,
      name: 'showRelatedPosts',
      type: 'boolean',
      description: descriptions.showRelatedPosts,
      initialValue: true
    }),
    defineField({
      title: fields.showTableOfContents,
      name: 'showTableOfContents',
      type: 'boolean',
      description: descriptions.showTableOfContents,
      initialValue: true
    }),
    defineField({
      title: fields.showPostsByCategory,
      name: 'showPostsByCategory',
      type: 'boolean',
      description: descriptions.showPostsByCategory,
      initialValue: true
    }),
  ]
})