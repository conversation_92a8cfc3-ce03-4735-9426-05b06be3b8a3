import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {marketingSettingsDict} from "../../dictionary/studio/schemas/settings/marketing";

const { document, fields, descriptions } = marketingSettingsDict;

export default defineType({
  name: 'marketingSettings',
  title: document.title,
  type: 'document',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: "googleAnalyticsId",
      type: "string",
      title: fields.googleAnalyticsId,
      description: descriptions.googleAnalyticsId,
    }),
    defineField({
      name: "googleTagManagerId",
      type: "string",
      title: fields.googleTagManagerId,
      description: descriptions.googleTagManagerId,
    }),
  ]
})