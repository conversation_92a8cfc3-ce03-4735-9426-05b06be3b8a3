import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {projectsPageDict} from '../../../dictionary/studio/schemas/singletons/pages/projects-page';

const { document, fields, descriptions } = projectsPageDict;

export default defineType({
  name: document.name,
  title: document.title,
  type: 'document',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
    }),
    defineField({
      name: 'slug',
      title: fields.slug,
      type: 'slug',
      options: {
        source: 'title',
      },
    }),
    defineField({
      name: 'pageBuilder',
      title: fields.pageBuilder,
      type: 'pageBuilder',
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
    }),
  ]
})