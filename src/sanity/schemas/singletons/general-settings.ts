import {fieldsets} from "../misc/fieldsets";
import {defineField, defineType} from "sanity";
import {fieldGroups} from "../misc/field-groups";
import {pageReferenceTypes} from "../misc/page-reference-types";
import {generalSettingsDict} from "../../dictionary/studio/schemas/settings/general";

const { document, fields, validation, descriptions } = generalSettingsDict;

export default defineType({
  name: 'generalSettings',
  title: document.title,
  type: 'document',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: "siteTitle",
      type: "string",
      title: fields.siteName,
      validation: rule => rule.required().error(validation.siteNameRequired)
    }),
    defineField({
      title: fields.logo ?? 'Logo',
      name: 'siteLogo',
      type: 'image',
    }),
    defineField({
      name: 'homePage',
      title: fields.homePage ?? 'Home Page',
      type: 'reference',
      description: descriptions.homePage ?? undefined,
      to: [ ...pageReferenceTypes ]
    }),
    defineField({
      title: fields.email ?? 'Email',
      name: 'companyEmailAddress',
      type: 'string',
      group: 'companyDetails',
      validation: rule => rule.email().error(validation.emailValidation ?? 'Invalid email')
    }),
    defineField({
      title: fields.phoneNumber ?? 'Phone Number',
      name: 'companyPhoneNumber',
      type: 'string',
      group: 'companyDetails'
    }),
    defineField({
      name: 'companySocialMediaLinks',
      title: fields.socialMedia,
      type: 'array',
      group: 'companyDetails',
      of: [{
        type: 'object',
        fields: [
          defineField({
            name: 'title',
            title: fields.platformName ?? 'Platform Name',
            type: 'string',
            validation: rule => rule.required().error(validation.platformNameRequired ?? 'Required')
          }),
          defineField({
            name: 'profileUrl',
            title: fields.profileUrl ?? 'Profile URL',
            type: 'string',
          })
        ]
      }],
    }),
  ]
})