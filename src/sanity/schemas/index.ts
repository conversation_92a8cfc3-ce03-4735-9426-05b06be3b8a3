import {SchemaTypeDefinition} from "sanity";

import center from "./documents/center";
import generalSettings from "./singletons/general-settings";
import navigationSettings from "./singletons/navigation-settings";
import blogSettings from "./singletons/blog-settings";
import marketingSettings from "./singletons/marketing-settings";
import redirect from "./documents/redirect";
import page from "./documents/page";
import post from "./documents/post";
import postCategory from "./documents/post-category";
import author from "./documents/author";
import testimonial from "./documents/testimonial";
import service from "./documents/service";
import form from "./documents/form";
import project from "./documents/project";
import projectCategory from "./documents/project-category";
import servicesPage from "./singletons/pages/services-page";
import blogPage from "./singletons/pages/blog-page";
import projectsPage from "./singletons/pages/projects-page";
import {pageBuilder} from "./page-builder/page-builder";
import headerBlock from "./page-builder/blocks/header-block";
import heroBlock from "./page-builder/blocks/hero-block";
import logoBlock from "./page-builder/blocks/logo-block";
import featuresMinimalBlock from "./page-builder/blocks/features-minimal-block";
import featureCardsBlock from "./page-builder/blocks/feature-cards-block";
import callToActionBlock from "./page-builder/blocks/call-to-action-block";
import testimonialBlock from "./page-builder/blocks/testimonial-block";
import portableTextBlock from "./page-builder/blocks/portable-text-block";
import freeformBlock from "./page-builder/blocks/freeform-block";
import servicesBlock from "./page-builder/blocks/services-block";
import formBlock from "./page-builder/blocks/form-block";
import mediaBlock from "./page-builder/blocks/media-block";
import gridLayoutBlock from "./page-builder/blocks/grid-layout-block";

// New Advanced Blocks
import contentGridsBlock from "./page-builder/blocks/content-grids-block";
import processTimelinesBlock from "./page-builder/blocks/process-timelines-block";
import statisticsBlock from "./page-builder/blocks/statistics-block";
import carouselBlock from "./page-builder/blocks/carousel-block";
import seoObject from './objects/seo';
import addressObject from './objects/address';
import contactObject from './objects/contact';
import headingObject from './objects/heading';
import richTextObject from './objects/rich-text';
import spacerObject from './objects/spacer';
import videoObject from './objects/video';
import buttonObject from './objects/button';
import singleImageObject from "./objects/single-image";
import callToActionObject from "./objects/call-to-action";
import {gridConfig} from "./objects/grid-config";
import {gridItemConfig} from "./objects/grid-item-config";
import sectionStyling from "./objects/section-styling";

// New Advanced Objects
import {contentItemObject} from "./objects/content-item";
import {filterConfigObject} from "./objects/filter-config";
import {timelineStepObject} from "./objects/timeline-step";
import {timelineConfigObject} from "./objects/timeline-config";
import {animationConfigObject} from "./objects/animation-config";
import {carouselConfigObject} from "./objects/carousel-config";
// Headless Data Source System
import {dataSourceObject} from "./objects/data-source";

const coreSchema = [
  generalSettings,
  navigationSettings,
  marketingSettings,
  blogSettings,
  redirect,
  page,
  post,
  postCategory,
  author,
  testimonial,
  projectCategory,
  project,
  form,
  blogPage,
  service,
  servicesPage,
  projectsPage,
  center,
];

const pageBuilderSchema = [
  pageBuilder,
  heroBlock,
  headerBlock,
  featureCardsBlock,
  featuresMinimalBlock,
  freeformBlock,
  portableTextBlock,
  callToActionBlock,
  logoBlock,
  mediaBlock,
  testimonialBlock,
  servicesBlock,
  formBlock,
  gridLayoutBlock,
  // New Advanced Blocks
  contentGridsBlock,
  processTimelinesBlock,
  statisticsBlock,
  carouselBlock
];

const objectSchema = [
  seoObject,
  addressObject,
  contactObject,
  headingObject,
  richTextObject,
  buttonObject,
  singleImageObject,
  spacerObject,
  callToActionObject,
  videoObject,
  gridConfig,
  gridItemConfig,
  sectionStyling,
  // New Advanced Objects
  contentItemObject,
  filterConfigObject,
  timelineStepObject,
  timelineConfigObject,
  animationConfigObject,
  carouselConfigObject,
  // Headless Data Source System
  dataSourceObject
];

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    ...coreSchema,
    ...pageBuilderSchema,
    ...objectSchema
  ],
};