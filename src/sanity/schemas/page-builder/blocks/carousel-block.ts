import {Play} from "lucide-react";
import {defineArray<PERSON><PERSON><PERSON>, defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {carouselBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/carousel-block';

const { fields } = carouselBlockDict;

export default defineType({
  name: 'carouselBlock',
  title: fields.title,
  type: 'object',
  icon: Play,
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  description: fields.subtitle,
  fields: [
    // Basic block fields
    defineField({
      name: 'title',
      title: fields.carouselTitle,
      type: 'string',
      group: 'content',
      description: fields.carouselTitleDesc
    }),
    defineField({
      name: 'description',
      title: fields.description,
      type: 'text',
      group: 'content',
      description: fields.descriptionDesc
    }),

    // Carousel configuration
    defineField({
      name: 'carouselConfig',
      title: fields.carouselConfig,
      type: 'carouselConfig',
      group: 'layout',
      description: fields.carouselConfigDesc
    }),

    // Content type selection
    defineField({
      name: 'contentType',
      title: fields.contentType,
      type: 'string',
      group: 'content',
      options: {
        list: [
          { title: fields.contentType_mixed, value: 'mixed' },
          { title: fields.contentType_images, value: 'images' },
          { title: fields.contentType_videos, value: 'videos' },
          { title: fields.contentType_cards, value: 'cards' },
          { title: fields.contentType_blocks, value: 'blocks' }
        ]
      },
      initialValue: 'mixed',
      description: fields.contentTypeDesc
    }),

    // Dynamic content array based on content type
    defineField({
      name: 'slides',
      title: fields.items,
      type: 'array',
      group: 'content',
      of: [
        // Mixed content slides
        defineArrayMember({
          type: 'object',
          name: 'mixedSlide',
          title: fields.mixedSlideTitle,
          // @ts-expect-error: 'hidden' is a valid property in Sanity schema
          hidden: ({ parent }) => parent?.contentType !== 'mixed',
          fields: [
            defineField({
              name: 'slideType',
              title: fields.slideType,
              type: 'string',
              options: {
                list: [
                  { title: fields.slideType_image, value: 'image' },
                  { title: fields.slideType_video, value: 'video' },
                  { title: fields.slideType_card, value: 'card' }
                ]
              },
              initialValue: 'image'
            }),
            defineField({
              name: 'image',
              title: fields.image,
              type: 'image',
              hidden: ({ parent }) => parent?.slideType !== 'image',
              fields: [
                defineField({
                  name: 'alt',
                  title: fields.imageAlt,
                  type: 'string'
                }),
                defineField({
                  name: 'caption',
                  title: fields.imageCaption,
                  type: 'string'
                })
              ]
            }),
            defineField({
              name: 'video',
              title: fields.video,
              type: 'object',
              hidden: ({ parent }) => parent?.slideType !== 'video',
              fields: [
                defineField({
                  name: 'videoFile',
                  title: fields.videoFile,
                  type: 'file',
                  options: { accept: 'video/*' }
                }),
                defineField({
                  name: 'thumbnail',
                  title: fields.videoThumbnail,
                  type: 'image'
                }),
                defineField({
                  name: 'caption',
                  title: fields.videoCaption,
                  type: 'string'
                })
              ]
            }),
            defineField({
              name: 'content',
              title: fields.content,
              type: 'array',
              hidden: ({ parent }) => parent?.slideType !== 'card',
              of: [{ type: 'block' }]
            })
          ],
          preview: {
            select: {
              slideType: 'slideType',
              image: 'image',
              caption: 'image.caption'
            },
            prepare({ slideType, image, caption }) {
              return {
                title: caption || `${slideType} slide`,
                media: image
              }
            }
          }
        }),

        // Image slides
        defineArrayMember({
          type: 'image',
          name: 'imageSlide',
          title: fields.imageSlideTitle,
          // @ts-expect-error: 'hidden' is a valid property in Sanity schema
          hidden: ({ parent }) => parent?.contentType !== 'images',
          fields: [
            defineField({
              name: 'alt',
              title: fields.imageAlt,
              type: 'string'
            }),
            defineField({
              name: 'caption',
              title: fields.imageCaption,
              type: 'string'
            })
          ]
        }),

        // Card slides
        defineArrayMember({
          type: 'object',
          name: 'cardSlide',
          title: fields.cardSlideTitle,
          // @ts-expect-error: 'hidden' is a valid property in Sanity schema
          hidden: ({ parent }) => parent?.contentType !== 'cards',
          fields: [
            defineField({
              name: 'title',
              title: fields.cardTitle,
              type: 'string'
            }),
            defineField({
              name: 'description',
              title: fields.cardDescription,
              type: 'text'
            }),
            defineField({
              name: 'image',
              title: fields.cardImage,
              type: 'image',
              fields: [
                defineField({
                  name: 'alt',
                  title: fields.cardImageAlt,
                  type: 'string'
                })
              ]
            }),
            defineField({
              name: 'button',
              title: fields.cardButton,
              type: 'object',
              fields: [
                defineField({
                  name: 'text',
                  title: fields.cardButtonText,
                  type: 'string'
                }),
                defineField({
                  name: 'url',
                  title: fields.cardButtonUrl,
                  type: 'url'
                }),
                defineField({
                  name: 'style',
                  title: fields.cardButtonStyle,
                  type: 'string',
                  options: {
                    list: [
                      { title: fields.cardButtonStyle_primary, value: 'primary' },
                      { title: fields.cardButtonStyle_secondary, value: 'secondary' },
                      { title: fields.cardButtonStyle_outline, value: 'outline-solid' }
                    ]
                  },
                  initialValue: 'primary'
                })
              ]
            })
          ],
          preview: {
            select: {
              title: 'title',
              description: 'description',
              image: 'image',
              buttonText: 'button.text'
            },
            prepare({ title, description, image, buttonText }) {
              return {
                title: title || fields.cardSlideTitle,
                subtitle: description || buttonText || 'No content',
                media: image
              }
            }
          }
        }),

        // Block reference slides
        defineArrayMember({
          type: 'reference',
          name: 'blockSlide',
          title: fields.blockSlideTitle,
          // @ts-expect-error: 'hidden' is a valid property in Sanity schema
          hidden: ({ parent }) => parent?.contentType !== 'blocks',
          to: [
            { type: 'heroBlock' },
            { type: 'featureCardsBlock' },
            { type: 'testimonialBlock' },
            { type: 'callToActionBlock' },
            { type: 'freeformBlock' }
          ]
        })
      ]
    }),

    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
      group: 'settings'
    })
  ],

  preview: {
    select: {
      title: 'title',
      contentType: 'contentType',
      slides: 'slides',
      autoplay: 'carouselConfig.autoplay'
    },
    prepare({ title, contentType, slides, autoplay }) {
      const slideCount = slides ? slides.length : 0
      const features = []
      if (autoplay) features.push('Autoplay')
      
      return {
        title: title || fields.noTitle,
        subtitle: `${slideCount} ${contentType} slides${features.length ? ' • ' + features.join(' • ') : ''}`,
        media: Play
      }
    }
  }
})
