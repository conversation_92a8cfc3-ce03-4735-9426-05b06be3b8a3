import {Clock} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {
    processTimelinesBlockDict
} from '../../../dictionary/studio/schemas/page-builder/blocks/process-timelines-block';

const { fields, validation, descriptions } = processTimelinesBlockDict;

export default defineType({
  name: 'processTimelinesBlock',
  title: fields.title,
  type: 'object',
  icon: Clock,
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      group: 'content',
      description: descriptions.title
    }),
    defineField({
      name: 'subtitle',
      title: fields.subtitle,
      type: 'text',
      rows: 2,
      group: 'content',
      description: descriptions.subtitle
    }),
    defineField({
      name: 'steps',
      title: fields.steps,
      type: 'array',
      group: 'content',
      of: [{ type: 'timelineStep' }],
      validation: Rule => Rule.min(2).error(validation.minSteps).max(20).error(validation.maxSteps),
      description: descriptions.steps
    }),
    defineField({
      name: 'timelineConfig',
      title: fields.timelineConfig,
      type: 'timelineConfig',
      group: 'layout',
      description: descriptions.timelineConfig
    }),
    defineField({
      name: 'showOverallProgress',
      title: fields.showOverallProgress,
      type: 'boolean',
      group: 'layout',
      initialValue: true,
      description: descriptions.showOverallProgress
    }),
    defineField({
      name: 'backgroundColor',
      title: fields.backgroundColor,
      type: 'simplerColor',
      group: 'appearance',
      description: descriptions.backgroundColor
    }),
    defineField({
      name: 'accentColor',
      title: fields.accentColor,
      type: 'simplerColor',
      group: 'appearance',
      description: descriptions.accentColor
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
      group: 'settings'
    }),
  ],
  preview: {
    select: {
      title: 'title',
      steps: 'steps',
      layout: 'timelineConfig.layout',
      showProgress: 'showOverallProgress'
    },
    prepare({ title, steps, layout, showProgress }) {
      const stepCount = steps?.length || 0;
      const completedSteps = steps?.filter((step: { status: string }) => step.status === 'completed').length || 0;
      const features = [];
      if (layout) features.push(layout);
      if (showProgress) features.push('Progress');
      return {
        title: title || fields.title,
        subtitle: `${stepCount} ${fields.steps} (${completedSteps} completed)${features.length ? ' • ' + features.join(' • ') : ''}`,
        media: Clock
      };
    }
  }
});
