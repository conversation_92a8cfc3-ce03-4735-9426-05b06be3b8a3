import {BriefcaseBusiness} from "lucide-react";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {paddingFields} from "../../misc/padding-fields";
import {defineArrayMember, defineField, defineType} from "sanity";
import {servicesBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/services-block';

const { fields } = servicesBlockDict;

export default defineType({
  name: 'servicesBlock',
  title: fields.title,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'services',
      title: fields.services,
      type: 'array',
      of: [
        defineArrayMember({
          type: 'reference',
          to: [{ type: 'service' }]
        })
      ]
    }),
    defineField({
      title: fields.background,
      name: 'background',
      type: 'string',
      options: {
        list: [
          { title: fields.background_white, value: 'white' },
          { title: fields.background_pattern, value: 'pattern' },
        ],
      },
      initialValue: 'white',
    }),
    defineField({
      title: fields.topCornerRadius,
      name: 'topCornerRadius',
      type: 'string',
      options: {
        list: [
          { title: fields.topCornerRadius_straight, value: 'straight' },
          { title: fields.topCornerRadius_rounded, value: 'rounded-sm' },
        ],
      },
      initialValue: 'straight',
    }),
    defineField({
      name: 'buttons',
      title: fields.buttons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
    ...paddingFields
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noHeading,
        subtitle: fields.subtitle,
        media: BriefcaseBusiness,
      }
    },
  },
})