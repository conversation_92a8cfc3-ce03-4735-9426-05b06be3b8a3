import {LetterText} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {AlignmentInput, alignmentOptions} from "@/sanity/components/alignment-input";
import {portableTextBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/portable-text-block';

const { fields } = portableTextBlockDict;

export default defineType({
  name: 'portableTextBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      of: [
        { type: 'block' },
        { type: 'callToActionObject' }
      ],
    }),
    defineField({
      title: fields.alignment,
      name: 'alignment',
      type: 'string',
      options: {
        list: alignmentOptions.map(({ title, value }) => ({ title, value })),
        layout: 'radio',
      },
      components: { input: AlignmentInput },
      initialValue: 'center',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'title',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: LetterText,
      };
    },
  },
})