import {Shapes} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {SpacingInput, spacingOptions} from "@/sanity/components/spacing-input";
import {AlignmentInput, alignmentOptions} from "@/sanity/components/alignment-input";
import {freeformBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/freeform-block';

const { fields, descriptions } = freeformBlockDict;

export default defineType({
  name: 'freeformBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
    }),
    defineField({
      title: fields.columnsPerRow,
      name: 'columnsPerRow',
      type: 'string',
      options: {
        list: [
          { title: '2', value: '2' },
          { title: '3', value: '3' },
          { title: '4', value: '4' },
        ],
      },
      initialValue: '2',
    }),
    defineField({
      name: 'columns',
      title: fields.columns,
      type: 'array',
      of: [{
        type: 'object',
        fields: [
          defineField({
            name: 'title',
            type: 'string',
            title: fields.columnTitle,
            description: descriptions.columnTitle
          }),
          defineField({
            title: fields.spacing,
            name: 'spacing',
            type: 'string',
            description: descriptions.spacing,
            options: {
              list: spacingOptions.map(({ title, value }) => ({ title, value })),
              layout: 'radio',
            },
            components: { input: SpacingInput },
            initialValue: 'small',
          }),
          defineField({
            title: fields.alignment,
            name: 'alignment',
            type: 'string',
            options: {
              list: alignmentOptions.map(({ title, value }) => ({ title, value })),
              layout: 'radio',
            },
            components: { input: AlignmentInput },
            initialValue: 'left',
          }),
          defineField({
            name: 'items',
            title: fields.items,
            type: 'array',
            of: [
              defineField({ 
                name: 'spacerObject', 
                type: 'spacerObject', 
                title: fields.spacerObject, 
              }),
              defineField({ 
                name: 'headingObject', 
                type: 'headingObject', 
                title: fields.headingObject, 
              }),
              defineField({ 
                name: 'richTextObject', 
                type: 'richTextObject', 
                title: fields.richTextObject, 
              }),
              defineField({ 
                name: 'buttonObject', 
                type: 'buttonObject', 
                title: fields.buttonObject, 
              }),
              defineField({ 
                name: 'singleImageObject', 
                type: 'singleImageObject', 
                title: fields.singleImageObject, 
              }),
            ]
          }),
        ],
      }]
    }),
    defineField({
      title: fields.border,
      name: 'border',
      type: 'string',
      description: descriptions.border,
      options: {
        list: [
          { title: fields.border_none, value: 'none' },
          { title: fields.border_topBottom, value: 'topBottom' },
          { title: fields.border_top, value: 'top' },
          { title: fields.border_bottom, value: 'bottom' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'title',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Shapes,
      };
    },
  },
})