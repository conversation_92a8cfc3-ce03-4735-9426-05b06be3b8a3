import {Grid3X3} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {gridLayoutBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/grid-layout-block';

const { fields, descriptions } = gridLayoutBlockDict;

export default defineType({
  name: 'gridLayoutBlock',
  title: fields.subtitle,
  type: 'object',
  icon: Grid3X3,
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      description: descriptions.title,
      group: 'content'
    }),
    defineField({
      name: 'displayTitle',
      title: fields.displayTitle,
      type: 'boolean',
      description: descriptions.displayTitle,
      initialValue: false,
      group: 'content'
    }),
    defineField({
      name: 'publicTitle',
      title: fields.publicTitle,
      type: 'string',
      description: descriptions.publicTitle,
      hidden: ({ document }) => !document?.displayTitle,
      group: 'content'
    }),
    defineField({
      name: 'description',
      title: fields.description,
      type: 'text',
      description: descriptions.description,
      rows: 3,
      hidden: ({ document }) => !document?.displayTitle,
      group: 'content'
    }),
    defineField({
      name: 'gridConfig',
      title: fields.gridConfig,
      type: 'gridConfig',
      description: descriptions.gridConfig,
      group: 'layout'
    }),
    defineField({
      name: 'gridStyling',
      title: fields.gridStyling,
      type: 'sectionStyling',
      description: descriptions.gridStyling,
      group: 'appearance'
    }),
    defineField({
      name: 'gridItems',
      title: fields.gridItems,
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'gridItem',
          title: fields.gridItem,
          fields: [
            defineField({
              name: 'gridItemConfig',
              title: fields.gridItemConfig,
              type: 'gridItemConfig',
              description: descriptions.gridItemConfig
            }),
            defineField({
              name: 'block',
              title: fields.block,
              type: 'array',
              of: [
                { type: 'heroBlock' },
                { type: 'headerBlock' },
                { type: 'featureCardsBlock' },
                { type: 'featuresMinimalBlock' },
                { type: 'freeformBlock' },
                { type: 'portableTextBlock' },
                { type: 'callToActionBlock' },
                { type: 'logoBlock' },
                { type: 'testimonialBlock' },
                { type: 'servicesBlock' },
                { type: 'formBlock' },
                { type: 'mediaBlock' }
              ],
              validation: Rule => Rule.required().max(1),
              description: descriptions.block
            })
          ],
          preview: {
            select: {
              blockType: 'block.0._type',
              columnSpan: 'gridItemConfig.columnSpan.desktop',
              rowSpan: 'gridItemConfig.rowSpan'
            },
            prepare({ blockType, columnSpan, rowSpan }) {
              const blockTitle = blockType ? blockType.replace('Block', '') : 'Empty';
              const span = [];
              if (columnSpan && columnSpan > 1) span.push(`${columnSpan} cols`);
              if (rowSpan && rowSpan > 1) span.push(`${rowSpan} rows`);
              const spanText = span.length > 0 ? ` (${span.join(', ')})` : '';
              
              return {
                title: `${blockTitle}${spanText}`,
                subtitle: blockType ? `${fields.gridItem} containing ${blockType}` : 'Empty grid item'
              };
            }
          }
        }
      ],
      group: 'content'
    })
  ],
  preview: {
    select: {
      title: 'title',
      publicTitle: 'publicTitle',
      displayTitle: 'displayTitle',
      gridItemsCount: 'gridItems'
    },
    prepare({ title, publicTitle, displayTitle, gridItemsCount }) {
      const itemCount = Array.isArray(gridItemsCount) ? gridItemsCount.length : 0;
      const displayedTitle = displayTitle && publicTitle ? publicTitle : title || fields.noTitle;
      
      return {
        title: displayedTitle,
        subtitle: `${fields.subtitle} with ${itemCount} item${itemCount !== 1 ? 's' : ''}`,
        media: Grid3X3
      };
    }
  }
});
