import {Shapes} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {featuresMinimalBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/features-minimal-block';

const { fields, descriptions } = featuresMinimalBlockDict;

export default defineType({
  name: 'featuresMinimalBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [{ title: 'Normal', value: 'normal' }],
          lists: [],
        },
      ],
    }),
    defineField({
      name: 'buttons',
      title: fields.buttons,
      description: descriptions.buttons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      name: 'features',
      title: fields.features,
      type: 'array',
      of: [{ type: 'string' }],
    }),
    defineField({
      name: 'enableBorderTop',
      title: fields.enableBorderTop,
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: "cornerRadiusTop",
      title: fields.cornerRadiusTop,
      type: "string",
      options: {
        list: [
          { title: fields.rounded, value: "rounded-sm" },
          { title: fields.straight, value: "straight" },
        ],
      },
      initialValue: 'rounded-sm',
      hidden: ({ parent }) => parent?.enableBorderTop === false,
    }),
    defineField({
      name: 'enableBorderBottom',
      title: fields.enableBorderBottom,
      type: 'boolean',
    }),
    defineField({
      name: "cornerRadiusBottom",
      title: fields.cornerRadiusBottom,
      type: "string",
      options: {
        list: [
          { title: fields.rounded, value: "rounded-sm" },
          { title: fields.straight, value: "straight" },
        ],
      },
      initialValue: 'rounded-sm',
      hidden: ({ parent }) => parent?.enableBorderBottom === false,
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Shapes,
      }
    },
  },
})