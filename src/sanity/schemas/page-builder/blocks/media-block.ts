import {Image} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {mediaBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/media-block';

const { fields } = mediaBlockDict;

export default defineType({
  name: 'mediaBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      title: fields.backgroundType,
      name: 'backgroundType',
      type: 'string',
      options: {
        list: [
          { title: fields.backgroundType_image, value: 'image' },
          { title: fields.backgroundType_video, value: 'video' },
        ],
      },
      initialValue: 'image',
    }),
    defineField({
      title: fields.backgroundWidth,
      name: 'backgroundWidth',
      type: 'string',
      options: {
        list: [
          { title: fields.backgroundWidth_full, value: 'full' },
          { title: fields.backgroundWidth_contained, value: 'contained' },
        ],
      },
      initialValue: 'full',
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string'
        }),
      ],
      hidden: ({ parent }) => parent?.backgroundType !== 'image',
    }),
    defineField({
      title: fields.overlayType,
      name: 'overlayType',
      type: 'string',
      options: {
        list: [
          { title: fields.overlayType_none, value: 'none' },
          { title: fields.overlayType_dark, value: 'dark' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      title: fields.dialogType,
      name: 'dialogType',
      type: 'string',
      options: {
        list: [
          { title: fields.dialogType_none, value: 'none' },
          { title: fields.dialogType_video, value: 'video' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      name: 'videoUrl',
      title: fields.videoUrl,
      type: 'string',
      hidden: ({ parent }) => parent?.dialogType !== 'video',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'image.altText',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Image,
      };
    },
  },
})