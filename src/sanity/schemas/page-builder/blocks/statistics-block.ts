import {TrendingUp} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {statisticsBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/statistics-block';

const { fields, validation, descriptions } = statisticsBlockDict;

export default defineType({
  name: 'statisticsBlock',
  title: fields.title,
  type: 'object',
  icon: TrendingUp,
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: 'title',
      title: fields.title,
      type: 'string',
      group: 'content',
      description: descriptions.title
    }),
    defineField({
      name: 'description',
      title: fields.description,
      type: 'text',
      rows: 2,
      group: 'content',
      description: descriptions.description
    }),
    defineField({
      name: 'statistics',
      title: fields.statistics,
      type: 'array',
      group: 'content',
      of: [
        {
          type: 'object',
          name: 'statistic',
          title: fields.statisticItem,
          fields: [
            defineField({
              name: 'label',
              title: fields.label,
              type: 'string',
              validation: Rule => Rule.required().error(validation.labelRequired),
              description: descriptions.label
            }),
            defineField({
              name: 'description',
              title: fields.statisticDescription,
              type: 'text',
              rows: 2,
              description: descriptions.statisticDescription
            }),
            defineField({
              name: 'dataSource',
              title: fields.dataSource,
              type: 'dataSource',
              validation: Rule => Rule.custom((value) => {
                if (!value) return validation.dataSourceRequired;
                if ((value as { outputFormat: string })?.outputFormat !== 'number') {
                  return validation.dataSourceNumber;
                }
                return true;
              }),
              description: descriptions.dataSource
            }),
            defineField({
              name: 'numberFormatting',
              title: fields.numberFormatting,
              type: 'object',
              fields: [
                defineField({
                  name: 'prefix',
                  title: fields.prefix,
                  type: 'string',
                  description: descriptions.prefix
                }),
                defineField({
                  name: 'suffix',
                  title: fields.suffix,
                  type: 'string',
                  description: descriptions.suffix
                }),
                defineField({
                  name: 'decimalPlaces',
                  title: fields.decimalPlaces,
                  type: 'number',
                  validation: Rule => Rule.min(0).max(4),
                  initialValue: 0,
                  description: descriptions.decimalPlaces
                }),
                defineField({
                  name: 'useThousandsSeparator',
                  title: fields.useThousandsSeparator,
                  type: 'boolean',
                  initialValue: true,
                  description: descriptions.useThousandsSeparator
                }),
                defineField({
                  name: 'abbreviateLargeNumbers',
                  title: fields.abbreviateLargeNumbers,
                  type: 'boolean',
                  initialValue: false,
                  description: descriptions.abbreviateLargeNumbers
                })
              ]
            }),
            defineField({
              name: 'animation',
              title: fields.animation,
              type: 'animationConfig',
              description: descriptions.animation
            }),
            defineField({
              name: 'icon',
              title: fields.icon,
              type: 'object',
              fields: [
                defineField({
                  name: 'iconType',
                  title: fields.iconType,
                  type: 'string',
                  options: {
                    list: [
                      { title: 'None', value: 'none' },
                      { title: fields.lucideIcon, value: 'lucide' },
                      { title: fields.customImage, value: 'image' },
                      { title: fields.emoji, value: 'emoji' }
                    ]
                  },
                  initialValue: 'none'
                }),
                defineField({
                  name: 'lucideIcon',
                  title: fields.lucideIcon,
                  type: 'string',
                  hidden: ({ parent }) => parent?.iconType !== 'lucide',
                  description: descriptions.lucideIcon
                }),
                defineField({
                  name: 'customImage',
                  title: fields.customImage,
                  type: 'image',
                  hidden: ({ parent }) => parent?.iconType !== 'image',
                  options: {
                    hotspot: true
                  },
                  description: descriptions.customImage
                }),
                defineField({
                  name: 'emoji',
                  title: fields.emoji,
                  type: 'string',
                  hidden: ({ parent }) => parent?.iconType !== 'emoji',
                  validation: Rule => Rule.max(2).error(validation.maxEmoji),
                  description: descriptions.emoji
                }),
                defineField({
                  name: 'position',
                  title: fields.position,
                  type: 'string',
                  options: {
                    list: [
                      { title: 'Above', value: 'above' },
                      { title: 'Left', value: 'left' },
                      { title: 'Right', value: 'right' }
                    ]
                  },
                  hidden: ({ parent }) => parent?.iconType === 'none',
                  initialValue: 'above'
                })
              ]
            }),
            defineField({
              name: 'styling',
              title: fields.styling,
              type: 'object',
              fields: [
                defineField({
                  name: 'numberColor',
                  title: fields.numberColor,
                  type: 'simplerColor',
                  description: descriptions.numberColor
                }),
                defineField({
                  name: 'labelColor',
                  title: fields.labelColor,
                  type: 'simplerColor',
                  description: descriptions.labelColor
                }),
                defineField({
                  name: 'numberSize',
                  title: fields.numberSize,
                  type: 'string',
                  options: {
                    list: [
                      { title: 'Small', value: 'text-2xl' },
                      { title: 'Medium', value: 'text-3xl' },
                      { title: 'Large', value: 'text-4xl' },
                      { title: 'Extra Large', value: 'text-5xl' },
                      { title: 'Huge', value: 'text-6xl' }
                    ]
                  },
                  initialValue: 'text-4xl',
                  description: descriptions.numberSize
                }),
                defineField({
                  name: 'fontWeight',
                  title: fields.fontWeight,
                  type: 'string',
                  options: {
                    list: [
                      { title: 'Normal', value: 'font-normal' },
                      { title: 'Medium', value: 'font-medium' },
                      { title: 'Semibold', value: 'font-semibold' },
                      { title: 'Bold', value: 'font-bold' },
                      { title: 'Extra Bold', value: 'font-extrabold' }
                    ]
                  },
                  initialValue: 'font-bold',
                  description: descriptions.fontWeight
                })
              ]
            })
          ],
          preview: {
            select: {
              label: 'label',
              sourceType: 'dataSource.sourceType',
              staticValue: 'dataSource.staticConfig.value',
              prefix: 'numberFormatting.prefix',
              suffix: 'numberFormatting.suffix',
              animationType: 'animation.animationType'
            },
            prepare({ label, sourceType, staticValue, prefix, suffix, animationType }) {
              let subtitle = sourceType || 'No source';
              if (sourceType === 'static' && staticValue !== undefined) {
                const formatted = `${prefix || ''}${staticValue}${suffix || ''}`;
                subtitle = `Static: ${formatted}`;
              }
              if (animationType) {
                subtitle += ` • ${animationType}`;
              }
              return {
                title: label || fields.statisticItem,
                subtitle,
                media: TrendingUp
              };
            }
          }
        }
      ],
      validation: Rule => Rule.min(1).error(validation.minStatistics).max(12).error(validation.maxStatistics),
      description: 'Add statistics to display'
    }),
    defineField({
      name: 'layout',
      title: fields.layout,
      type: 'object',
      group: 'layout',
      fields: [
        defineField({
          name: 'columns',
          title: fields.columns,
          type: 'object',
          fields: [
            defineField({
              name: 'mobile',
              title: fields.mobile,
              type: 'number',
              validation: Rule => Rule.min(1).error(validation.minMobile).max(3).error(validation.maxMobile),
              initialValue: 1
            }),
            defineField({
              name: 'tablet',
              title: fields.tablet,
              type: 'number',
              validation: Rule => Rule.min(1).error(validation.minTablet).max(4).error(validation.maxTablet),
              initialValue: 2
            }),
            defineField({
              name: 'desktop',
              title: fields.desktop,
              type: 'number',
              validation: Rule => Rule.min(1).error(validation.minDesktop).max(6).error(validation.maxDesktop),
              initialValue: 4
            })
          ]
        }),
        defineField({
          name: 'spacing',
          title: fields.spacing,
          type: 'string',
          options: {
            list: [
              { title: 'Small', value: '4' },
              { title: 'Medium', value: '6' },
              { title: 'Large', value: '8' },
              { title: 'Extra Large', value: '12' }
            ]
          },
          initialValue: '8',
          description: descriptions.spacing
        }),
        defineField({
          name: 'alignment',
          title: fields.alignment,
          type: 'string',
          options: {
            list: [
              { title: 'Left', value: 'left' },
              { title: 'Center', value: 'center' },
              { title: 'Right', value: 'right' }
            ]
          },
          initialValue: 'center',
          description: descriptions.alignment
        })
      ]
    }),
    defineField({
      name: 'globalAnimation',
      title: fields.globalAnimation,
      type: 'object',
      group: 'layout',
      fields: [
        defineField({
          name: 'enableStagger',
          title: fields.enableStagger,
          type: 'boolean',
          initialValue: true,
          description: descriptions.enableStagger
        }),
        defineField({
          name: 'staggerDelay',
          title: fields.staggerDelay,
          type: 'number',
          validation: Rule => Rule.min(0).error(validation.minStaggerDelay).max(2).error(validation.maxStaggerDelay),
          initialValue: 0.2,
          hidden: ({ parent }) => !parent?.enableStagger,
          description: descriptions.staggerDelay
        })
      ]
    }),
    defineField({
      name: 'backgroundColor',
      title: fields.backgroundColor,
      type: 'simplerColor',
      group: 'appearance',
      description: descriptions.backgroundColor
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
      group: 'settings'
    })
  ],
  preview: {
    select: {
      title: 'title',
      statistics: 'statistics',
      enableStagger: 'globalAnimation.enableStagger'
    },
    prepare({ title, statistics, enableStagger }) {
      const count = statistics?.length || 0;
      const features = [];
      if (enableStagger) features.push('Staggered');
      return {
        title: title || fields.title,
        subtitle: `${count} ${fields.statistics}${features.length ? ' • ' + features.join(' • ') : ''}`,
        media: TrendingUp
      };
    }
  }
});
