import {GalleryVertical} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {headerBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/header-block';

const { fields } = headerBlockDict;

export default defineType({
  name: 'headerBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      of: [
        { 
          type: 'block',
          styles: [{ title: 'Normal', value: 'normal' }],
          lists: [],
        },
      ],
    }),
    defineField({
      title: fields.bottomCornerRadius,
      name: 'bottomCornerRadius',
      type: 'string',
      options: {
        list: [
          { title: fields.bottomCornerRadius_straight, value: 'straight' },
          { title: fields.bottomCornerRadius_rounded, value: 'rounded-sm' },
        ],
      },
      initialValue: 'straight',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: GalleryVertical,
      };
    },
  },
})