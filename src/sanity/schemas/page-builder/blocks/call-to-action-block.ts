import {Clapperboard} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {callToActionBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/call-to-action-block';

const { fields } = callToActionBlockDict;

export default defineType({
  name: 'callToActionBlock',
  title: fields.title,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      of: [
        { 
          type: 'block',
          styles: [{ title: fields.style_normal, value: 'normal' }],
          lists: [],
        },
      ],
    }),
    defineField({
      name: 'buttons',
      title: fields.buttons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Clapperboard,
      }
    },
  },
})