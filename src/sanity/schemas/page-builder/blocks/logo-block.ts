import {GripH<PERSON>zon<PERSON>} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {logoBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/logo-block';

const { fields, descriptions } = logoBlockDict;

export default defineType({
  name: 'logoBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'logos',
      title: fields.logos,
      type: 'array',
      of: [{
        type: 'object',
        fields: [
          {
            name: 'title',
            type: 'string',
            title: fields.title,
          },
          {
            name: 'image',
            type: 'image',
            title: fields.image,
          },
          defineField({
            name: 'size',
            title: fields.size,
            type: 'string',
            options: {
              list: [
                { title: fields.size_default, value: 'default' },
                { title: fields.size_large, value: 'large' },
              ],
            },
            initialValue: 'default',
          }),
          {
            name: 'link',
            type: 'url',
            title: fields.link,
            description: descriptions.link
          }
        ]
      }]
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: GripHorizontal,
      };
    },
  },
})