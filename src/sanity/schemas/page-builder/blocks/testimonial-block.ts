import {Star} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {testimonialBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/testimonial-block';

const { fields, descriptions } = testimonialBlockDict;

export default defineType({
  name: 'testimonialBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'eyebrow',
      title: fields.eyebrow,
      type: 'string',
    }),
    defineField({
      name: 'testimonials',
      title: fields.testimonials,
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'testimonial' }] }],
      description: descriptions.testimonials,
    }),
    defineField({
      name: "cornerRadiusTop",
      title: fields.cornerRadiusTop,
      type: "string",
      options: {
        list: [
          { title: fields.rounded, value: "rounded-sm" },
          { title: fields.straight, value: "straight" },
        ],
      },
      initialValue: 'straight',
    }),
    defineField({
      name: "cornerRadiusBottom",
      title: fields.cornerRadiusBottom,
      type: "string",
      options: {
        list: [
          { title: fields.rounded, value: "rounded-sm" },
          { title: fields.straight, value: "straight" },
        ],
      },
      initialValue: 'straight',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Star,
      }
    },
  },
})