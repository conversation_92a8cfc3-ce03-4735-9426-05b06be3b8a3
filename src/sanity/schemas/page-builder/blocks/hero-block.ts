import {GalleryVertical} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {heroBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/hero-block';

const { fields } = heroBlockDict;

export default defineType({
  name: 'heroBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'content',
      title: fields.content,
      type: 'array',
      of: [
        { 
          type: 'block',
          styles: [{ title: 'Normal', value: 'normal' }],
          lists: [],
        },
      ],
    }),
    defineField({
      title: fields.mediaType,
      name: 'mediaType',
      type: 'string',
      options: {
        list: [
          { title: fields.mediaType_image, value: 'image' },
          { title: fields.mediaType_none, value: 'none' },
        ],
      },
      initialValue: 'image',
    }),
    defineField({
      title: fields.bottomCornerRadius,
      name: 'bottomCornerRadius',
      type: 'string',
      options: {
        list: [
          { title: fields.bottomCornerRadius_straight, value: 'straight' },
          { title: fields.bottomCornerRadius_rounded, value: 'rounded-sm' },
        ],
      },
      initialValue: 'straight',
    }),
    defineField({
      name: 'image',
      title: fields.image,
      type: 'image',
      fields: [
        defineField({
          name: 'altText',
          title: fields.altText,
          type: 'string'
        }),
        defineField({
          title: fields.height,
          name: 'height',
          type: 'string',
          options: {
            list: [
              { title: fields.height_full, value: 'full' },
              { title: fields.height_short, value: 'short' },
            ],
          },
          initialValue: 'full',
        }),
      ],
      hidden: ({ parent }) => parent?.mediaType !== 'image',
    }),
    defineField({
      name: 'buttons',
      title: fields.buttons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      title: fields.dialogType,
      name: 'dialogType',
      type: 'string',
      options: {
        list: [
          { title: fields.dialogType_none, value: 'none' },
          { title: fields.dialogType_video, value: 'video' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      name: 'videoUrl',
      title: fields.videoUrl,
      type: 'string',
      hidden: ({ parent }) => parent?.dialogType !== 'video',
    }),
    defineField({
      title: fields.overlayType,
      name: 'overlayType',
      type: 'string',
      options: {
        list: [
          { title: fields.overlayType_none, value: 'none' },
          { title: fields.overlayType_dark, value: 'dark' },
        ],
      },
      initialValue: 'none',
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: GalleryVertical,
      };
    },
  },
})