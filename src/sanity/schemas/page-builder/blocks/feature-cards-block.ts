import {Shapes} from "lucide-react";
import {defineField, defineType} from "sanity";
import {fieldsets} from "../../misc/fieldsets";
import {fieldGroups} from "../../misc/field-groups";
import {buttonFields} from "../../misc/button-fields";
import {featureCardsBlockDict} from '../../../dictionary/studio/schemas/page-builder/blocks/feature-cards-block';

const { fields } = featureCardsBlockDict;

export default defineType({
  name: 'featureCardsBlock',
  title: fields.subtitle,
  type: 'object',
  fieldsets: [ ...fieldsets ],
  groups: [ ...fieldGroups ],
  fields: [
    defineField({
      name: 'heading',
      title: fields.heading,
      type: 'string',
    }),
    defineField({
      name: 'buttons',
      title: fields.buttons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      name: 'features',
      title: fields.features,
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            defineField({
              name: 'title',
              title: fields.featureTitle,
              type: 'string',
            }),
            define<PERSON>ield({
              name: 'description',
              title: fields.featureDescription,
              type: 'text',
              rows: 4
            }),
            defineField({
              name: 'items',
              title: fields.featureItems,
              type: 'array',
              of: [{ type: 'string' }],
            }),
            defineField({
              name: 'image',
              title: fields.featureImage,
              type: 'image',
            }),
            defineField({
              name: 'button',
              title: fields.featureButton,
              type: 'object',
              fields: buttonFields
            })
          ],
        },
      ],
    }),
    defineField({
      name: 'showCallToAction',
      title: fields.showCallToAction,
      type: 'boolean',
    }),
    defineField({
      name: 'callToActionHeading',
      title: fields.callToActionHeading,
      type: 'string',
    }),
    defineField({
      name: 'callToActionContent',
      title: fields.callToActionContent,
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [{ title: 'Normal', value: 'normal' }],
          lists: [],
        },
      ],
    }),
    defineField({
      name: 'callToActionButtons',
      title: fields.callToActionButtons,
      type: 'array',
      of: [{ type: 'buttonObject' }],
    }),
    defineField({
      name: 'anchorId',
      title: fields.anchorId,
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'heading',
      media: '',
    },
    prepare(selection) {
      const { title } = selection
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Shapes,
      }
    },
  },
})