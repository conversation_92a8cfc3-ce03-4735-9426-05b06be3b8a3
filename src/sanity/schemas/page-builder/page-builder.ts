import {defineArrayMember, defineType} from "sanity";

export const pageBuilder = defineType({
  name: 'pageBuilder',
  type: 'array',
  of: [
    defineArrayMember({ name: 'heroBlock', type: 'heroBlock' }),
    defineArrayMember({ name: 'headerBlock', type: 'headerBlock' }),
    defineArrayMember({ name: 'featureCardsBlock', type: 'featureCardsBlock' }),
    defineArrayMember({ name: 'featuresMinimalBlock', type: 'featuresMinimalBlock' }),
    defineArrayMember({ name: 'freeformBlock', type: 'freeformBlock' }),
    defineArrayMember({ name: 'portableTextBlock', type: 'portableTextBlock' }),
    defineArrayMember({ name: 'callToActionBlock', type: 'callToActionBlock' }),
    defineArrayMember({ name: 'logoBlock', type: 'logoBlock' }),
    defineArrayMember({ name: 'testimonialBlock', type: 'testimonialBlock' }),
    defineArrayMember({ name: 'servicesBlock', type: 'servicesBlock' }),
    defineArrayMember({ name: 'formBlock', type: 'formBlock' }),
    defineArrayMember({ name: 'mediaBlock', type: 'mediaBlock' }),
    defineArrayMember({ name: 'gridLayoutBlock', type: 'gridLayoutBlock' }),
    // New Advanced Blocks
    defineArrayMember({ name: 'contentGridsBlock', type: 'contentGridsBlock' }),
    defineArrayMember({ name: 'processTimelinesBlock', type: 'processTimelinesBlock' }),
    defineArrayMember({ name: 'statisticsBlock', type: 'statisticsBlock' }),
    defineArrayMember({ name: 'carouselBlock', type: 'carouselBlock' }),
  ],  options: {
    insertMenu: {
      groups: [
        {
          name: 'intro',
          title: 'Intro',
          of: [ 'heroBlock', 'headerBlock' ]
        },
        {
          name: 'content',
          title: 'Content',
          of: [ 'freeformBlock', 'mediaBlock', 'portableTextBlock', 'contentGridsBlock', 'carouselBlock' ]
        },
        {
          name: 'marketing',
          title: 'Marketing',
          of: [ 'featureCardsBlock', 'featuresMinimalBlock', 'callToActionBlock', 'servicesBlock', 'formBlock', 'statisticsBlock' ]
        },
        {
          name: 'socialProof',
          title: 'Social Proof',
          of: [ 'logoBlock', 'testimonialBlock' ]
        },
        {
          name: 'advanced',
          title: 'Advanced',
          of: [ 'processTimelinesBlock', 'gridLayoutBlock' ]
        }
      ],
      views: [
        {
          name: 'grid', 
          previewImageUrl: (schemaTypeName) => `/sanity/preview-${schemaTypeName}.png`
        },
        { name: 'list' },
      ],
    }
  }
})