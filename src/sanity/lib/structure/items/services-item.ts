import { Folder } from "lucide-react";
import { orderableDocumentListDeskItem } from "@sanity/orderable-document-list";
import { StructureBuilder, StructureResolverContext } from "sanity/structure";
import { servicesItemDict } from '../../../dictionary/studio/structure/items/services-item';

const { items } = servicesItemDict;

export const ServicesItem = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  orderableDocumentListDeskItem({
    S, 
    context, 
    icon: Folder, 
    type: 'service', 
    title: items.services, 
    id: 'orderable-services'
  })
)