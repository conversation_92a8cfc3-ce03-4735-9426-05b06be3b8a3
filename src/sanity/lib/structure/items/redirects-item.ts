import { Link } from "lucide-react";
import { StructureBuilder } from "sanity/structure";
import { redirectsItemDict } from '../../../dictionary/studio/structure/items/redirects-item';

const { items } = redirectsItemDict;

export const RedirectsItem = (
  S: StructureBuilder, 
) => (
  S.listItem()
    .title(items.redirects)
    .icon(Link)
    .child(
      S.documentList()
      .title(items.allRedirects)
      .filter('_type == "redirect"')
    ) 
)