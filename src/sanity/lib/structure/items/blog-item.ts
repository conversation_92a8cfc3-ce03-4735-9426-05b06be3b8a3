import { Folder, Tag, Users } from "lucide-react";
import { StructureBuilder, StructureResolverContext } from "sanity/structure";
import { orderableDocumentListDeskItem} from '@sanity/orderable-document-list';
import { blogItemDict } from '../../../dictionary/studio/structure/items/blog-item';

const { items } = blogItemDict;

export const BlogItem = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  S.listItem()
    .title(items.blog)
    .icon(Folder)
    .child(
      S.list()
        .title(items.blog)
        .items([
          AllPosts(S),
          PostCategories(S, context),
          Authors(S)
        ])
    )
)

export const AllPosts = (
  S: StructureBuilder, 
) => (
  S.listItem()
    .title(items.posts)
    .icon(Folder)
    .child(
      S.documentList()
      .title(items.allPosts)
      .filter('_type == "post"')
    ) 
)

export const PostCategories = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  orderableDocumentListDeskItem({
    S, 
    context, 
    icon: Tag, 
    type: 'postCategory', 
    title: items.categories, 
    id: 'orderable-post-categories'
  })
)

export const Authors = (
  S: StructureBuilder, 
) => (
  S.listItem()
    .title(items.authors)
    .icon(Users)
    .child(
      S.documentList()
      .title(items.authors)
      .filter('_type == "author"')
    ) 
)