import { Send } from "lucide-react";
import { StructureBuilder } from "sanity/structure";
import { formsItemDict } from '../../../dictionary/studio/structure/items/forms-item';

const { items } = formsItemDict;

export const FormsItem = (
  S: StructureBuilder, 
) => (
  S.listItem()
    .title(items.forms)
    .icon(Send)
    .child(
      S.documentList()
      .title(items.allForms)
      .filter('_type == "form"')
    ) 
)