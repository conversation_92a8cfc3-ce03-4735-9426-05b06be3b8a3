import { Settings } from "lucide-react";
import type { StructureBuilder } from "sanity/structure";
import { settingsItemDict } from '../../../dictionary/studio/structure/items/settings-item';

const { items } = settingsItemDict;

export const SettingsItem = (S: StructureBuilder) =>
  S.listItem()
    .title(items.settings)
    .icon(Settings)
    .child(
      S.list()
        .title(items.settings)
        .items([
          S.listItem()
            .title(items.general)
            .child(
              S.document()
              .id('generalSettings')
              .schemaType('generalSettings')
              .documentId('generalSettings')
              .title(items.general)
            ),
          S.listItem()
            .title(items.navigation)
            .child(
              S.document()
                .id('navigationSettings')
                .schemaType('navigationSettings')
                .documentId('navigationSettings')
                .title(items.navigation)
            ),
          S.divider(),
          S.listItem()
            .title(items.marketing)
            .child(
              S.document()
                .id('marketingSettings')
                .schemaType('marketingSettings')
                .documentId('marketingSettings')
                .title(items.marketing)
            ),
          S.listItem()
            .title(items.redirects)
            .child(
              S.documentList()
              .title(items.allRedirects)
              .filter('_type == "redirect"')
            ), 
          S.divider(),
          S.listItem()
            .title(items.blogAndPosts)
            .child(
              S.document()
                .id('blogSettings')
                .schemaType('blogSettings')
                .documentId('blogSettings')
                .title(items.blogAndPosts)
            ),
        ])
    )