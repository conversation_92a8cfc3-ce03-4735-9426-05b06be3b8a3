import { Star } from "lucide-react";
import { StructureBuilder, StructureResolverContext } from "sanity/structure";
import { orderableDocumentListDeskItem} from '@sanity/orderable-document-list';
import { testimonialsItemDict } from '../../../dictionary/studio/structure/items/testimonials-item';

const { items } = testimonialsItemDict;

export const TestimonialsItem = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  orderableDocumentListDeskItem({
    S, 
    context, 
    icon: Star, 
    type: 'testimonial', 
    title: items.testimonials, 
    id: 'orderable-testimonials'
  })
)