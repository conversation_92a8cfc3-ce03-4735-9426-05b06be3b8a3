import { MapPin } from "lucide-react";
import { StructureBuilder } from "sanity/structure";
import { centersItemDict } from '../../../dictionary/studio/structure/items/centers-item';

const { items } = centersItemDict;

export const CentersItem = (S: StructureBuilder) => (
  S.listItem()
    .title(items.centers)
    .icon(MapPin)
    .child(
      S.documentList()
        .title(items.centers)
        .filter('_type == "center"')
        .defaultOrdering([
          { field: 'centerType', direction: 'asc' },
          { field: 'name', direction: 'asc' }
        ])
    )
);
