import { Folder, Tag } from "lucide-react";
import { StructureBuilder, StructureResolverContext } from "sanity/structure";
import { orderableDocumentListDeskItem} from '@sanity/orderable-document-list';
import { projectsItemDict } from '../../../dictionary/studio/structure/items/projects-item';

const { items } = projectsItemDict;

export const ProjectsItem = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  S.listItem()
    .title(items.projects)
    .icon(Folder)
    .child(
      S.list()
        .title(items.projects)
        .items([
          AllProjects(S),
          ProjectCategories(S, context),
        ])
    )
)

export const AllProjects = (
  S: StructureBuilder, 
) => (
  S.listItem()
    .title(items.projects)
    .icon(Folder)
    .child(
      S.documentList()
      .title(items.allProjects)
      .filter('_type == "project"')
    ) 
)

export const ProjectCategories = (
  S: StructureBuilder, 
  context: StructureResolverContext
) => (
  orderableDocumentListDeskItem({
    S, 
    context, 
    icon: Tag, 
    type: 'projectCategory', 
    title: items.categories, 
    id: 'orderable-project-categories'
  })
)