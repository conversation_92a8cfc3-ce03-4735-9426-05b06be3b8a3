import { defineQuery } from "next-sanity";
import { pageBuilder } from "../fragments/page-builder";

export const pageSlugsQuery = defineQuery(`*[_type == "page" && defined(slug.current)] {
  'params': { 'slug': slug.current }
}`);

// Enhanced query to get all pages with full hierarchy for static generation
export const allPagesWithHierarchyQuery = defineQuery(`*[_type == "page" && defined(slug.current)] {
  _id,
  title,
  'slug': slug.current,
  parent->{
    _id,
    title,
    'slug': slug.current,
    parent->{
      _id,
      title,
      'slug': slug.current,
      parent->{
        _id,
        title,
        'slug': slug.current
      }
    }
  }
}`);

// Query to find page by nested path segments
export const pageByNestedSlugQuery = defineQuery(`*[_type == 'page' && slug.current == $slug] {
  _type,
  _id,
  title,
  'slug': slug.current,
  parent->{
    _id,
    title,
    'slug': slug.current,
    parent->{
      _id,
      title,
      'slug': slug.current,
      parent->{
        _id,
        title,
        'slug': slug.current
      }
    }
  },
  ${pageBuilder},
  "seo": {
    "title": coalesce(seo.title, title, ""),
    "description": coalesce(seo.description,  ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

export const pageBySlugQuery = defineQuery(`*[_type == 'page' && slug.current == $slug][0] {
  _type,
  _id,
  title,
  'slug': slug.current,
  parent->{
    _id,
    title,
    'slug': slug.current
  },
  ${pageBuilder},
  "seo": {
    "title": coalesce(seo.title, title, ""),
    "description": coalesce(seo.description,  ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

// New query to get all pages with hierarchy info
export const pagesWithHierarchyQuery = defineQuery(`*[_type == "page"] | order(title asc) {
  _id,
  title,
  'slug': slug.current,
  parent->{
    _id,
    title,
    'slug': slug.current
  },
  'children': *[_type == "page" && references(^._id)] {
    _id,
    title,
    'slug': slug.current
  }
}`);

// Query to get child pages of a specific page
export const childPagesQuery = defineQuery(`*[_type == "page" && parent._ref == $parentId] | order(title asc) {
  _id,
  title,
  'slug': slug.current,
  ${pageBuilder}
}`);

// Query to get page breadcrumbs
export const pageBreadcrumbsQuery = defineQuery(`*[_type == 'page' && slug.current == $slug][0] {
  _id,
  title,
  'slug': slug.current,
  parent->{
    _id,
    title,
    'slug': slug.current,
    parent->{
      _id,
      title,
      'slug': slug.current,
      parent->{
        _id,
        title,
        'slug': slug.current
      }
    }
  }
}`);

// Query for root pages (pages without parents)
export const rootPagesQuery = defineQuery(`*[_type == "page" && !defined(parent)] | order(title asc) {
  _id,
  title,
  'slug': slug.current,
  'children': *[_type == "page" && references(^._id)] {
    _id,
    title,
    'slug': slug.current
  }
}`);
