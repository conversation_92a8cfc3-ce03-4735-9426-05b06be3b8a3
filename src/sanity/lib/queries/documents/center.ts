import { defineQuery } from "next-sanity";
import { pageBuilder } from "../fragments/page-builder";

export const centerSlugsQuery = defineQuery(`*[_type == "center" && defined(slug.current)] {
  'params': { 'slug': slug.current }
}`);

export const centerBySlugQuery = defineQuery(`*[_type == 'center' && slug.current == $slug][0] {
  _id,
  _type,
  name,
  'slug': slug.current,
  centerType,
  autoscoutSellerId,
  shortDescription,
  image {
    ...,
    asset->
  },
  ${pageBuilder},
  address {
    street,
    postalCode,
    city,
    region,
    country,
    coordinates {
      lat,
      lng
    }
  },
  contact {
    phone,
    email,
    website,
    whatsapp
  },
  openingHours[] {
    day,
    hours
  },
  brandConfig {
    primaryColor {
      hex
    },
    secondaryColor {
      hex
    },
    logo {
      ...,
      asset->
    }
  },
  certifications[] {
    name,
    logo {
      ...,
      asset->
    },
    description
  },
  specialties,
  team[] {
    name,
    role,
    photo {
      ...,
      asset->
    },
    directPhone,
    directEmail,
    specialization
  },
  serviceAreas,
  apiSettings {
    enableAutoSync,
    syncFrequency
  },
  searchDefaults {
    defaultSort,
    itemsPerPage
  },
  displaySettings {
    showPrices,
    showFinancing,
    featuredBadge
  },
  "seo": {
    "title": coalesce(seo.title, name, ""),
    "description": coalesce(seo.description, shortDescription, ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

export const allCentersQuery = defineQuery(`*[_type == 'center'] | order(centerType asc, name asc) {
  _id,
  _type,
  name,
  'slug': slug.current,
  centerType,
  shortDescription,
  image {
    ...,
    asset->
  },
  address {
    city,
    region,
    coordinates {
      lat,
      lng
    }
  },
  contact {
    phone,
    email
  },
  brandConfig {
    primaryColor {
      hex
    },
    logo {
      ...,
      asset->
    }
  },
  specialties
}`);

export const centersByTypeQuery = defineQuery(`*[_type == 'center' && centerType == $centerType] | order(name asc) {
  _id,
  _type,
  name,
  'slug': slug.current,
  centerType,
  shortDescription,
  image {
    ...,
    asset->
  },
  address {
    city,
    region,
    coordinates {
      lat,
      lng
    }
  },
  contact {
    phone,
    email
  },
  brandConfig {
    primaryColor {
      hex
    },
    logo {
      ...,
      asset->
    }
  },
  specialties
}`);
