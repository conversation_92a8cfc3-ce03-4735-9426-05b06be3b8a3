import { defineQuery} from "next-sanity";
import { buttonQuery } from "../fragments/misc";

export const navigationSettingsQuery = defineQuery(`*[_type == 'navigationSettings'][0] {
  "navbar": {
    navbarMenuItems[] {
      _key,
      title,
      pageReference->{
        _id,
        _type,
        title,
        "slug": slug.current,
        parent->{
          _id,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current
          }
        },
        "children": *[_type == "page" && parent._ref == ^._id] {
          _id,
          title,
          "slug": slug.current
        }
      },
      pageReferences[]->{
        _id,
        _type,
        title,
        name,
        "slug": slug.current,
        parent->{
          _id,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current
          }
        }
      },
      menuItemType,
      isButton,
      showChildren
    },
  },
  "slideOutMenu": {
    showSlideOutMenu,
    slideOutMenuItems[] {
      _key,
      title,
      _type,
      menuItemType,
      showChildren,
      pageReference->{
        _id,
        _type,
        title,
        "slug": slug.current,
        parent->{
          _id,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current
          }
        },
        "children": *[_type == "page" && parent._ref == ^._id] {
          _id,
          title,
          "slug": slug.current
        }
      },
      pageReferences[]->{
        _id,
        _type,
        title,
        "slug": slug.current,
        parent->{
          _id,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current
          }
        }
      },
    },
    slideOutMenuButtons[] {
      ${buttonQuery}
    },
    showCompanyDetailsSlideOutMenu,
    "slideOutMenuSettings": *[_type == 'generalSettings'][0] {
      companyEmailAddress,
      companyPhoneNumber,
      companyAddress,
      companyName,
      companyTagline,
      companyLogo,
      companyDarkLogo,
    }
  },
  "footer": {
    footerColumns[] {
      _key,
      title,
      menuItems[] {
        _key,
        title,
        linkType,
        pageReference->{
          _id,
          _type,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current,
            parent->{
              _id,
              title,
              "slug": slug.current
            }
          }
        },
        externalUrl
      }
    },
    footerLegalMenuItems[] {
      _key,
      title,
      pageReference->{
        _id,
        _type,
        title,
        "slug": slug.current,
        parent->{
          _id,
          title,
          "slug": slug.current,
          parent->{
            _id,
            title,
            "slug": slug.current
          }
        }
      }
    }
  }
}`);
