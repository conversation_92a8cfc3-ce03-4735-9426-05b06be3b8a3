import {
    heroBlockQuery,
    headerBlockQuery,
    featureBlockQuery,
    featureCardsBlockQuery,
    featuresMinimalBlockQuery,
    callToActionBlockQuery,
    logoBlockQuery,
    testimonialBlockQuery,
    freeformBlockQuery,
    portableTextBlockQuery,
    blogArchiveBlockQuery,
    servicesBlockQuery,
    formBlockQuery,
    mediaBlockQuery,
    gridLayoutBlockQuery, contentGridsBlockQuery, processTimelinesBlockQuery, statisticsBlockQuery, carouselBlockQuery,
} from "./blocks";

export const pageBuilder = `
  pageBuilder[] {
    ${heroBlockQuery},
    ${headerBlockQuery},
    ${featureBlockQuery},
    ${featureCardsBlockQuery},
    ${featuresMinimalBlockQuery},
    ${callToActionBlockQuery},
    ${logoBlockQuery},
    ${testimonialBlockQuery},
    ${freeformBlockQuery},
    ${portableTextBlockQuery},
    ${blogArchiveBlockQuery},
    ${servicesBlockQuery},
    ${formBlockQuery},
    ${mediaBlockQuery},
    ${gridLayoutBlockQuery},
    ${contentGridsBlockQuery},
    ${processTimelinesBlockQuery},
    ${statisticsBlockQuery},
    ${carouselBlockQuery}
  }
`