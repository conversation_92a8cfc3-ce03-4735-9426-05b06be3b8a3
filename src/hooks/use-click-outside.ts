import { useEffect, RefObject } from 'react';

export function useClickOutside<T extends HTMLElement | null>(
  ref: RefObject<T>, handler: () => void
) {
  useEffect(() => {
    
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler()
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }

  }, [ref, handler])
}