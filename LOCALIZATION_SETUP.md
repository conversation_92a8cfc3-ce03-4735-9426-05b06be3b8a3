# Studio UI Localization Setup

## Installation

To complete the Studio UI localization setup, install the required Sanity locale packages:

```bash
npm install @sanity/locale-fr-fr
```

## Environment Configuration

Add the following to your `.env.local` file to set the default language:

```env
# Set default language (en or fr)
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
```

To change the default language to French:

```env
NEXT_PUBLIC_DEFAULT_LANGUAGE=fr
```

## Features

✅ **Default to English** - English is the default language  
✅ **Additional French language** - French translations available  
✅ **Environment-based default** - Set default language via `.env`  
✅ **Modular dictionary structure** - Following sane-kit pattern  
✅ **SOLID principles** - Clean, extensible architecture  
✅ **Type safety** - Full TypeScript support  
✅ **Future-proof** - Easy to add new languages  

## Adding New Languages

To add a new language (e.g., Spanish):

1. Install the locale package:
   ```bash
   npm install @sanity/locale-es-es
   ```

2. Create dictionary files:
   - `src/sanity/dictionary/studio/fields/es.ts`
   - `src/sanity/dictionary/studio/validation/es.ts`
   - `src/sanity/dictionary/studio/descriptions/es.ts`
   - `src/sanity/dictionary/studio/options/es.ts`

3. Add to the respective index files to register the language

4. Update `sanity.config.ts` to include the new locale plugin

5. Update the supported languages type in the studio configuration

The system will automatically detect and validate new languages, preventing runtime errors.

## Usage in Schemas

```typescript
import { fields, documents, validation } from "../../dictionary";

export default defineType({
  name: 'page',
  title: documents.page, // Automatically localized
  fields: [
    defineField({
      name: 'title',
      title: fields.title, // Automatically localized
      validation: rule => rule.required().error(validation.titleRequired) // Localized validation
    }),
  ]
})
```

## Architecture

The system follows SOLID principles:

- **Single Responsibility**: Each dictionary file handles one concern
- **Open/Closed**: Easy to extend with new languages without modifying existing code
- **Liskov Substitution**: All language implementations follow the same interface
- **Interface Segregation**: Separate interfaces for fields, validation, descriptions, options
- **Dependency Inversion**: High-level modules don't depend on low-level details

## Language Registry

The `LanguageRegistry` singleton ensures:
- Only fully supported languages are available
- Safe fallbacks for missing languages
- Environment-based language detection
- Type safety across the entire system
