# Autocorner Website Structure & Content Strategy
## Built on Devocracy-Sanity-Starter Foundation

## Executive Summary

This refined website structure leverages the **Autocorner-J-C-C-Oberson/website** repository (forked from devocracy-sanity-starter) while adding comprehensive Autocorner-specific functionality. The strategy maintains the modern foundation while implementing vehicle management, center operations, and brand-specific features through strategic schema extensions and component additions.

---

## Foundation Analysis

### ✅ **Devocracy Starter Strengths**
**Modern Architecture:**
- Next.js 15 with App Router and Server Components ✅
- tRPC with Zod validation for type-safe APIs ✅
- Sanity Studio with flexible page builder ✅
- Component architecture: UI → Modules → Sections ✅
- Performance optimizations and SEO ready ✅

**Existing Page Builder Blocks:**
- `heroBlock` - Perfect for vehicle showcases ✅
- `callToActionBlock` - Ideal for contact/test drive CTAs ✅
- `testimonialBlock` - Customer reviews ✅
- `servicesBlock` - Service offerings ✅
- `formBlock` - Contact forms ✅
- `statisticsBlock` - Vehicle stats (500+ cars, 35+ years) ✅
- `carouselBlock` - Can be adapted for vehicle galleries ✅

### 🚗 **Required Autocorner Extensions**
**New Document Types:**
- `center` - Physical center management with AutoScout seller IDs
- `vehicleLandingPage` - Brand/model specific pages
- `brandPage` - Audi/Skoda hub pages

**New Page Builder Blocks:**
- `vehicleSearchBlock` - Advanced search interface
- `vehicleShowcaseBlock` - Vehicle grid/carousel with filtering
- `financingCalculatorBlock` - Leasing/credit calculations
- `centerSelectorBlock` - Center navigation widget
- `brandNavigationBlock` - Audi/Skoda brand selector

**API Extensions:**
- AutoScout24 integration via tRPC
- Center-based vehicle filtering
- Real-time inventory management

---

## Site Architecture Strategy

### 🏠 **Homepage (`/`)**
**Current Foundation:** Existing `page` document with `pageBuilder` ✅
**Enhancement Strategy:** Add Autocorner-specific blocks to existing page builder

**Content Block Structure:**
```typescript
pageBuilder: [
  {
    _type: 'heroBlock',
    // Enhanced with vehicle showcase
    title: 'Bienvenue dans l\'univers Autocorner',
    backgroundVideo: 'audi-showcase.mp4'
  },
  {
    _type: 'centerSelectorBlock', // 🆕 NEW
    title: 'Nos centres Autocorner',
    layout: 'grid',
    showVehicleCount: true
  },
  {
    _type: 'vehicleSearchBlock', // 🆕 NEW
    variant: 'featured',
    title: 'Rechercher un véhicule',
    defaultFilters: { condition: 'all' }
  },
  {
    _type: 'vehicleShowcaseBlock', // 🆕 NEW
    title: 'Votre future voiture vous attend...',
    layout: 'grid',
    vehicleFilters: {
      featured: true,
      limit: 12
    }
  },
  {
    _type: 'brandNavigationBlock', // 🆕 NEW
    title: 'Nos marques',
    brands: ['audi', 'skoda'],
    showModelCount: true
  },
  {
    _type: 'statisticsBlock', // ✅ EXISTING
    stats: [
      { value: '500+', label: 'Véhicules disponibles' },
      { value: '35+', label: 'Années d\'expérience' },
      { value: '120+', label: 'Collaborateurs' }
    ]
  },
  {
    _type: 'servicesBlock', // ✅ EXISTING  
    title: 'Vous aimez votre voiture ? Nous aussi !',
    services: [/* service grid */]
  },
  {
    _type: 'testimonialBlock', // ✅ EXISTING
    title: 'Témoignages clients'
  }
]
```

### 🔍 **Vehicle Search & Results (`/vehicules`)**
**New Implementation:** Dedicated search results page with advanced filtering

**Page Structure:**
```typescript
// src/app/[locale]/(frontend)/vehicules/page.tsx
export default function VehicleSearchPage({ searchParams }) {
  return (
    <div className="vehicle-search-page">
      <VehicleSearchForm defaultValues={searchParams} />
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <VehicleFiltersSidebar filters={searchParams} />
        <VehicleResults searchParams={searchParams} />
      </div>
    </div>
  )
}
```

**Search Features:**
- Advanced filtering (make, model, price, year, fuel, center)
- Real-time results with AutoScout24 API
- Save search functionality
- Sort options (price, year, mileage, relevance)
- Grid/list view toggle
- Pagination with infinite scroll option

**URL Structure:**
```
/vehicules                           # All vehicles
/vehicules?make=audi                 # Audi vehicles
/vehicules?make=audi&condition=new   # New Audi vehicles  
/vehicules?center=lutry              # Lutry center vehicles
/vehicules?make=skoda&price_max=50000 # Skoda under 50k
```

### 🚗 **Vehicle Detail Pages (`/vehicules/[id]`)**
**New Implementation:** Comprehensive PDP with AutoScout24 integration

**Page Features:**
- High-resolution image gallery with 360° view
- Complete specifications from AutoScout24 API
- Equipment and options list
- Financing calculator integration
- Contact form with center context
- Related vehicles suggestions
- Virtual tour scheduling
- Test drive booking

**Component Structure:**
```typescript
<VehicleDetailPage>
  <VehicleImageGallery images={vehicle.images} />
  <VehicleSpecifications vehicle={vehicle} />
  <FinancingCalculator vehiclePrice={vehicle.price} />
  <VehicleContactForm vehicle={vehicle} />
  <RelatedVehicles currentVehicle={vehicle} />
</VehicleDetailPage>
```

---

## Brand-Specific Hub Strategy

### 🏢 **Audi Hub (`/audi`)**
**Foundation:** Extend existing `page` document with brand-specific schema

**Schema Enhancement:**
```typescript
// Enhanced page document for brand pages
{
  _type: 'page',
  title: 'Audi - Excellence sportive et luxe',
  slug: { current: 'audi' },
  pageType: 'brandHub', // 🆕 NEW field
  brandConfig: { // 🆕 NEW object
    brand: 'audi',
    primaryColor: '#FF0000',
    heroVideo: 'audi-brand-video.mp4',
    featuredModels: ['A4', 'Q5', 'e-tron']
  },
  pageBuilder: [
    {
      _type: 'heroBlock',
      title: 'Audi - Vorsprung durch Technik',
      brandSpecific: true
    },
    {
      _type: 'vehicleShowcaseBlock',
      title: 'Découvrez la gamme Audi',
      vehicleFilters: {
        make: 'audi',
        featured: true
      },
      layout: 'carousel'
    },
    // Model-specific sections generated dynamically
  ]
}
```

**Content Strategy:**
- Official Audi content integration via web scraping
- Model-specific landing pages (`/audi/a4`, `/audi/q5`)
- Sport and luxury positioning
- RS and S-Line performance showcases
- Electric vehicle focus (e-tron family)

### 🔧 **Skoda Hub (`/skoda`)**
**Same foundation with Skoda-specific theming and content**

**Content Strategy:**
- Value and reliability positioning  
- Family-oriented vehicle focus
- Practical solutions emphasis
- Environmental benefits (Enyaq electric)
- "Simply Clever" features highlighting

**URL Structure for Brand Hubs:**
```
/audi/                    # Audi brand homepage
/audi/modeles/            # All Audi models
/audi/modeles/a4/         # A4 specific page
/audi/modeles/q5/         # Q5 specific page  
/audi/vehicules/          # Available Audi vehicles
/audi/services/           # Audi-specific services
/audi/actualites/         # Audi news and updates

/skoda/                   # Skoda brand homepage
/skoda/modeles/           # All Skoda models
/skoda/modeles/octavia/   # Octavia specific page
/skoda/vehicules/         # Available Skoda vehicles
```

---

## Center Management Strategy

### 🏢 **Center Document Schema**
```typescript
// src/sanity/schemas/documents/center.ts
export const centerSchema = defineType({
  name: 'center',
  title: 'Centre Autocorner',
  fields: [
    { name: 'name', title: 'Nom', type: 'string' },
    { name: 'slug', title: 'Slug', type: 'slug' },
    { name: 'autoscoutSellerId', title: 'AutoScout Seller ID', type: 'string' },
    { name: 'type', title: 'Type', type: 'string', 
      options: { list: ['audi', 'skoda', 'occasions'] } },
    { name: 'address', title: 'Adresse', type: 'object' },
    { name: 'contact', title: 'Contact', type: 'object' },
    { name: 'brandColor', title: 'Couleur', type: 'color' },
    { name: 'logo', title: 'Logo', type: 'image' },
    { name: 'specialties', title: 'Spécialités', type: 'array' },
    { name: 'openingHours', title: 'Horaires', type: 'array' }
  ]
})
```

### 🗺️ **Center-Specific Pages (`/centres/[slug]`)**
**Dynamic routing with center context:**

```typescript
// src/app/[locale]/(frontend)/centres/[slug]/page.tsx
export default async function CenterPage({ params }) {
  const center = await sanityFetch(`
    *[_type == "center" && slug.current == $slug][0]
  `, { slug: params.slug })

  // Auto-inject center context into vehicle blocks
  const pageContent = await sanityFetch(`
    *[_type == "page" && slug.current == $centerPageSlug][0]{
      pageBuilder[]{
        ...,
        _type == "vehicleShowcaseBlock" => {
          ...,
          vehicleFilters {
            ...,
            centerFilter: $centerId
          }
        }
      }
    }
  `, { centerId: center._id })

  return <PageBuilder sections={pageContent.pageBuilder} />
}
```

**Center URL Structure:**
```
/centres/                    # All centers overview
/centres/lutry/              # Audi Lutry homepage  
/centres/lutry/vehicules/    # Lutry vehicles
/centres/lutry/equipe/       # Lutry team
/centres/lutry/contact/      # Lutry contact
/centres/romanel/            # Skoda Romanel
/centres/sion/               # Occasions Sion
```

---

## Enhanced Content Blocks

### 🔍 **Vehicle Search Block**
```typescript
// Enhanced search with center awareness
vehicleSearchBlock: {
  title: 'Rechercher un véhicule',
  variant: 'advanced', // simple | advanced | widget
  centerFilter: {
    enabled: true,
    defaultCenter: 'lutry',
    allowMultipleSelection: true
  },
  defaultFilters: {
    make: 'all',
    condition: 'all'
  },
  searchBehavior: {
    redirectToResults: true,
    showPreview: false
  }
}
```

**Rendered Component Features:**
- Dynamic form generation from AutoScout24 metadata
- Center-aware filtering
- Real-time suggestions
- Advanced filters (price range, year, fuel, transmission)
- Saved searches (for logged-in users)

### 🚗 **Vehicle Showcase Block**
```typescript
// Flexible vehicle display with smart filtering
vehicleShowcaseBlock: {
  title: 'Nos véhicules disponibles',
  layout: 'grid', // grid | carousel | list
  vehicleFilters: {
    centerFilter: {
      enabled: true,
      inheritFromPage: true, // Auto-detect center from URL
      centers: ['lutry', 'sion'] // Or specific centers
    },
    make: 'audi', // audi | skoda | all
    condition: 'all', // new | used | all
    featured: true,
    priceRange: { min: 20000, max: 100000 }
  },
  displayOptions: {
    itemsToShow: 12,
    showFilters: true,
    showSorting: true,
    showPagination: true
  }
}
```

**Smart Features:**
- Automatic center detection from page context
- Real-time vehicle data from AutoScout24
- Lazy loading for performance
- Responsive grid layouts
- Filter persistence in URL

### 💰 **Financing Calculator Block**
```typescript
financingCalculatorBlock: {
  title: 'Calculateur de financement',
  calculatorType: 'both', // leasing | credit | both
  defaultValues: {
    downPayment: 10000,
    termMonths: 48,
    interestRate: 2.9
  },
  integrations: {
    autoFillFromVehicle: true,
    showComparisonTable: true,
    allowBankPartnerRates: true
  }
}
```

---

## Technical Implementation Strategy

### 🔧 **Schema Extensions (No Breaking Changes)**
**Extend existing page document:**
```typescript
// Enhance existing page schema without breaking changes
{
  _type: 'page', // ✅ Keep existing
  // ✅ All existing fields remain
  
  // 🆕 Add new optional fields
  pageType: 'standard' | 'brandHub' | 'centerPage' | 'vehicleLanding',
  brandConfig: {
    brand: 'audi' | 'skoda',
    primaryColor: string,
    featuredModels: string[]
  },
  centerConfig: {
    center: reference to center,
    autoInjectContext: boolean
  }
}
```

**Add new blocks to existing page builder:**
```typescript
// src/sanity/schemas/page-builder/page-builder.ts
export const pageBuilder = defineType({
  name: 'pageBuilder',
  type: 'array',
  of: [
    // ✅ Keep all existing blocks
    { type: 'heroBlock' },
    { type: 'callToActionBlock' },
    // ... all current blocks
    
    // 🆕 Add new Autocorner blocks
    { type: 'vehicleSearchBlock' },
    { type: 'vehicleShowcaseBlock' },
    { type: 'financingCalculatorBlock' },
    { type: 'centerSelectorBlock' },
    { type: 'brandNavigationBlock' }
  ]
})
```

### 🔌 **API Integration via tRPC**
```typescript
// src/lib/trpc/routers/vehicle.ts
export const vehicleRouter = router({
  search: publicProcedure
    .input(vehicleSearchSchema)
    .query(async ({ input, ctx }) => {
      // Get seller IDs from centers if filtering by center
      const sellerIds = input.centerIds?.length 
        ? await getCenterSellerIds(input.centerIds, ctx.sanity)
        : []

      // Search AutoScout24 with seller ID filtering
      const results = await autoscoutClient.search({
        ...input,
        sellerId: sellerIds
      })

      return transformAutoscoutResults(results)
    }),

  getByCenter: publicProcedure
    .input(z.object({ centerSlug: z.string() }))
    .query(async ({ input, ctx }) => {
      const center = await ctx.sanity.fetch(
        `*[_type == "center" && slug.current == $slug][0]`,
        { slug: input.centerSlug }
      )
      
      return await autoscoutClient.search({
        sellerId: [center.autoscoutSellerId]
      })
    })
})
```

### 🎨 **Component Architecture**
**Follow existing devocracy patterns:**
```typescript
// src/components/sections/vehicle-search-block.tsx
export default function VehicleSearchBlock(props) {
  // ✅ Follow existing section component pattern
  // ✅ Use existing UI components
  // ✅ Integrate with existing styling system
}

// src/components/modules/vehicle-card.tsx  
export default function VehicleCard(props) {
  // ✅ Follow existing module component pattern
  // ✅ Use shadcn/ui components
  // ✅ Maintain consistent design system
}
```

---

## Content Strategy & SEO

### 🎯 **SEO Optimization**
**Leverage existing SEO foundation:**
- ✅ Keep existing meta tag system
- ✅ Extend structured data for vehicles
- ✅ Add automotive-specific rich snippets

**Vehicle-Specific SEO:**
```typescript
// Enhanced SEO for vehicle pages
{
  title: "Audi A4 2024 - Dès CHF 45'900 - Autocorner Lutry",
  description: "Découvrez l'Audi A4 2024 chez Autocorner Lutry. Financement disponible, garantie constructeur. Essai gratuit.",
  structuredData: {
    '@type': 'Product',
    name: 'Audi A4 2024',
    offers: {
      price: 45900,
      priceCurrency: 'CHF'
    },
    brand: 'Audi',
    seller: 'Autocorner Lutry'
  }
}
```

### 📝 **Content Management Workflow**
**Enhanced Sanity Studio experience:**
```typescript
// Custom Studio components for vehicle content
{
  title: 'Vehicles Content',
  structure: [
    listItem()
      .title('Vehicle Showcases')
      .child(
        documentList()
          .title('Pages with Vehicle Blocks')
          .filter('_type == "page" && count(pageBuilder[_type == "vehicleShowcaseBlock"]) > 0')
      ),
    listItem()
      .title('Center Management')
      .child(/* center management interface */),
    listItem()
      .title('Brand Content')
      .child(/* brand page management */)
  ]
}
```

---

## Migration & Implementation Roadmap

### 📅 **Phase 1: Foundation (Week 1)**
**Extend existing schemas without breaking changes:**
1. Add `center` document type
2. Create new vehicle blocks
3. Extend page builder with new blocks
4. Test in Sanity Studio

### 📅 **Phase 2: API Integration (Week 2)**  
**Add AutoScout24 integration:**
1. Setup AutoScout24 client
2. Create vehicle tRPC router
3. Add center-based filtering
4. Test API endpoints

### 📅 **Phase 3: Components (Week 3)**
**Build React components following existing patterns:**
1. VehicleSearchBlock component
2. VehicleShowcaseBlock component  
3. Vehicle-specific UI components
4. Integration with page builder

### 📅 **Phase 4: Pages & Routing (Week 4)**
**Create vehicle-specific pages:**
1. Vehicle search results page
2. Vehicle detail pages
3. Brand hub pages
4. Center-specific pages

### 📅 **Phase 5: Content & SEO (Week 5)**
**Content creation and optimization:**
1. Create initial center content
2. Setup brand hub pages
3. SEO optimization
4. Content migration from old site

### 📅 **Phase 6: Testing & Launch (Week 6)**
**Quality assurance and optimization:**
1. Performance testing
2. Mobile optimization
3. Analytics integration
4. Final content review

---

## Success Metrics & KPIs

### 📊 **Technical Metrics**
- **Page Load Speed:** < 3 seconds on mobile
- **Core Web Vitals:** All green scores
- **Type Safety:** 100% coverage for vehicle APIs
- **Component Reusability:** 80% shared components

### 📈 **Business Metrics**  
- **Lead Generation:** 25% increase in contact forms
- **Vehicle Views:** Track engagement per vehicle
- **Center Performance:** Vehicle views per center
- **Search Usage:** Track search patterns and conversions

### 🎯 **User Experience Metrics**
- **Search Success Rate:** % of searches leading to vehicle views
- **Conversion Funnel:** Homepage → Search → Vehicle → Contact
- **Mobile Experience:** 95%+ mobile usability score
- **Return Visitors:** Track user engagement patterns

This refined strategy leverages the solid devocracy-sanity-starter foundation while strategically adding all Autocorner-specific functionality through clean, extensible patterns that maintain the modern architecture and performance benefits.