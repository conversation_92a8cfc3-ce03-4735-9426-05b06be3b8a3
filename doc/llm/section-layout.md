# Section-Based Layout System with Grid Item Controls

## ✅ **NEW HIERARCHY (SOLID Compliant):**

### **Structure:**
```
Page Document
├── sections[] (references to Section documents)
    └── Section Document 
        ├── layoutType: 'simple' | 'grid'
        ├── gridConfig: gridConfigObject (conditional)
        ├── sectionStyling: container, padding, background
        └── content[] (references to Block documents with Grid Item overrides)
            ├── heroBlock (unchanged document) + gridItemConfig
            ├── featureCardsBlock (unchanged document) + gridItemConfig
            └── freeformBlock (unchanged document) + gridItemConfig
```

## ✅ **SOLID Principles:**

### **1. Single Responsibility:**
- **`gridConfigObject`**: Only handles grid configuration
- **`gridItemConfig`**: Only handles individual item grid properties
- **`Section`**: Only handles layout and block organization  
- **`Blocks`**: Only handle their specific content (unchanged)
- **`Page`**: Only handles page-level configuration

### **2. Open/Closed Principle:**
- **New layout types**: Easy to add to section `layoutType`
- **New grid item properties**: Easy to extend `gridItemConfig`
- **New blocks**: Just add to available blocks list
- **Existing blocks**: Completely unchanged, no modifications needed

### **3. Liskov Substitution:**
- **Layout types**: Grid and simple are interchangeable
- **Grid item configs**: Can be applied to any block type
- **Blocks**: Any block can be used in any section

### **4. Interface Segregation:**
- **Grid config**: Only appears when `layoutType = 'grid'`
- **Grid item config**: Only appears when parent section uses grid
- **Section styling**: Separated into its own object
- **Block spacing**: Only for simple layouts

### **5. Dependency Inversion:**
- **Sections depend on block references**: Not concrete implementations
- **Grid configs are reusable**: Can be used anywhere
- **Grid item configs are optional**: Blocks work with or without them
- **Layouts are configurable**: No hardcoded behavior

## ✅ **Key Benefits:**

### **🎯 Clean Separation:**
- **Blocks remain unchanged**: Your existing heroBlock, featureCardsBlock work as-is
- **Stega implementation preserved**: Block-level editing still works
- **Section adds layout**: Pure layout responsibility
- **Grid item config is optional**: Only when you need custom positioning

### **🔄 Reusability:**
- **Blocks reusable**: Same block can be used in multiple sections/pages
- **Sections reusable**: Same section can be used in multiple pages
- **Grid configs reusable**: Same grid config across different sections
- **Grid item configs flexible**: Per-block customization when needed

### **🧩 Flexibility:**
- **Any layout for any blocks**: Hero blocks in grid, feature cards in simple flow
- **Custom grid positioning**: Full-width CTAs, spanning items, custom positioning
- **Nested complexity**: portableTextBlock can still have internal grid layouts
- **Mix and match**: Different sections can have different layouts

### **📦 Easy Migration:**
- **Frontend changes minimal**: Wrap existing blocks with SectionRenderer
- **Existing blocks work**: No schema changes to blocks needed
- **Gradual migration**: Can migrate sections one by one
- **Optional grid features**: Add grid item configs only when needed

## ✅ **Enhanced Usage Examples:**

### **Hero Section (Simple Layout):**
```typescript
{
  _type: 'section',
  title: 'Hero Section',
  layoutType: 'simple',
  sectionStyling: {
    containerWidth: 'full',
    verticalPadding: 'large'
  },
  content: [
    {
      blockReference: { _ref: 'hero-block-homepage' }
      // No gridItemConfig needed for simple layout
    }
  ]
}
```

### **Features Grid Section with Custom CTA:**
```typescript
{
  _type: 'section',
  title: 'Our Features',
  displayTitle: true,
  layoutType: 'grid',
  gridConfig: {
    columns: { mobile: 1, tablet: 2, desktop: 3 },
    gap: '8',
    alignment: 'stretch'
  },
  content: [
    {
      blockReference: { _ref: 'feature-block-1' }
      // Uses default grid positioning (1 column span)
    },
    {
      blockReference: { _ref: 'feature-block-2' }
      // Uses default grid positioning
    },
    {
      blockReference: { _ref: 'feature-block-3' }
      // Uses default grid positioning
    },
    {
      blockReference: { _ref: 'cta-block-footer' },
      gridItemConfig: {
        columnSpan: { mobile: 1, tablet: 2, desktop: 3 }, // Full width!
        rowSpan: 1,
        order: { mobile: 4, tablet: 4, desktop: 4 },
        alignment: 'center'
      }
    }
  ]
}
```

### **Complex Grid Layout:**
```typescript
{
  _type: 'section',
  title: 'Magazine Layout',
  layoutType: 'grid',
  gridConfig: {
    columns: { mobile: 1, tablet: 4, desktop: 6 },
    gap: '4',
    alignment: 'start'
  },
  content: [
    {
      blockReference: { _ref: 'hero-block-featured' },
      gridItemConfig: {
        columnSpan: { mobile: 1, tablet: 4, desktop: 4 }, // Large feature
        rowSpan: 2,
        order: { mobile: 1, tablet: 1, desktop: 1 }
      }
    },
    {
      blockReference: { _ref: 'testimonial-block-1' },
      gridItemConfig: {
        columnSpan: { mobile: 1, tablet: 2, desktop: 2 },
        rowSpan: 1,
        order: { mobile: 2, tablet: 2, desktop: 2 }
      }
    },
    {
      blockReference: { _ref: 'stats-block' },
      gridItemConfig: {
        columnSpan: { mobile: 1, tablet: 2, desktop: 2 },
        rowSpan: 1,
        order: { mobile: 3, tablet: 3, desktop: 3 }
      }
    },
    {
      blockReference: { _ref: 'cta-newsletter' },
      gridItemConfig: {
        columnSpan: { mobile: 1, tablet: 4, desktop: 6 }, // Full width footer
        order: { mobile: 4, tablet: 4, desktop: 4 },
        alignment: 'center',
        customStyling: {
          backgroundColor: '#f8f9fa',
          padding: 'large'
        }
      }
    }
  ]
}
```

### **Advanced Grid Features:**
```typescript
{
  _type: 'section',
  title: 'Product Showcase',
  layoutType: 'grid',
  gridConfig: {
    columns: { mobile: 2, tablet: 4, desktop: 6 },
    gap: '6',
    alignment: 'stretch'
  },
  content: [
    {
      blockReference: { _ref: 'product-hero' },
      gridItemConfig: {
        columnSpan: { mobile: 2, tablet: 4, desktop: 3 },
        rowSpan: 2,
        startColumn: { mobile: 1, tablet: 1, desktop: 1 },
        startRow: 1
      }
    },
    {
      blockReference: { _ref: 'product-specs' },
      gridItemConfig: {
        columnSpan: { mobile: 2, tablet: 2, desktop: 3 },
        rowSpan: 1,
        startColumn: { mobile: 1, tablet: 3, desktop: 4 },
        startRow: { mobile: 'auto', tablet: 1, desktop: 1 }
      }
    },
    {
      blockReference: { _ref: 'product-reviews' },
      gridItemConfig: {
        columnSpan: { mobile: 2, tablet: 2, desktop: 3 },
        rowSpan: 1,
        startColumn: { mobile: 1, tablet: 3, desktop: 4 },
        startRow: { mobile: 'auto', tablet: 'auto', desktop: 2 }
      }
    },
    {
      blockReference: { _ref: 'product-cta' },
      gridItemConfig: {
        columnSpan: { mobile: 2, tablet: 4, desktop: 6 },
        order: 999, // Always last
        customStyling: {
          backgroundColor: '#000',
          textColor: '#fff',
          padding: 'xlarge'
        }
      }
    }
  ]
}
```

## ✅ **Schema Implementation:**

### **Grid Item Configuration Object:**
```typescript
export const gridItemConfigObject = defineType({
  name: 'gridItemConfig',
  title: 'Grid Item Configuration',
  type: 'object',
  description: 'Custom grid positioning and styling for this block',
  fields: [
    // Column spanning
    defineField({
      name: 'columnSpan',
      title: 'Column Span',
      type: 'object',
      fields: [
        { name: 'mobile', title: 'Mobile', type: 'number', validation: Rule => Rule.min(1).max(2) },
        { name: 'tablet', title: 'Tablet', type: 'number', validation: Rule => Rule.min(1).max(4) },
        { name: 'desktop', title: 'Desktop', type: 'number', validation: Rule => Rule.min(1).max(12) }
      ]
    }),
    
    // Row spanning
    defineField({
      name: 'rowSpan',
      title: 'Row Span',
      type: 'number',
      validation: Rule => Rule.min(1).max(4),
      initialValue: 1
    }),
    
    // Grid positioning
    defineField({
      name: 'startColumn',
      title: 'Start Column',
      type: 'object',
      fields: [
        { name: 'mobile', title: 'Mobile', type: 'number' },
        { name: 'tablet', title: 'Tablet', type: 'number' },
        { name: 'desktop', title: 'Desktop', type: 'number' }
      ]
    }),
    
    defineField({
      name: 'startRow',
      title: 'Start Row',
      type: 'object',
      fields: [
        { name: 'mobile', title: 'Mobile', type: 'number' },
        { name: 'tablet', title: 'Tablet', type: 'number' },
        { name: 'desktop', title: 'Desktop', type: 'number' }
      ]
    }),
    
    // Order control
    defineField({
      name: 'order',
      title: 'Display Order',
      type: 'object',
      fields: [
        { name: 'mobile', title: 'Mobile Order', type: 'number' },
        { name: 'tablet', title: 'Tablet Order', type: 'number' },
        { name: 'desktop', title: 'Desktop Order', type: 'number' }
      ]
    }),
    
    // Item-specific alignment
    defineField({
      name: 'alignment',
      title: 'Item Alignment',
      type: 'string',
      options: {
        list: [
          { title: 'Default (inherit from section)', value: 'inherit' },
          { title: 'Top', value: 'start' },
          { title: 'Center', value: 'center' },
          { title: 'Bottom', value: 'end' },
          { title: 'Stretch', value: 'stretch' }
        ]
      },
      initialValue: 'inherit'
    }),
    
    // Custom styling for this grid item
    defineField({
      name: 'customStyling',
      title: 'Custom Styling',
      type: 'object',
      fields: [
        defineField({
          name: 'backgroundColor',
          title: 'Background Color',
          type: 'color'
        }),
        defineField({
          name: 'textColor',
          title: 'Text Color',
          type: 'color'
        }),
        defineField({
          name: 'padding',
          title: 'Internal Padding',
          type: 'string',
          options: {
            list: [
              { title: 'None', value: 'none' },
              { title: 'Small', value: 'small' },
              { title: 'Medium', value: 'medium' },
              { title: 'Large', value: 'large' },
              { title: 'X-Large', value: 'xlarge' }
            ]
          }
        }),
        defineField({
          name: 'borderRadius',
          title: 'Border Radius',
          type: 'string',
          options: {
            list: [
              { title: 'None', value: 'none' },
              { title: 'Small', value: 'sm' },
              { title: 'Medium', value: 'md' },
              { title: 'Large', value: 'lg' },
              { title: 'Full', value: 'full' }
            ]
          }
        })
      ]
    })
  ]
})
```

### **Enhanced Section Content Array:**
```typescript
// In the section schema, replace simple block references with:
defineField({
  name: 'content',
  title: 'Content Blocks',
  type: 'array',
  of: [
    defineArrayMember({
      type: 'object',
      name: 'blockWithGridConfig',
      title: 'Block',
      fields: [
        defineField({
          name: 'blockReference',
          title: 'Block',
          type: 'reference',
          to: availableBlocks // All your block types
        }),
        defineField({
          name: 'gridItemConfig',
          title: 'Grid Item Configuration',
          type: 'gridItemConfig',
          hidden: ({ document }) => {
            // Only show when parent section uses grid layout
            return document?.layoutType !== 'grid'
          },
          description: 'Custom grid positioning for this block (optional)'
        })
      ],
      preview: {
        select: {
          title: 'blockReference.title',
          blockType: 'blockReference._type',
          columnSpan: 'gridItemConfig.columnSpan.desktop',
          rowSpan: 'gridItemConfig.rowSpan'
        },
        prepare({ title, blockType, columnSpan, rowSpan }) {
          const gridInfo = columnSpan || rowSpan 
            ? ` • Grid: ${columnSpan || 1}×${rowSpan || 1}`
            : ''
          
          return {
            title: title || 'Untitled Block',
            subtitle: `${blockType}${gridInfo}`
          }
        }
      }
    })
  ]
})
```

## ✅ **Frontend Implementation:**

### **Enhanced Grid Layout Component:**
```tsx
const GridLayout: React.FC<{
  config: GridConfig
  children: React.ReactNode
  className?: string
}> = ({ config, children, className }) => {
  const { columns, gap, alignment } = config

  const gridClasses = cn(
    'grid',
    `grid-cols-${columns.mobile}`,
    `sm:grid-cols-${columns.tablet}`,
    `lg:grid-cols-${columns.desktop}`,
    `gap-${gap}`,
    `items-${alignment}`,
    className
  )

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}

// Enhanced Block Wrapper with Grid Item Config
const BlockWithGridConfig: React.FC<{
  block: any
  gridItemConfig?: GridItemConfig
}> = ({ block, gridItemConfig }) => {
  if (!gridItemConfig) {
    return <BlockRenderer block={block} />
  }

  const {
    columnSpan,
    rowSpan,
    startColumn,
    startRow,
    order,
    alignment,
    customStyling
  } = gridItemConfig

  const gridItemClasses = cn(
    // Column span
    columnSpan?.mobile && `col-span-${columnSpan.mobile}`,
    columnSpan?.tablet && `sm:col-span-${columnSpan.tablet}`,
    columnSpan?.desktop && `lg:col-span-${columnSpan.desktop}`,
    
    // Row span
    rowSpan && rowSpan > 1 && `row-span-${rowSpan}`,
    
    // Start position
    startColumn?.mobile && `col-start-${startColumn.mobile}`,
    startColumn?.tablet && `sm:col-start-${startColumn.tablet}`,
    startColumn?.desktop && `lg:col-start-${startColumn.desktop}`,
    
    startRow?.mobile && `row-start-${startRow.mobile}`,
    startRow?.tablet && `sm:row-start-${startRow.tablet}`,
    startRow?.desktop && `lg:row-start-${startRow.desktop}`,
    
    // Order
    order?.mobile && `order-${order.mobile}`,
    order?.tablet && `sm:order-${order.tablet}`,
    order?.desktop && `lg:order-${order.desktop}`,
    
    // Alignment (if different from section default)
    alignment && alignment !== 'inherit' && `self-${alignment}`,
    
    // Custom styling
    customStyling?.borderRadius && `rounded-${customStyling.borderRadius}`,
    {
      'p-0': customStyling?.padding === 'none',
      'p-4': customStyling?.padding === 'small',
      'p-6': customStyling?.padding === 'medium',
      'p-8': customStyling?.padding === 'large',
      'p-12': customStyling?.padding === 'xlarge'
    }
  )

  const customStyles = {
    backgroundColor: customStyling?.backgroundColor?.hex,
    color: customStyling?.textColor?.hex
  }

  return (
    <div className={gridItemClasses} style={customStyles}>
      <BlockRenderer block={block} />
    </div>
  )
}
```

This approach gives you:
- ✅ **Ultimate flexibility**: Any block type in any layout with custom positioning
- ✅ **Clean architecture**: Clear separation of concerns with optional enhancements
- ✅ **Easy maintenance**: Blocks, sections, and grid items evolve independently  
- ✅ **Future-proof**: Easy to add new layout types, blocks, and grid features
- ✅ **Granular control**: Per-block grid customization when needed
- ✅ **Simple when simple**: Default behavior works without any configuration